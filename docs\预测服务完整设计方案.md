# 预测服务完整设计方案

## 1. 问题分析

### 当前实现的不足
1. **数据源依赖性强**：直接依赖调度系统提供的NextStopId，当调度系统不提供站点信息时无法工作
2. **预测算法简化**：使用直线距离和简单平均速度，精度不高
3. **缺少位置推断**：无法从GPS轨迹推断车辆在线路上的准确位置
4. **交通状况考虑不足**：只有简单的时间段调整因子

### 调度系统数据类型
系统需要支持两种不同的调度系统数据：
- **类型A**：只提供GPS位置数据（经纬度、速度、方向）
- **类型B**：提供GPS位置数据 + 站点信息（CurrentStopId、NextStopId）

## 2. 分层架构设计

### 第一层：数据源适配层
```
DataSourceAdapter
├── ScheduleSystemDataDetector  // 检测数据源类型
├── StationInfoExtractor        // 提取站点信息
└── GpsDataProcessor           // 处理GPS数据
```

### 第二层：位置推断层
```
LocationInferenceService
├── RouteMatchingAlgorithm     // GPS到线路匹配
├── DirectionDetector          // 行驶方向判断
├── NextStopInference          // 下一站推断
└── LocationStateManager       // 位置状态管理
```

### 第三层：预测计算层
```
PredictionCalculationService
├── RouteDistanceCalculator    // 路径距离计算
├── DynamicSpeedPredictor      // 动态速度预测
├── TrafficAdjustmentEngine    // 交通状况调整
└── ArrivalTimeCalculator      // 到站时间计算
```

### 第四层：预测优化层
```
PredictionOptimizationService
├── MultiVehicleDataFusion     // 多车辆数据融合
├── HistoricalDataLearning     // 历史数据学习
├── RealTimeFeedbackAdjustment // 实时反馈调整
└── PredictionModelUpdater     // 预测模型更新
```

## 3. 核心算法设计

### 3.1 GPS位置到线路匹配算法（Map Matching）

**目标**：将车辆GPS点精确匹配到线路路径上

**算法步骤**：
1. 计算GPS点到线路各路段的最短距离
2. 选择距离最近且方向角度差异最小的路段
3. 计算GPS点在该路段上的投影位置
4. 验证匹配结果的合理性（考虑GPS精度）

**关键参数**：
- 最大匹配距离：100米
- 最大方向角度差：45度
- GPS精度阈值：20米

### 3.2 下一站点推断算法

**情况1：有NextStopId**
- 直接使用调度系统提供的NextStopId
- 验证NextStopId的合理性（是否在线路上）

**情况2：无NextStopId**
1. 基于GPS匹配结果确定车辆在线路上的位置
2. 根据行驶方向确定线路方向（上行/下行）
3. 查找线路站点序列中的下一个站点
4. 考虑车辆是否已经过站的情况

### 3.3 路径距离计算算法

**替代直线距离的精确计算**：
1. 如果有详细线路路径数据，沿路径计算实际距离
2. 如果只有站点数据，使用站点间的标准距离
3. 考虑道路弯曲、绕行等因素的修正系数

### 3.4 动态速度预测算法

**多因素速度预测**：
```
预测速度 = 基础速度 × 时间因子 × 路段因子 × 交通因子 × 天气因子
```

- **基础速度**：车辆历史平均速度 + 当前速度的加权平均
- **时间因子**：不同时间段的速度调整（高峰期、平峰期）
- **路段因子**：不同路段的速度特征（市区、郊区、高速）
- **交通因子**：实时交通状况调整
- **天气因子**：天气对行驶速度的影响

## 4. 数据模型设计

### 4.1 新增实体

```csharp
/// <summary>
/// 线路路径点
/// </summary>
public class RoutePoint
{
    public int Id { get; set; }
    public int LineId { get; set; }
    public int SequenceNumber { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? SpeedLimit { get; set; }
    public string? RoadType { get; set; }
}

/// <summary>
/// 车辆位置状态（包含推断信息）
/// </summary>
public class VehicleLocationState
{
    public int VehicleId { get; set; }
    public DateTime Timestamp { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public int? CurrentStopId { get; set; }
    public int? NextStopId { get; set; }
    public double? DistanceToNextStop { get; set; }
    public double MatchingConfidence { get; set; }
    public bool IsInferred { get; set; } // 是否为推断结果
}

/// <summary>
/// 预测上下文
/// </summary>
public class PredictionContext
{
    public VehicleLocationState LocationState { get; set; }
    public BusLine Line { get; set; }
    public BusStop TargetStop { get; set; }
    public List<VehiclePosition> RecentTrajectory { get; set; }
    public double CurrentSpeed { get; set; }
    public double AverageSpeed { get; set; }
    public DateTime PredictionTime { get; set; }
}
```

### 4.2 新增服务接口

```csharp
/// <summary>
/// 位置推断服务
/// </summary>
public interface ILocationInferenceService
{
    Task<VehicleLocationState> InferLocationStateAsync(VehiclePosition position);
    Task<int?> InferNextStopIdAsync(int vehicleId, VehiclePosition position);
    Task<double> CalculateDistanceToStopAsync(VehiclePosition position, int stopId);
}

/// <summary>
/// 路径匹配服务
/// </summary>
public interface IRouteMatchingService
{
    Task<RouteMatchResult> MatchToRouteAsync(double latitude, double longitude, int lineId);
    Task<double> CalculateRouteDistanceAsync(int lineId, double fromLat, double fromLon, int toStopId);
}

/// <summary>
/// 预测模型服务
/// </summary>
public interface IPredictionModelService
{
    Task<double> PredictSpeedAsync(PredictionContext context);
    Task<double> CalculateTrafficFactorAsync(int lineId, int stopId, DateTime time);
    Task UpdateModelAsync(int vehicleId, int stopId, DateTime actualArrival, DateTime predictedArrival);
}
```

## 5. 实施计划

### 阶段1：基础架构完善（2-3天）
- [ ] 完善PredictionService处理NextStopId为null的情况
- [ ] 实现LocationInferenceService基础功能
- [ ] 添加基础的GPS到线路匹配算法
- [ ] 实现简单的下一站推断逻辑

### 阶段2：算法优化（3-4天）
- [ ] 实现精确的路径距离计算
- [ ] 优化速度预测算法，考虑多种因素
- [ ] 添加交通状况调整机制
- [ ] 实现预测结果缓存和优化

### 阶段3：智能化提升（4-5天）
- [ ] 实现基于历史数据的学习算法
- [ ] 添加多车辆数据融合
- [ ] 实现预测模型的自动优化
- [ ] 添加异常情况处理

## 6. 性能和质量要求

### 性能指标
- 单次预测计算时间：< 200ms
- 批量预测处理能力：> 100次/秒
- 预测精度：误差 < 2分钟（80%的情况下）
- 系统可用性：> 99.5%

### 质量保证
- 单元测试覆盖率：> 90%
- 集成测试覆盖核心场景
- 性能测试验证响应时间
- 准确性测试验证预测精度

## 7. 监控和运维

### 关键指标监控
- 预测准确率统计
- 算法执行时间监控
- 数据源质量监控
- 异常情况告警

### 运维支持
- 算法参数动态调整
- 预测模型版本管理
- 数据质量报告
- 性能优化建议
