# 路径生成与预测服务完整集成方案

## 1. 方案概述

本方案旨在解决实时公交系统中线路路径数据缺失导致的预测精度不高问题，通过基于GPS轨迹的路径生成技术，结合预测服务的全面升级，实现从数据缺失到预测精度大幅提升的完整解决方案。

### 1.1 核心目标
- **数据完整性**：为所有线路生成高质量的路径数据
- **预测精度**：将到站预测误差从当前的5-8分钟降低到2-3分钟
- **系统可靠性**：建立自动化的路径更新和质量监控机制
- **运维效率**：提供可视化的路径管理和预测监控工具

### 1.2 技术创新点
- **智能路径生成**：基于大数据聚类算法自动生成标准路径
- **多源数据融合**：同时支持有站点信息和无站点信息的调度系统
- **自适应预测**：根据实时数据动态调整预测模型参数
- **可视化管理**：提供直观的路径编辑和质量监控界面

## 2. 数据基础分析

### 2.1 现有数据资源
- **TimescaleDB中的GPS轨迹**：`vehicle_positions`表存储了大量历史轨迹数据
- **线路基础信息**：`bus_lines`表中的`RouteGeometry`字段（目前为空）
- **站点位置信息**：`bus_stops`表提供了站点的精确坐标
- **线路站点关系**：`line_stops`表定义了站点在线路中的顺序

### 2.2 数据质量评估
```sql
-- 评估线路GPS数据质量的SQL查询
SELECT 
    v.line_id,
    bl.line_number,
    bl.line_name,
    COUNT(*) as total_gps_points,
    COUNT(DISTINCT v.id) as vehicle_count,
    MIN(vp.timestamp) as earliest_data,
    MAX(vp.timestamp) as latest_data,
    AVG(vp.speed) as avg_speed
FROM vehicle_positions vp
JOIN vehicles v ON vp.vehicle_id = v.id
JOIN bus_lines bl ON v.line_id = bl.id
WHERE vp.timestamp >= NOW() - INTERVAL '30 days'
GROUP BY v.line_id, bl.line_number, bl.line_name
ORDER BY total_gps_points DESC;
```

## 3. 路径生成算法设计

### 3.1 数据预处理
1. **轨迹清洗**：去除异常GPS点（速度过快、位置跳跃等）
2. **方向分离**：区分上行和下行方向的轨迹
3. **时间过滤**：选择运营时间内的有效轨迹
4. **密度筛选**：选择GPS点密度适中的轨迹段

### 3.2 路径聚合算法
```csharp
public class RoutePathGenerator
{
    /// <summary>
    /// 基于GPS轨迹生成线路路径
    /// </summary>
    public async Task<LineString> GenerateRoutePathAsync(int lineId, int direction, 
        DateTime startDate, DateTime endDate)
    {
        // 1. 获取线路的所有车辆轨迹
        var trajectories = await GetLineTrajectories(lineId, direction, startDate, endDate);
        
        // 2. 轨迹预处理和清洗
        var cleanedTrajectories = CleanTrajectories(trajectories);
        
        // 3. 轨迹聚合生成标准路径
        var standardPath = AggregateTrajectories(cleanedTrajectories);
        
        // 4. 路径平滑和优化
        var smoothedPath = SmoothPath(standardPath);
        
        // 5. 与站点位置对齐
        var alignedPath = AlignWithStops(smoothedPath, lineId, direction);
        
        return alignedPath;
    }
    
    /// <summary>
    /// 轨迹聚合算法 - 使用密度聚类
    /// </summary>
    private LineString AggregateTrajectories(List<List<GpsPoint>> trajectories)
    {
        var pathPoints = new List<Coordinate>();
        
        // 将所有轨迹按距离分段
        var segments = DivideIntoSegments(trajectories, segmentLength: 100); // 100米一段
        
        foreach (var segment in segments)
        {
            // 对每段内的GPS点进行聚类，找到中心路径
            var centerPoint = CalculateSegmentCenter(segment);
            pathPoints.Add(new Coordinate(centerPoint.Longitude, centerPoint.Latitude));
        }
        
        return new LineString(pathPoints.ToArray());
    }
}
```

### 3.3 路径质量评估
```csharp
public class RoutePathQualityAssessment
{
    /// <summary>
    /// 评估生成路径的质量
    /// </summary>
    public RouteQualityScore AssessRouteQuality(LineString generatedPath, int lineId)
    {
        var score = new RouteQualityScore();
        
        // 1. 站点覆盖度：路径是否经过所有站点
        score.StopCoverage = CalculateStopCoverage(generatedPath, lineId);
        
        // 2. 路径连续性：是否有明显的跳跃或断裂
        score.PathContinuity = CalculatePathContinuity(generatedPath);
        
        // 3. 与实际道路的匹配度
        score.RoadMatching = CalculateRoadMatching(generatedPath);
        
        // 4. 数据支撑度：有多少GPS数据支撑这条路径
        score.DataSupport = CalculateDataSupport(generatedPath, lineId);
        
        // 综合评分
        score.OverallScore = (score.StopCoverage * 0.3 + 
                             score.PathContinuity * 0.3 + 
                             score.RoadMatching * 0.2 + 
                             score.DataSupport * 0.2);
        
        return score;
    }
}
```

## 4. 人工优化工具设计

### 4.1 可视化编辑界面
```typescript
// 前端路径编辑组件设计
interface RoutePathEditor {
    // 显示生成的路径
    displayGeneratedPath(path: LineString): void;
    
    // 显示原始GPS轨迹点
    displayGpsTrajectories(trajectories: GpsPoint[][]): void;
    
    // 显示线路站点
    displayStops(stops: BusStop[]): void;
    
    // 路径编辑功能
    enablePathEditing(): void;
    
    // 保存编辑后的路径
    saveEditedPath(path: LineString): Promise<void>;
}
```

### 4.2 编辑功能设计
1. **拖拽调整**：可以拖拽路径上的点进行微调
2. **添加路径点**：在需要的位置添加新的路径点
3. **删除路径点**：删除不合理的路径点
4. **分段编辑**：可以选择特定路段进行精细编辑
5. **撤销重做**：支持编辑操作的撤销和重做

## 5. 实施方案

### 5.1 第一阶段：自动生成（1-2天）
```csharp
// 新增服务接口
public interface IRoutePathGenerationService
{
    /// <summary>
    /// 为指定线路生成路径
    /// </summary>
    Task<RouteGenerationResult> GenerateRoutePathAsync(int lineId, 
        RouteGenerationOptions options);
    
    /// <summary>
    /// 批量生成所有线路的路径
    /// </summary>
    Task<List<RouteGenerationResult>> GenerateAllRoutePathsAsync();
    
    /// <summary>
    /// 评估路径质量
    /// </summary>
    Task<RouteQualityScore> AssessRouteQualityAsync(int lineId);
    
    /// <summary>
    /// 更新线路路径
    /// </summary>
    Task UpdateRoutePathAsync(int lineId, LineString newPath);
}
```

### 5.2 第二阶段：质量优化（2-3天）
- 实现路径质量评估算法
- 添加异常检测和自动修正
- 实现多版本路径管理
- 添加路径变更历史记录

### 5.3 第三阶段：人工编辑工具（3-4天）
- 开发Web端路径编辑界面
- 实现可视化编辑功能
- 添加编辑权限管理
- 实现编辑审核流程

## 6. 数据模型扩展

### 6.1 新增表结构
```sql
-- 路径生成历史表
CREATE TABLE route_path_generation_history (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    direction INTEGER NOT NULL,
    generated_path GEOMETRY(LINESTRING, 4326),
    generation_method VARCHAR(50), -- 'auto', 'manual', 'hybrid'
    quality_score DECIMAL(5,2),
    data_start_date DATE,
    data_end_date DATE,
    gps_points_count INTEGER,
    created_by VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);

-- 路径编辑记录表
CREATE TABLE route_path_edit_logs (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    editor_id VARCHAR(100) NOT NULL,
    edit_type VARCHAR(20), -- 'create', 'update', 'delete'
    old_path GEOMETRY(LINESTRING, 4326),
    new_path GEOMETRY(LINESTRING, 4326),
    edit_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);
```

### 6.2 配置参数表
```sql
-- 路径生成配置表
CREATE TABLE route_generation_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入默认配置
INSERT INTO route_generation_configs (config_key, config_value, description) VALUES
('segment_length_meters', '100', '路径分段长度（米）'),
('min_gps_points_per_segment', '5', '每段最少GPS点数'),
('max_speed_kmh', '80', '最大合理速度（公里/小时）'),
('min_trajectory_length_km', '2', '最短有效轨迹长度（公里）'),
('clustering_radius_meters', '20', '聚类半径（米）');
```

## 7. 使用流程

### 7.1 自动生成流程
1. **数据准备**：选择时间范围（建议30天以上的数据）
2. **质量检查**：评估GPS数据的完整性和质量
3. **自动生成**：运行路径生成算法
4. **质量评估**：自动评估生成路径的质量
5. **结果审核**：人工审核生成结果

### 7.2 人工优化流程
1. **路径预览**：在地图上查看自动生成的路径
2. **问题识别**：标识需要调整的路径段
3. **精细编辑**：使用编辑工具进行调整
4. **质量验证**：验证编辑后的路径质量
5. **保存发布**：保存并激活新的路径

## 8. 预期效果

### 8.1 预测精度提升
- **距离计算精度**：从直线距离提升到实际路径距离，精度提升30-50%
- **到站时间预测**：考虑实际路径后，预测误差减少20-30%
- **路径匹配准确性**：GPS点到路径的匹配准确率达到95%以上

### 8.2 系统功能增强
- **路径可视化**：可以在地图上显示完整的线路路径
- **轨迹分析**：可以分析车辆偏离标准路径的情况
- **运营优化**：为线路优化提供数据支撑

这个方案充分利用了现有的GPS轨迹数据，通过算法自动生成和人工优化相结合的方式，可以为所有线路生成高质量的路径数据，从而大幅提升预测服务的精度。
