-- 实时公交系统 TimescaleDB 初始化脚本
-- 创建时序数据表

-- 启用TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 1. GPS轨迹时序表
CREATE TABLE IF NOT EXISTS bus_positions (
    time TIMESTAMPTZ NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    line_id VARCHAR(50) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    speed DECIMAL(5, 2) DEFAULT 0,
    direction INTEGER DEFAULT 0, -- 方向角度 0-359
    accuracy DECIMAL(5, 2) DEFAULT 10, -- GPS精度(米)
    altitude DECIMAL(8, 2), -- 海拔高度(米)
    status VARCHAR(20) DEFAULT 'normal',
    passenger_count INTEGER DEFAULT 0,
    source_platform VARCHAR(50) NOT NULL, -- 数据来源平台
    raw_data JSONB, -- 原始数据，用于调试
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 将表转换为时序表，按时间分区，每天一个分区
SELECT create_hypertable('bus_positions', 'time', chunk_time_interval => INTERVAL '1 day');

-- 2. 车辆状态变更时序表
CREATE TABLE IF NOT EXISTS vehicle_status_history (
    time TIMESTAMPTZ NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    reason VARCHAR(100),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    source_platform VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 转换为时序表
SELECT create_hypertable('vehicle_status_history', 'time', chunk_time_interval => INTERVAL '1 day');

-- 3. 系统性能指标时序表
CREATE TABLE IF NOT EXISTS system_metrics (
    time TIMESTAMPTZ NOT NULL,
    metric_name VARCHAR(50) NOT NULL,
    metric_value DECIMAL(15, 4) NOT NULL,
    metric_unit VARCHAR(20),
    tags JSONB, -- 额外的标签信息
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 转换为时序表
SELECT create_hypertable('system_metrics', 'time', chunk_time_interval => INTERVAL '1 hour');

-- 4. API调用统计时序表
CREATE TABLE IF NOT EXISTS api_call_stats (
    time TIMESTAMPTZ NOT NULL,
    endpoint VARCHAR(100) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER NOT NULL,
    user_agent VARCHAR(200),
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 转换为时序表
SELECT create_hypertable('api_call_stats', 'time', chunk_time_interval => INTERVAL '1 hour');

-- 创建索引

-- GPS轨迹表索引
CREATE INDEX IF NOT EXISTS idx_bus_positions_vehicle_time ON bus_positions (vehicle_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_bus_positions_line_time ON bus_positions (line_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_bus_positions_source ON bus_positions (source_platform, time DESC);
CREATE INDEX IF NOT EXISTS idx_bus_positions_status ON bus_positions (status, time DESC);

-- 车辆状态历史索引
CREATE INDEX IF NOT EXISTS idx_vehicle_status_history_vehicle_time ON vehicle_status_history (vehicle_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_vehicle_status_history_status ON vehicle_status_history (new_status, time DESC);

-- 系统指标索引
CREATE INDEX IF NOT EXISTS idx_system_metrics_name_time ON system_metrics (metric_name, time DESC);
CREATE INDEX IF NOT EXISTS idx_system_metrics_tags ON system_metrics USING GIN (tags);

-- API统计索引
CREATE INDEX IF NOT EXISTS idx_api_call_stats_endpoint_time ON api_call_stats (endpoint, time DESC);
CREATE INDEX IF NOT EXISTS idx_api_call_stats_status ON api_call_stats (status_code, time DESC);

-- 创建连续聚合视图（实时物化视图）

-- 1. 每分钟车辆位置聚合
CREATE MATERIALIZED VIEW IF NOT EXISTS bus_positions_1min
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 minute', time) AS bucket,
    vehicle_id,
    line_id,
    AVG(latitude) as avg_latitude,
    AVG(longitude) as avg_longitude,
    AVG(speed) as avg_speed,
    COUNT(*) as position_count,
    MAX(time) as last_update
FROM bus_positions
GROUP BY bucket, vehicle_id, line_id;

-- 2. 每小时API调用统计
CREATE MATERIALIZED VIEW IF NOT EXISTS api_stats_1hour
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS bucket,
    endpoint,
    method,
    status_code,
    COUNT(*) as call_count,
    AVG(response_time_ms) as avg_response_time,
    MAX(response_time_ms) as max_response_time,
    MIN(response_time_ms) as min_response_time
FROM api_call_stats
GROUP BY bucket, endpoint, method, status_code;

-- 3. 每小时系统指标聚合
CREATE MATERIALIZED VIEW IF NOT EXISTS system_metrics_1hour
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', time) AS bucket,
    metric_name,
    AVG(metric_value) as avg_value,
    MAX(metric_value) as max_value,
    MIN(metric_value) as min_value,
    COUNT(*) as sample_count
FROM system_metrics
GROUP BY bucket, metric_name;

-- 设置连续聚合刷新策略
SELECT add_continuous_aggregate_policy('bus_positions_1min',
    start_offset => INTERVAL '1 hour',
    end_offset => INTERVAL '1 minute',
    schedule_interval => INTERVAL '1 minute');

SELECT add_continuous_aggregate_policy('api_stats_1hour',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

SELECT add_continuous_aggregate_policy('system_metrics_1hour',
    start_offset => INTERVAL '1 day',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');

-- 设置数据保留策略

-- GPS原始数据保留90天（延长保留期用于分析）
SELECT add_retention_policy('bus_positions', INTERVAL '90 days');

-- 车辆状态历史保留180天
SELECT add_retention_policy('vehicle_status_history', INTERVAL '180 days');

-- API调用统计保留30天（延长用于性能分析）
SELECT add_retention_policy('api_call_stats', INTERVAL '30 days');

-- 系统指标保留60天
SELECT add_retention_policy('system_metrics', INTERVAL '60 days');

-- 聚合视图保留更长时间
SELECT add_retention_policy('bus_positions_1min', INTERVAL '90 days');
SELECT add_retention_policy('api_stats_1hour', INTERVAL '1 year');
SELECT add_retention_policy('system_metrics_1hour', INTERVAL '1 year');

-- 创建压缩策略（节省存储空间）
ALTER TABLE bus_positions SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'vehicle_id, line_id',
    timescaledb.compress_orderby = 'time DESC'
);

ALTER TABLE vehicle_status_history SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'vehicle_id',
    timescaledb.compress_orderby = 'time DESC'
);

-- 设置自动压缩策略（数据超过1天后压缩）
SELECT add_compression_policy('bus_positions', INTERVAL '1 day');
SELECT add_compression_policy('vehicle_status_history', INTERVAL '1 day');

-- 创建有用的函数

-- 获取车辆最新位置
CREATE OR REPLACE FUNCTION get_latest_vehicle_position(p_vehicle_id VARCHAR)
RETURNS TABLE(
    vehicle_id VARCHAR,
    latitude DECIMAL,
    longitude DECIMAL,
    speed DECIMAL,
    direction INTEGER,
    last_update TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bp.vehicle_id,
        bp.latitude,
        bp.longitude,
        bp.speed,
        bp.direction,
        bp.time as last_update
    FROM bus_positions bp
    WHERE bp.vehicle_id = p_vehicle_id
    ORDER BY bp.time DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 获取线路上所有车辆的最新位置
CREATE OR REPLACE FUNCTION get_line_vehicles_latest_positions(p_line_id VARCHAR)
RETURNS TABLE(
    vehicle_id VARCHAR,
    latitude DECIMAL,
    longitude DECIMAL,
    speed DECIMAL,
    direction INTEGER,
    last_update TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT ON (bp.vehicle_id)
        bp.vehicle_id,
        bp.latitude,
        bp.longitude,
        bp.speed,
        bp.direction,
        bp.time as last_update
    FROM bus_positions bp
    WHERE bp.line_id = p_line_id
        AND bp.time > NOW() - INTERVAL '1 hour'
    ORDER BY bp.vehicle_id, bp.time DESC;
END;
$$ LANGUAGE plpgsql;

-- 完成初始化
SELECT 'TimescaleDB initialized successfully' as status;
