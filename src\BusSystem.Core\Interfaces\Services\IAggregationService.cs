using BusSystem.Shared.Enums;

namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// 聚合服务接口 - 负责聚合多个数据源的信息
/// </summary>
public interface IAggregationService
{
    /// <summary>
    /// 获取附近站点及实时信息（首页聚合接口）
    /// </summary>
    Task<object> GetNearbyStopsWithRealtimeInfoAsync(double longitude, double latitude, double radiusMeters = 500, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02, bool mergeSameNameStops = true);

    /// <summary>
    /// 获取站点详细信息及到站预测
    /// </summary>
    Task<object> GetStopDetailWithPredictionsAsync(int stopId, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02);

    /// <summary>
    /// 搜索站点和线路（首页搜索功能）
    /// </summary>
    Task<object> SearchStopsAndLinesAsync(string keyword, double? longitude = null, double? latitude = null, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02);

    /// <summary>
    /// 获取单个站点的到站预测
    /// </summary>
    Task<object> GetSingleStopPredictionsAsync(int stopId, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02);

    /// <summary>
    /// 获取可切换的线路方向列表（同线路编号的不同方向）
    /// </summary>
    Task<object> GetAlternativeLinesAsync(int lineId);
}
