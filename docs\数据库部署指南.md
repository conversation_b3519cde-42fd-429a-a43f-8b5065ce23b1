# 数据库部署指南

## 📋 概述

本指南详细说明了实时公交系统数据库的部署步骤，包括PostgreSQL、PostGIS、TimescaleDB的安装配置，以及所有数据表的创建。

## 🗄️ 数据库架构

### 核心组件
- **PostgreSQL 15+**: 主数据库
- **PostGIS 3.3+**: 地理空间扩展
- **TimescaleDB 2.11+**: 时序数据扩展
- **Redis 7.0+**: 缓存和实时数据

### 数据库结构
```
实时公交数据库
├── 基础数据表 (PostgreSQL)
│   ├── bus_lines (公交线路)
│   ├── bus_stops (公交站点)
│   ├── vehicles (车辆信息)
│   └── line_stops (线路站点关系)
├── 业务数据表 (PostgreSQL)
│   ├── arrival_predictions_history (到站预测历史)
│   ├── user_locations (用户位置记录)
│   ├── user_favorites (用户收藏)
│   ├── user_search_history (搜索历史)
│   ├── search_behavior_stats (搜索统计)
│   ├── user_feedback (用户反馈)
│   └── system_notifications (系统通知)
├── 系统数据表 (PostgreSQL)
│   ├── system_configs (系统配置)
│   ├── data_sync_logs (数据同步日志)
│   ├── api_access_logs (API访问日志)
│   ├── system_error_logs (系统错误日志)
│   ├── system_performance_metrics (性能监控)
│   ├── data_source_configs (数据源配置)
│   └── system_scheduled_tasks (定时任务)
└── 时序数据表 (TimescaleDB)
    ├── vehicle_positions (车辆位置轨迹)
    ├── vehicle_status_history (车辆状态历史)
    ├── api_call_stats (API调用统计)
    └── system_metrics (系统指标)
```

## 🚀 部署步骤

### 第一步：环境准备

#### 1.1 安装PostgreSQL 15
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-15 postgresql-contrib-15

# CentOS/RHEL
sudo dnf install postgresql15-server postgresql15-contrib
sudo postgresql-15-setup initdb
sudo systemctl enable postgresql-15
sudo systemctl start postgresql-15
```

#### 1.2 安装PostGIS
```bash
# Ubuntu/Debian
sudo apt install postgresql-15-postgis-3

# CentOS/RHEL
sudo dnf install postgis33_15
```

#### 1.3 安装TimescaleDB
```bash
# Ubuntu/Debian
echo "deb https://packagecloud.io/timescale/timescaledb/ubuntu/ $(lsb_release -c -s) main" | sudo tee /etc/apt/sources.list.d/timescaledb.list
wget --quiet -O - https://packagecloud.io/timescale/timescaledb/gpgkey | sudo apt-key add -
sudo apt update
sudo apt install timescaledb-2-postgresql-15

# CentOS/RHEL
sudo tee /etc/yum.repos.d/timescale_timescaledb.repo <<EOL
[timescale_timescaledb]
name=timescale_timescaledb
baseurl=https://packagecloud.io/timescale/timescaledb/el/8/\$basearch
repo_gpgcheck=1
gpgcheck=0
enabled=1
gpgkey=https://packagecloud.io/timescale/timescaledb/gpgkey
sslverify=1
sslcacert=/etc/pki/tls/certs/ca-bundle.crt
metadata_expire=300
EOL
sudo dnf install timescaledb-2-postgresql-15
```

### 第二步：数据库配置

#### 2.1 配置PostgreSQL
```bash
# 编辑postgresql.conf
sudo nano /etc/postgresql/15/main/postgresql.conf

# 添加或修改以下配置
shared_preload_libraries = 'timescaledb,postgis-3'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

#### 2.2 配置pg_hba.conf
```bash
sudo nano /etc/postgresql/15/main/pg_hba.conf

# 添加应用连接权限
host    bus_system    bus_user    127.0.0.1/32    md5
host    bus_system    bus_user    ::1/128         md5
```

#### 2.3 重启PostgreSQL
```bash
sudo systemctl restart postgresql
```

### 第三步：创建数据库和用户

```sql
-- 连接到PostgreSQL
sudo -u postgres psql

-- 创建数据库用户
CREATE USER bus_user WITH PASSWORD 'your_secure_password';

-- 创建数据库
CREATE DATABASE bus_system OWNER bus_user;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE bus_system TO bus_user;

-- 连接到业务数据库
\c bus_system

-- 启用扩展
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 授予用户权限
GRANT ALL ON SCHEMA public TO bus_user;
GRANT ALL ON ALL TABLES IN SCHEMA public TO bus_user;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO bus_user;
```

### 第四步：执行数据库脚本

#### 4.1 创建基础数据表
```bash
psql -h localhost -U bus_user -d bus_system -f scripts/create-tables.sql
```

#### 4.2 创建业务数据表
```bash
psql -h localhost -U bus_user -d bus_system -f scripts/create-business-tables.sql
```

#### 4.3 创建系统数据表
```bash
psql -h localhost -U bus_user -d bus_system -f scripts/create-system-tables.sql
```

#### 4.4 初始化TimescaleDB
```bash
psql -h localhost -U bus_user -d bus_system -f scripts/init-timescaledb.sql
```

#### 4.5 插入初始数据
```bash
psql -h localhost -U bus_user -d bus_system -f scripts/insert-sample-data.sql
```

### 第五步：验证部署

#### 5.1 检查扩展
```sql
SELECT * FROM pg_extension;
```

#### 5.2 检查超表
```sql
SELECT * FROM timescaledb_information.hypertables;
```

#### 5.3 检查数据表
```sql
\dt
```

#### 5.4 检查空间索引
```sql
SELECT schemaname, tablename, indexname 
FROM pg_indexes 
WHERE indexdef LIKE '%GIST%';
```

## 🔧 性能优化

### 索引优化
- 所有时序表按时间字段创建索引
- 地理位置字段使用GIST空间索引
- 外键关系创建适当索引
- 查询频繁的字段组合创建复合索引

### TimescaleDB优化
- **数据保留策略**: GPS数据保留90天，日志数据保留30天
- **压缩策略**: 7天后自动压缩时序数据
- **分区策略**: 按天分区，提升查询性能

### 连接池配置
```bash
# 安装pgbouncer
sudo apt install pgbouncer

# 配置连接池
sudo nano /etc/pgbouncer/pgbouncer.ini

[databases]
bus_system = host=localhost port=5432 dbname=bus_system

[pgbouncer]
pool_mode = transaction
max_client_conn = 100
default_pool_size = 20
```

## 📊 监控和维护

### 定期维护任务
1. **数据清理**: 定期清理过期的GPS轨迹和日志数据
2. **索引维护**: 定期重建和分析索引
3. **统计信息更新**: 定期更新表统计信息
4. **备份策略**: 每日增量备份，每周全量备份

### 监控指标
- 数据库连接数
- 查询响应时间
- 磁盘使用率
- 缓存命中率
- 时序数据压缩率

## 🔒 安全配置

### 数据库安全
- 使用强密码
- 限制网络访问
- 启用SSL连接
- 定期更新密码
- 审计日志记录

### 备份策略
```bash
# 创建备份脚本
#!/bin/bash
BACKUP_DIR="/var/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)

# 全量备份
pg_dump -h localhost -U bus_user -d bus_system > $BACKUP_DIR/bus_system_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/bus_system_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

## 🚨 故障排除

### 常见问题
1. **连接失败**: 检查pg_hba.conf配置和防火墙设置
2. **扩展加载失败**: 检查shared_preload_libraries配置
3. **空间查询慢**: 检查PostGIS索引是否正确创建
4. **时序数据查询慢**: 检查TimescaleDB分区和压缩策略

### 日志分析
```bash
# 查看PostgreSQL日志
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

---

**文档版本**: v1.0  
**更新时间**: 2025-08-22  
**适用版本**: PostgreSQL 15+, PostGIS 3.3+, TimescaleDB 2.11+
