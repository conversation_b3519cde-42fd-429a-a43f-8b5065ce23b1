using Microsoft.Extensions.Logging;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Services;

/// <summary>
/// 线路服务实现
/// </summary>
public class LineService : ILineService
{
    private readonly IBusLineRepository _lineRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILogger<LineService> _logger;

    public LineService(
        IBusLineRepository lineRepository,
        IVehicleRepository vehicleRepository,
        ILogger<LineService> logger)
    {
        _lineRepository = lineRepository;
        _vehicleRepository = vehicleRepository;
        _logger = logger;
    }

    public async Task<BusLine?> GetLineAsync(int lineId)
    {
        try
        {
            return await _lineRepository.GetByIdAsync(lineId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路失败，线路ID: {LineId}", lineId);
            throw;
        }
    }

    public async Task<BusLine?> GetLineByNumberAsync(string lineNumber, int? direction = null)
    {
        try
        {
            if (direction.HasValue)
            {
                return await _lineRepository.GetByLineNumberAndDirectionAsync(lineNumber, direction.Value);
            }
            return await _lineRepository.GetByLineNumberAsync(lineNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据线路编号获取线路失败，线路编号: {LineNumber}, 方向: {Direction}", 
                lineNumber, direction);
            throw;
        }
    }

    public async Task<BusLine?> GetLineWithStopsAsync(int lineId)
    {
        try
        {
            return await _lineRepository.GetWithStopsAsync(lineId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路及站点信息失败，线路ID: {LineId}", lineId);
            throw;
        }
    }

    public async Task<IEnumerable<BusLine>> SearchLinesAsync(string keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return await _lineRepository.GetActiveAsync();
            }
            return await _lineRepository.SearchAsync(keyword.Trim());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索线路失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<IEnumerable<BusLine>> GetActiveLinesAsync()
    {
        try
        {
            return await _lineRepository.GetActiveAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取活跃线路失败");
            throw;
        }
    }

    public async Task<PagedResult<BusLine>> GetLinesPagedAsync(int pageNumber, int pageSize, string? keyword = null)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                return await _lineRepository.GetPagedAsync(
                    pageNumber, pageSize,
                    x => x.LineNumber.Contains(keyword) || 
                         x.LineName.Contains(keyword) ||
                         x.StartStopName.Contains(keyword) ||
                         x.EndStopName.Contains(keyword),
                    x => x.LineNumber);
            }
            
            return await _lineRepository.GetPagedAsync(
                pageNumber, pageSize,
                orderBy: x => x.LineNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取线路失败，页码: {PageNumber}, 页大小: {PageSize}, 关键词: {Keyword}", 
                pageNumber, pageSize, keyword);
            throw;
        }
    }

    public async Task<BusLine> CreateLineAsync(BusLine line)
    {
        try
        {
            // 验证线路编号是否已存在
            var existingLine = await _lineRepository.GetByLineNumberAndDirectionAsync(
                line.LineNumber, line.Direction);
            if (existingLine != null)
            {
                throw new InvalidOperationException($"线路编号 {line.LineNumber} 方向 {line.Direction} 已存在");
            }

            var result = await _lineRepository.AddAsync(line);
            _logger.LogInformation("创建线路成功，线路ID: {LineId}, 线路编号: {LineNumber}", 
                result.Id, result.LineNumber);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建线路失败，线路编号: {LineNumber}", line.LineNumber);
            throw;
        }
    }

    public async Task<BusLine> UpdateLineAsync(BusLine line)
    {
        try
        {
            var existingLine = await _lineRepository.GetByIdAsync(line.Id);
            if (existingLine == null)
            {
                throw new InvalidOperationException($"线路不存在，ID: {line.Id}");
            }

            var result = await _lineRepository.UpdateAsync(line);
            _logger.LogInformation("更新线路成功，线路ID: {LineId}, 线路编号: {LineNumber}", 
                result.Id, result.LineNumber);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新线路失败，线路ID: {LineId}", line.Id);
            throw;
        }
    }

    public async Task DeleteLineAsync(int lineId)
    {
        try
        {
            var line = await _lineRepository.GetByIdAsync(lineId);
            if (line == null)
            {
                throw new InvalidOperationException($"线路不存在，ID: {lineId}");
            }

            // 检查是否有关联的车辆
            var vehicles = await _vehicleRepository.GetByLineIdAsync(lineId);
            if (vehicles.Any())
            {
                throw new InvalidOperationException("无法删除线路，存在关联的车辆");
            }

            await _lineRepository.DeleteAsync(line);
            _logger.LogInformation("删除线路成功，线路ID: {LineId}, 线路编号: {LineNumber}", 
                lineId, line.LineNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除线路失败，线路ID: {LineId}", lineId);
            throw;
        }
    }

    public async Task<object> GetLineStatsAsync(int lineId)
    {
        try
        {
            var line = await _lineRepository.GetWithStopsAsync(lineId);
            if (line == null)
            {
                throw new InvalidOperationException($"线路不存在，ID: {lineId}");
            }

            var vehicles = await _vehicleRepository.GetByLineIdAsync(lineId);
            var activeVehicles = vehicles.Where(v => v.Status == 1);

            return new
            {
                LineId = lineId,
                LineNumber = line.LineNumber,
                LineName = line.LineName,
                TotalStops = line.LineStops?.Count ?? 0,
                TotalVehicles = vehicles.Count(),
                ActiveVehicles = activeVehicles.Count(),
                TotalDistance = line.TotalDistance,
                OperationTime = new
                {
                    StartTime = line.OperationStartTime,
                    EndTime = line.OperationEndTime
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路统计信息失败，线路ID: {LineId}", lineId);
            throw;
        }
    }
}
