using NetTopologySuite.Geometries;

namespace BusSystem.Core.Entities;

/// <summary>
/// 公交线路实体
/// </summary>
public class BusLine : BaseEntity
{
    /// <summary>
    /// 外部系统线路ID（用于数据同步）
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// 线路编号
    /// </summary>
    public string LineNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// 线路名称
    /// </summary>
    public string LineName { get; set; } = string.Empty;
    
    /// <summary>
    /// 起点站名称
    /// </summary>
    public string StartStopName { get; set; } = string.Empty;
    
    /// <summary>
    /// 终点站名称
    /// </summary>
    public string EndStopName { get; set; } = string.Empty;
    
    /// <summary>
    /// 方向：0-上行，1-下行
    /// </summary>
    public int Direction { get; set; }
    
    /// <summary>
    /// 线路几何形状（PostGIS）
    /// </summary>
    public LineString? RouteGeometry { get; set; }
    
    /// <summary>
    /// 运营开始时间
    /// </summary>
    public TimeSpan? OperationStartTime { get; set; }
    
    /// <summary>
    /// 运营结束时间
    /// </summary>
    public TimeSpan? OperationEndTime { get; set; }

    /// <summary>
    /// 运营时间描述（用于数据同步）
    /// </summary>
    public string? OperationTime { get; set; }
    
    /// <summary>
    /// 高峰期发车间隔（分钟）
    /// </summary>
    public int? PeakInterval { get; set; }
    
    /// <summary>
    /// 平峰期发车间隔（分钟）
    /// </summary>
    public int? NormalInterval { get; set; }
    
    /// <summary>
    /// 票价
    /// </summary>
    public decimal? TicketPrice { get; set; }
    
    /// <summary>
    /// 总距离（公里）
    /// </summary>
    public decimal? TotalDistance { get; set; }
    
    /// <summary>
    /// 总站点数
    /// </summary>
    public int? TotalStops { get; set; }
    
    /// <summary>
    /// 运营公司ID
    /// </summary>
    public int? CompanyId { get; set; }
    
    /// <summary>
    /// 状态：0-停运，1-正常，2-部分停运
    /// </summary>
    public int Status { get; set; } = 1;
    
    /// <summary>
    /// 线路站点关联
    /// </summary>
    public virtual ICollection<LineStop> LineStops { get; set; } = new List<LineStop>();
    
    /// <summary>
    /// 线路车辆关联
    /// </summary>
    public virtual ICollection<Vehicle> Vehicles { get; set; } = new List<Vehicle>();
}
