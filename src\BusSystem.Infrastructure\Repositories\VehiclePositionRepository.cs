using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// 车辆位置Repository实现（时序数据）
/// </summary>
public class VehiclePositionRepository : IVehiclePositionRepository
{
    private readonly TimescaleDbContext _context;

    public VehiclePositionRepository(TimescaleDbContext context)
    {
        _context = context;
    }

    public async Task<VehiclePosition> AddAsync(VehiclePosition position)
    {
        await _context.VehiclePositions.AddAsync(position);
        await _context.SaveChangesAsync();
        return position;
    }

    public async Task<IEnumerable<VehiclePosition>> AddRangeAsync(IEnumerable<VehiclePosition> positions)
    {
        var positionList = positions.ToList();
        await _context.VehiclePositions.AddRangeAsync(positionList);
        await _context.SaveChangesAsync();
        return positionList;
    }

    public async Task<VehiclePosition?> GetLatestPositionAsync(int vehicleId)
    {
        return await _context.VehiclePositions
            .Where(p => p.VehicleId == vehicleId)
            .OrderByDescending(p => p.Timestamp)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<VehiclePosition>> GetLatestPositionsAsync(IEnumerable<int> vehicleIds)
    {
        var vehicleIdList = vehicleIds.ToList();
        var latestPositions = new List<VehiclePosition>();

        foreach (var vehicleId in vehicleIdList)
        {
            var position = await GetLatestPositionAsync(vehicleId);
            if (position != null)
            {
                latestPositions.Add(position);
            }
        }

        return latestPositions;
    }

    public async Task<IEnumerable<VehiclePosition>> GetLineVehiclePositionsAsync(int lineId)
    {
        // 使用原生SQL查询获取线路上所有车辆的最新位置
        var sql = @"
            SELECT DISTINCT ON (vp.vehicle_id) vp.*
            FROM vehicle_positions vp
            INNER JOIN vehicles v ON vp.vehicle_id = v.id
            WHERE v.line_id = {0}
            ORDER BY vp.vehicle_id, vp.timestamp DESC";

        return await _context.VehiclePositions
            .FromSqlRaw(sql, lineId)
            .ToListAsync();
    }

    public async Task<IEnumerable<VehiclePosition>> GetVehicleTrajectoryAsync(int vehicleId, DateTime startTime, DateTime endTime)
    {
        return await _context.VehiclePositions
            .Where(p => p.VehicleId == vehicleId && 
                       p.Timestamp >= startTime && 
                       p.Timestamp <= endTime)
            .OrderBy(p => p.Timestamp)
            .ToListAsync();
    }

    public async Task<IEnumerable<VehiclePosition>> GetNearbyVehiclesAsync(double longitude, double latitude, double radiusMeters)
    {
        // 使用简单的距离计算（实际应用中可以使用更精确的地理计算）
        var latRange = radiusMeters / 111000.0; // 大约1度纬度 = 111km
        var lonRange = radiusMeters / (111000.0 * Math.Cos(latitude * Math.PI / 180));

        var minLat = latitude - latRange;
        var maxLat = latitude + latRange;
        var minLon = longitude - lonRange;
        var maxLon = longitude + lonRange;

        // 获取每个车辆的最新位置
        var sql = @"
            SELECT DISTINCT ON (vehicle_id) *
            FROM vehicle_positions
            WHERE latitude BETWEEN {0} AND {1}
              AND longitude BETWEEN {2} AND {3}
              AND timestamp > NOW() - INTERVAL '10 minutes'
            ORDER BY vehicle_id, timestamp DESC";

        return await _context.VehiclePositions
            .FromSqlRaw(sql, minLat, maxLat, minLon, maxLon)
            .ToListAsync();
    }

    public async Task DeleteOldDataAsync(DateTime beforeTime)
    {
        var oldPositions = _context.VehiclePositions
            .Where(p => p.Timestamp < beforeTime);

        _context.VehiclePositions.RemoveRange(oldPositions);
        await _context.SaveChangesAsync();
    }

    public async Task<object> GetPositionStatsAsync(int vehicleId, DateTime date)
    {
        var startOfDay = date.Date;
        var endOfDay = startOfDay.AddDays(1);

        var positions = await _context.VehiclePositions
            .Where(p => p.VehicleId == vehicleId && 
                       p.Timestamp >= startOfDay && 
                       p.Timestamp < endOfDay)
            .OrderBy(p => p.Timestamp)
            .ToListAsync();

        if (!positions.Any())
        {
            return new
            {
                VehicleId = vehicleId,
                Date = date.ToString("yyyy-MM-dd"),
                TotalRecords = 0,
                TotalDistance = 0.0,
                AverageSpeed = 0.0,
                MaxSpeed = 0.0
            };
        }

        var totalDistance = 0.0;
        for (int i = 1; i < positions.Count; i++)
        {
            var prev = positions[i - 1];
            var curr = positions[i];
            
            // 简单的距离计算（实际应用中应使用更精确的地理计算）
            var latDiff = curr.Latitude - prev.Latitude;
            var lonDiff = curr.Longitude - prev.Longitude;
            var distance = Math.Sqrt(latDiff * latDiff + lonDiff * lonDiff) * 111000; // 转换为米
            totalDistance += distance;
        }

        var speeds = positions.Where(p => p.Speed.HasValue).Select(p => p.Speed!.Value);
        var averageSpeed = speeds.Any() ? speeds.Average() : 0;
        var maxSpeed = speeds.Any() ? speeds.Max() : 0;

        return new
        {
            VehicleId = vehicleId,
            Date = date.ToString("yyyy-MM-dd"),
            TotalRecords = positions.Count,
            TotalDistance = Math.Round(totalDistance / 1000, 2), // 转换为公里
            AverageSpeed = Math.Round(averageSpeed, 2),
            MaxSpeed = Math.Round(maxSpeed, 2),
            FirstRecord = positions.First().Timestamp,
            LastRecord = positions.Last().Timestamp
        };
    }
}
