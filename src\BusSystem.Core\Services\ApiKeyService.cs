using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;

namespace BusSystem.Core.Services;

/// <summary>
/// API密钥管理服务实现
/// </summary>
public class ApiKeyService : IApiKeyService
{
    private readonly IApiKeyRepository _apiKeyRepository;
    private readonly ILogger<ApiKeyService> _logger;
    private const int CacheExpirationMinutes = 15;

    public ApiKeyService(
        IApiKeyRepository apiKeyRepository,
        ILogger<ApiKeyService> logger)
    {
        _apiKeyRepository = apiKeyRepository;
        _logger = logger;
    }

    public async Task<ApiKeyInfo?> ValidateApiKeyAsync(string apiKey)
    {
        if (string.IsNullOrWhiteSpace(apiKey))
        {
            return null;
        }

        try
        {
            var apiKeyHash = ComputeHash(apiKey);

            // 从数据库获取
            var apiKeyInfo = await _apiKeyRepository.GetByHashAsync(apiKeyHash);

            return apiKeyInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证API密钥时发生异常");
            return null;
        }
    }

    public async Task<bool> HasPermissionAsync(int apiKeyId, string endpoint, string httpMethod)
    {
        try
        {
            // 获取权限列表
            var permissions = (await _apiKeyRepository.GetPermissionsAsync(apiKeyId)).ToList();

            // 检查权限匹配
            return permissions?.Any(p => IsPermissionMatch(ConvertToApiPermission(p), endpoint, httpMethod)) ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查API权限时发生异常，ApiKeyId: {ApiKeyId}, Endpoint: {Endpoint}", 
                apiKeyId, endpoint);
            return false;
        }
    }

    public async Task<RateLimitResult> CheckRateLimitAsync(int apiKeyId)
    {
        try
        {
            var apiKeyInfo = await _apiKeyRepository.GetByIdAsync(apiKeyId);
            if (apiKeyInfo == null)
            {
                return new RateLimitResult
                {
                    IsAllowed = false,
                    Message = "API密钥不存在"
                };
            }

            var currentHour = DateTime.UtcNow.Date.AddHours(DateTime.UtcNow.Hour);
            var currentCount = await _apiKeyRepository.GetHourlyRequestCountAsync(apiKeyId, currentHour);

            var isAllowed = currentCount < apiKeyInfo.RateLimitPerHour;
            var resetTime = currentHour.AddHours(1) - DateTime.UtcNow;

            return new RateLimitResult
            {
                IsAllowed = isAllowed,
                CurrentCount = currentCount,
                Limit = apiKeyInfo.RateLimitPerHour,
                ResetTime = resetTime,
                Message = isAllowed ? "允许访问" : $"超出每小时限制 {apiKeyInfo.RateLimitPerHour} 次"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查速率限制时发生异常，ApiKeyId: {ApiKeyId}", apiKeyId);
            return new RateLimitResult
            {
                IsAllowed = false,
                Message = "检查速率限制时发生错误"
            };
        }
    }

    public async Task LogApiAccessAsync(ApiAccessLog accessLog)
    {
        try
        {
            await _apiKeyRepository.LogAccessAsync(accessLog);
            
            // 更新速率限制计数
            if (accessLog.ApiKeyId.HasValue)
            {
                await _apiKeyRepository.IncrementRequestCountAsync(accessLog.ApiKeyId.Value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录API访问日志时发生异常");
            // 不抛出异常，避免影响正常业务流程
        }
    }

    public async Task UpdateApiKeyUsageAsync(int apiKeyId)
    {
        try
        {
            await _apiKeyRepository.UpdateLastUsedAsync(apiKeyId, DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新API密钥使用信息时发生异常，ApiKeyId: {ApiKeyId}", apiKeyId);
            // 不抛出异常，避免影响正常业务流程
        }
    }

    public async Task<IEnumerable<ApiPermission>> GetApiKeyPermissionsAsync(int apiKeyId)
    {
        try
        {
            var entityPermissions = await _apiKeyRepository.GetPermissionsAsync(apiKeyId);
            return entityPermissions.Select(ConvertToApiPermission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取API密钥权限时发生异常，ApiKeyId: {ApiKeyId}", apiKeyId);
            return Enumerable.Empty<ApiPermission>();
        }
    }

    public async Task<CreateApiKeyResult> CreateApiKeyAsync(CreateApiKeyRequest request)
    {
        try
        {
            // 生成API密钥
            var apiKey = GenerateApiKey();
            var apiKeyHash = ComputeHash(apiKey);

            var apiKeyInfo = new ApiKeyInfo
            {
                KeyName = request.KeyName,
                KeyType = request.KeyType,
                Description = request.Description,
                OwnerName = request.OwnerName,
                IsActive = true,
                RateLimitPerHour = request.RateLimitPerHour,
                RateLimitPerDay = request.RateLimitPerDay,
                AllowedIps = request.AllowedIps,
                ExpiresAt = request.ExpiresAt,
                CreatedAt = DateTime.UtcNow
            };

            var createdApiKey = await _apiKeyRepository.CreateAsync(apiKeyInfo, apiKeyHash);

            // 设置权限
            if (request.PermissionNames.Any())
            {
                await _apiKeyRepository.SetPermissionsAsync(createdApiKey.Id, request.PermissionNames);
            }

            // 记录API密钥创建日志（包含创建者信息）
            _logger.LogInformation("创建API密钥成功: {KeyName}, 类型: {KeyType}, 创建者: {CreatedBy}, API密钥ID: {ApiKeyId}",
                request.KeyName, request.KeyType, request.CreatedBy, createdApiKey.Id);

            // 获取完整的权限信息
            createdApiKey.Permissions = (await GetApiKeyPermissionsAsync(createdApiKey.Id)).ToList();

            // 安全返回：只在创建时返回完整API密钥，后续不再提供
            return new CreateApiKeyResult
            {
                ApiKeyInfo = createdApiKey,
                ApiKey = apiKey, // 仅在创建时返回一次
                SecurityNotice = "请妥善保管此API密钥，系统不会再次显示完整密钥。如遗失请重新生成。"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建API密钥时发生异常: {KeyName}, 创建者: {CreatedBy}",
                request.KeyName, request.CreatedBy);
            throw;
        }
    }

    public async Task UpdateApiKeyAsync(int apiKeyId, UpdateApiKeyRequest request)
    {
        try
        {
            await _apiKeyRepository.UpdateAsync(apiKeyId, request);
            
            if (request.PermissionNames != null)
            {
                await _apiKeyRepository.SetPermissionsAsync(apiKeyId, request.PermissionNames);
            }

            // 更新完成

            _logger.LogInformation("更新API密钥成功: {ApiKeyId}", apiKeyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新API密钥时发生异常: {ApiKeyId}", apiKeyId);
            throw;
        }
    }

    public async Task DisableApiKeyAsync(int apiKeyId)
    {
        try
        {
            await _apiKeyRepository.DisableAsync(apiKeyId);
            
            // 禁用完成

            _logger.LogInformation("禁用API密钥成功: {ApiKeyId}", apiKeyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用API密钥时发生异常: {ApiKeyId}", apiKeyId);
            throw;
        }
    }

    public async Task<IEnumerable<ApiKeyInfo>> GetApiKeysAsync(string? keyType = null, bool? isActive = null)
    {
        try
        {
            return await _apiKeyRepository.GetAllAsync(keyType, isActive);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取API密钥列表时发生异常");
            throw;
        }
    }

    #region 私有方法

    private bool IsPermissionMatch(ApiPermission permission, string endpoint, string httpMethod)
    {
        if (!permission.IsActive)
        {
            return false;
        }

        // 检查HTTP方法
        if (!IsHttpMethodAllowed(permission.HttpMethods, httpMethod))
        {
            return false;
        }

        // 检查端点匹配
        return IsEndpointMatch(permission.EndpointPattern, endpoint);
    }

    private static bool IsHttpMethodAllowed(string allowedMethods, string requestMethod)
    {
        if (string.IsNullOrWhiteSpace(allowedMethods))
        {
            return true;
        }

        var methods = allowedMethods.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(m => m.Trim().ToUpperInvariant())
            .ToArray();

        return methods.Contains(requestMethod.ToUpperInvariant()) || methods.Contains("*");
    }

    private static bool IsEndpointMatch(string pattern, string endpoint)
    {
        if (string.IsNullOrWhiteSpace(pattern))
        {
            return false;
        }

        // 标准化路径
        pattern = pattern.TrimEnd('/');
        endpoint = endpoint.TrimEnd('/');

        // 精确匹配
        if (string.Equals(pattern, endpoint, StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        // 通配符匹配
        if (pattern.EndsWith("*"))
        {
            var prefix = pattern.Substring(0, pattern.Length - 1);
            return endpoint.StartsWith(prefix, StringComparison.OrdinalIgnoreCase);
        }

        return false;
    }

    private static string ComputeHash(string input)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    private static string GenerateApiKey()
    {
        // 生成32字节的随机数据
        var randomBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        
        // 转换为Base64字符串并移除特殊字符
        return Convert.ToBase64String(randomBytes)
            .Replace("+", "")
            .Replace("/", "")
            .Replace("=", "")
            .Substring(0, 32);
    }

    public async Task<IEnumerable<ApiPermission>> GetAllPermissionsAsync()
    {
        try
        {
            var entityPermissions = await _apiKeyRepository.GetAllPermissionsAsync();
            return entityPermissions.Select(ConvertToApiPermission);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有权限失败");
            throw;
        }
    }

    private static ApiPermission ConvertToApiPermission(Entities.ApiPermission entity)
    {
        return new ApiPermission
        {
            Id = entity.Id,
            PermissionName = entity.PermissionName,
            EndpointPattern = entity.EndpointPattern,
            HttpMethods = entity.HttpMethods,
            Description = entity.Description ?? string.Empty,
            IsActive = entity.IsActive
        };
    }

    #endregion
}
