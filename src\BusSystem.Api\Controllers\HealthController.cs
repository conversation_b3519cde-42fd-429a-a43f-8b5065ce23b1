using Microsoft.AspNetCore.Mvc;
using BusSystem.Shared.Models.Common;
using BusSystem.Core.Interfaces.Services;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 健康检查控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly IRedisService _redisService;
    private readonly ILogger<HealthController> _logger;

    public HealthController(IRedisService redisService, ILogger<HealthController> logger)
    {
        _redisService = redisService;
        _logger = logger;
    }

    /// <summary>
    /// 基础健康检查
    /// </summary>
    [HttpGet]
    public Task<ActionResult<ApiResponse<object>>> Get()
    {
        try
        {
            var healthInfo = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
            };

            _logger.LogInformation("健康检查请求 - 状态: {Status}", healthInfo.Status);
            
            return Task.FromResult<ActionResult<ApiResponse<object>>>(Ok(ApiResponse<object>.Ok(healthInfo, "系统运行正常")));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查失败");
            return Task.FromResult<ActionResult<ApiResponse<object>>>(StatusCode(500, ApiResponse<object>.Fail("系统异常", "HEALTH_CHECK_FAILED")));
        }
    }

    /// <summary>
    /// 详细健康检查（包括数据库和Redis连接）
    /// </summary>
    [HttpGet("detailed")]
    public async Task<ActionResult<ApiResponse<object>>> GetDetailed()
    {
        try
        {
            var healthChecks = new Dictionary<string, object>
            {
                ["api"] = new { Status = "Healthy", Timestamp = DateTime.UtcNow }
            };

            // 检查Redis连接
            try
            {
                await _redisService.SetStringAsync("health_check", "test", TimeSpan.FromSeconds(10));
                var redisTest = await _redisService.GetStringAsync("health_check");
                healthChecks["redis"] = new { Status = redisTest == "test" ? "Healthy" : "Unhealthy" };
                await _redisService.DeleteAsync("health_check");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Redis健康检查失败");
                healthChecks["redis"] = new { Status = "Unhealthy", Error = ex.Message };
            }

            var overallStatus = healthChecks.Values.All(v => 
                v.GetType().GetProperty("Status")?.GetValue(v)?.ToString() == "Healthy") 
                ? "Healthy" : "Degraded";

            var result = new
            {
                OverallStatus = overallStatus,
                Checks = healthChecks,
                Timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("详细健康检查完成 - 整体状态: {Status}", overallStatus);

            return Ok(ApiResponse<object>.Ok(result, $"系统状态: {overallStatus}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "详细健康检查失败");
            return StatusCode(500, ApiResponse<object>.Fail("系统异常", "DETAILED_HEALTH_CHECK_FAILED"));
        }
    }
}
