namespace BusSystem.Core.Entities;

/// <summary>
/// API密钥实体
/// </summary>
public class ApiKey : BaseEntity
{
    /// <summary>
    /// 密钥名称
    /// </summary>
    public string KeyName { get; set; } = string.Empty;

    /// <summary>
    /// API密钥哈希值
    /// </summary>
    public string ApiKeyHash { get; set; } = string.Empty;

    /// <summary>
    /// 密钥类型 (sync/frontend/partner/admin)
    /// </summary>
    public string KeyType { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 所有者名称
    /// </summary>
    public string? OwnerName { get; set; }

    /// <summary>
    /// 所有者联系方式
    /// </summary>
    public string? OwnerContact { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 每小时速率限制
    /// </summary>
    public int RateLimitPerHour { get; set; } = 1000;

    /// <summary>
    /// 每天速率限制
    /// </summary>
    public int RateLimitPerDay { get; set; } = 10000;

    /// <summary>
    /// 允许的IP地址列表
    /// </summary>
    public string[]? AllowedIps { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; } = 0;

    /// <summary>
    /// 关联的权限
    /// </summary>
    public virtual ICollection<ApiKeyPermission> ApiKeyPermissions { get; set; } = new List<ApiKeyPermission>();
}

/// <summary>
/// API权限实体
/// </summary>
public class ApiPermission : BaseEntity
{
    /// <summary>
    /// 权限名称
    /// </summary>
    public string PermissionName { get; set; } = string.Empty;

    /// <summary>
    /// 端点模式
    /// </summary>
    public string EndpointPattern { get; set; } = string.Empty;

    /// <summary>
    /// HTTP方法
    /// </summary>
    public string HttpMethods { get; set; } = "GET,POST,PUT,DELETE";

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 关联的API密钥权限
    /// </summary>
    public virtual ICollection<ApiKeyPermission> ApiKeyPermissions { get; set; } = new List<ApiKeyPermission>();
}

/// <summary>
/// API密钥权限关联实体
/// </summary>
public class ApiKeyPermission
{
    /// <summary>
    /// API密钥ID
    /// </summary>
    public int ApiKeyId { get; set; }

    /// <summary>
    /// 权限ID
    /// </summary>
    public int PermissionId { get; set; }

    /// <summary>
    /// 授权时间
    /// </summary>
    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 授权人
    /// </summary>
    public string? GrantedBy { get; set; }

    /// <summary>
    /// 关联的API密钥
    /// </summary>
    public virtual ApiKey ApiKey { get; set; } = null!;

    /// <summary>
    /// 关联的权限
    /// </summary>
    public virtual ApiPermission Permission { get; set; } = null!;
}
