using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using BusSystem.Core.Interfaces.Services;

namespace BusSystem.Api.Authorization;

/// <summary>
/// API权限授权处理器
/// </summary>
public class ApiPermissionAuthorizationHandler : AuthorizationHandler<ApiPermissionRequirement>
{
    private readonly IApiKeyService _apiKeyService;
    private readonly ILogger<ApiPermissionAuthorizationHandler> _logger;

    public ApiPermissionAuthorizationHandler(
        IApiKeyService apiKeyService,
        ILogger<ApiPermissionAuthorizationHandler> logger)
    {
        _apiKeyService = apiKeyService;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        ApiPermissionRequirement requirement)
    {
        try
        {
            // 检查用户是否已认证
            if (!context.User.Identity?.IsAuthenticated ?? true)
            {
                _logger.LogDebug("用户未认证，拒绝访问");
                context.Fail();
                return;
            }

            // 获取API Key ID
            var apiKeyIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
            if (apiKeyIdClaim == null || !int.TryParse(apiKeyIdClaim.Value, out var apiKeyId))
            {
                _logger.LogWarning("无法获取API密钥ID");
                context.Fail();
                return;
            }

            // 获取当前请求的端点和HTTP方法
            var httpContext = context.Resource as HttpContext;
            if (httpContext == null)
            {
                _logger.LogWarning("无法获取HTTP上下文");
                context.Fail();
                return;
            }

            var endpoint = httpContext.Request.Path.Value ?? string.Empty;
            var httpMethod = httpContext.Request.Method;

            // 检查是否有访问权限
            var hasPermission = await _apiKeyService.HasPermissionAsync(apiKeyId, endpoint, httpMethod);
            if (!hasPermission)
            {
                var keyName = context.User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
                _logger.LogWarning("API密钥 {KeyName} 没有访问 {Method} {Endpoint} 的权限", 
                    keyName, httpMethod, endpoint);
                context.Fail();
                return;
            }

            _logger.LogDebug("API权限验证通过: {Method} {Endpoint}", httpMethod, endpoint);
            context.Succeed(requirement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API权限验证过程中发生异常");
            context.Fail();
        }
    }
}

/// <summary>
/// API权限要求
/// </summary>
public class ApiPermissionRequirement : IAuthorizationRequirement
{
    public string? RequiredPermission { get; }

    public ApiPermissionRequirement(string? requiredPermission = null)
    {
        RequiredPermission = requiredPermission;
    }
}

/// <summary>
/// API权限授权特性
/// </summary>
public class ApiAuthorizeAttribute : AuthorizeAttribute
{
    public ApiAuthorizeAttribute(string? permission = null)
    {
        Policy = permission != null ? $"ApiPermission:{permission}" : "ApiPermission";
    }
}

/// <summary>
/// API权限策略扩展
/// </summary>
public static class ApiAuthorizationPolicyExtensions
{
    public static void AddApiAuthorizationPolicies(this IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // 默认API权限策略
            options.AddPolicy("ApiPermission", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.Requirements.Add(new ApiPermissionRequirement());
            });

            // 数据同步权限策略
            options.AddPolicy("ApiPermission:DataSync", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.Requirements.Add(new ApiPermissionRequirement("datasync"));
            });

            // 前端应用权限策略
            options.AddPolicy("ApiPermission:Frontend", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.Requirements.Add(new ApiPermissionRequirement("frontend"));
            });

            // 第三方合作伙伴权限策略
            options.AddPolicy("ApiPermission:Partner", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.Requirements.Add(new ApiPermissionRequirement("partner"));
            });

            // 管理员权限策略
            options.AddPolicy("ApiPermission:Admin", policy =>
            {
                policy.RequireAuthenticatedUser();
                policy.Requirements.Add(new ApiPermissionRequirement("admin"));
            });

            // 特定权限策略（可以根据需要添加更多）
            var specificPermissions = new[]
            {
                "datasync.gps", "datasync.base_data", "datasync.cleanup",
                "home.nearby_stops", "home.search", "search.all",
                "lines.list", "stops.list", "realtime.vehicles"
            };

            foreach (var permission in specificPermissions)
            {
                options.AddPolicy($"ApiPermission:{permission}", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.Requirements.Add(new ApiPermissionRequirement(permission));
                });
            }
        });

        // 注册授权处理器
        services.AddScoped<IAuthorizationHandler, ApiPermissionAuthorizationHandler>();
    }
}

/// <summary>
/// 端点权限匹配器
/// </summary>
public static class EndpointPermissionMatcher
{
    /// <summary>
    /// 检查端点是否匹配权限模式
    /// </summary>
    /// <param name="endpointPattern">权限端点模式（支持通配符）</param>
    /// <param name="requestEndpoint">请求端点</param>
    /// <param name="allowedMethods">允许的HTTP方法</param>
    /// <param name="requestMethod">请求的HTTP方法</param>
    /// <returns>是否匹配</returns>
    public static bool IsMatch(string endpointPattern, string requestEndpoint, string allowedMethods, string requestMethod)
    {
        // 检查HTTP方法
        if (!IsMethodAllowed(allowedMethods, requestMethod))
        {
            return false;
        }

        // 检查端点模式
        return IsEndpointMatch(endpointPattern, requestEndpoint);
    }

    private static bool IsMethodAllowed(string allowedMethods, string requestMethod)
    {
        if (string.IsNullOrWhiteSpace(allowedMethods))
        {
            return true; // 没有限制
        }

        var methods = allowedMethods.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(m => m.Trim().ToUpperInvariant())
            .ToArray();

        return methods.Contains(requestMethod.ToUpperInvariant()) || methods.Contains("*");
    }

    private static bool IsEndpointMatch(string pattern, string endpoint)
    {
        if (string.IsNullOrWhiteSpace(pattern))
        {
            return false;
        }

        // 标准化路径（移除尾部斜杠）
        pattern = pattern.TrimEnd('/');
        endpoint = endpoint.TrimEnd('/');

        // 精确匹配
        if (pattern == endpoint)
        {
            return true;
        }

        // 通配符匹配
        if (pattern.EndsWith("*"))
        {
            var prefix = pattern.Substring(0, pattern.Length - 1);
            return endpoint.StartsWith(prefix, StringComparison.OrdinalIgnoreCase);
        }

        // 参数匹配（如 /api/stops/{id} 匹配 /api/stops/123）
        if (pattern.Contains("{") && pattern.Contains("}"))
        {
            return IsParameterizedMatch(pattern, endpoint);
        }

        return false;
    }

    private static bool IsParameterizedMatch(string pattern, string endpoint)
    {
        var patternParts = pattern.Split('/', StringSplitOptions.RemoveEmptyEntries);
        var endpointParts = endpoint.Split('/', StringSplitOptions.RemoveEmptyEntries);

        if (patternParts.Length != endpointParts.Length)
        {
            return false;
        }

        for (int i = 0; i < patternParts.Length; i++)
        {
            var patternPart = patternParts[i];
            var endpointPart = endpointParts[i];

            // 如果是参数占位符，跳过
            if (patternPart.StartsWith("{") && patternPart.EndsWith("}"))
            {
                continue;
            }

            // 如果是通配符，匹配成功
            if (patternPart == "*")
            {
                continue;
            }

            // 精确匹配
            if (!string.Equals(patternPart, endpointPart, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }
        }

        return true;
    }
}
