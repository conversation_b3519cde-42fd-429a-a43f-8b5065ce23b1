-- =============================================
-- API认证系统一键设置脚本
-- 使用方法: psql -h localhost -U bus_user -d bus_system -f setup-api-auth.sql
-- =============================================

-- 1. 创建API密钥主表
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL,
    api_key_hash VARCHAR(64) NOT NULL UNIQUE,
    key_type VARCHAR(20) NOT NULL,
    description TEXT,
    owner_name VARCHAR(100),
    owner_contact VARCHAR(200),
    is_active BOOLEAN DEFAULT true,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    allowed_ips TEXT[],
    expires_at TIMESTAMPTZ,
    last_used_at TIMESTAMPTZ,
    usage_count BIGINT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    
    CONSTRAINT chk_key_type CHECK (key_type IN ('sync', 'frontend', 'partner', 'admin'))
);

-- 2. 创建API权限定义表
CREATE TABLE IF NOT EXISTS api_permissions (
    id SERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    endpoint_pattern VARCHAR(200) NOT NULL,
    http_methods VARCHAR(100) DEFAULT 'GET,POST,PUT,DELETE',
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建API密钥权限关联表
CREATE TABLE IF NOT EXISTS api_key_permissions (
    api_key_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    granted_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    granted_by VARCHAR(100),
    
    PRIMARY KEY (api_key_id, permission_id),
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES api_permissions(id) ON DELETE CASCADE
);

-- 4. 创建API访问审计表
CREATE TABLE IF NOT EXISTS api_access_audit (
    id BIGSERIAL PRIMARY KEY,
    api_key_id INTEGER,
    api_key_hash VARCHAR(64),
    endpoint VARCHAR(500) NOT NULL,
    http_method VARCHAR(10) NOT NULL,
    client_ip INET,
    user_agent TEXT,
    request_size BIGINT,
    response_size BIGINT,
    response_time_ms INTEGER,
    status_code INTEGER,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL
);

-- 5. 创建API速率限制记录表
CREATE TABLE IF NOT EXISTS api_rate_limits (
    id BIGSERIAL PRIMARY KEY,
    api_key_id INTEGER NOT NULL,
    time_window TIMESTAMPTZ NOT NULL,
    request_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(api_key_id, time_window),
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_api_keys_hash ON api_keys (api_key_hash);
CREATE INDEX IF NOT EXISTS idx_api_keys_type_active ON api_keys (key_type, is_active);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires ON api_keys (expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_api_permissions_pattern ON api_permissions (endpoint_pattern);
CREATE INDEX IF NOT EXISTS idx_api_access_audit_key_time ON api_access_audit (api_key_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_access_audit_endpoint ON api_access_audit (endpoint, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_access_audit_status ON api_access_audit (status_code, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_key_window ON api_rate_limits (api_key_id, time_window DESC);

-- 插入默认权限定义
INSERT INTO api_permissions (permission_name, endpoint_pattern, http_methods, description) VALUES
-- 数据同步权限
('datasync.gps', '/api/datasync/gps*', 'POST', 'GPS数据同步权限'),
('datasync.base_data', '/api/datasync/base-data*', 'POST', '基础数据同步权限'),
('datasync.status', '/api/datasync/status*', 'GET', '同步状态查询权限'),
('datasync.health', '/api/datasync/health*', 'GET', '健康检查权限'),
('datasync.cleanup', '/api/datasync/cleanup*', 'POST', '数据清理权限（管理员）'),

-- 前端业务权限
('home.nearby_stops', '/api/home/<USER>', 'GET', '附近站点查询权限'),
('home.stop_detail', '/api/home/<USER>/*/detail*', 'GET', '站点详情查询权限'),
('home.stop_alternatives', '/api/home/<USER>/*/alternatives*', 'GET', '站点切换查询权限'),
('home.stop_predictions', '/api/home/<USER>/*/predictions*', 'GET', '单站点预测权限'),
('home.search', '/api/home/<USER>', 'GET', '首页搜索权限'),

-- 搜索权限
('search.lines', '/api/search/lines*', 'GET', '线路搜索权限'),
('search.stops', '/api/search/stops*', 'GET', '站点搜索权限'),
('search.all', '/api/search/all*', 'GET', '综合搜索权限'),
('search.suggestions', '/api/search/suggestions*', 'GET', '搜索建议权限'),
('search.hot_keywords', '/api/search/hot-keywords*', 'GET', '热门关键词权限'),
('search.stats', '/api/search/stats*', 'GET', '搜索统计权限'),

-- 线路权限
('lines.list', '/api/lines*', 'GET', '线路列表权限'),
('lines.detail', '/api/lines/*', 'GET', '线路详情权限'),

-- 站点权限
('stops.list', '/api/stops*', 'GET', '站点列表权限'),
('stops.detail', '/api/stops/*', 'GET', '站点详情权限'),
('stops.nearby', '/api/stops/nearby*', 'GET', '附近站点权限'),

-- 实时数据权限
('realtime.vehicles', '/api/realtime/vehicles*', 'GET', '实时车辆位置权限'),
('realtime.predictions', '/api/realtime/predictions*', 'GET', '实时到站预测权限'),

-- 坐标转换权限
('coordinate.transform', '/api/coordinate/transform*', 'POST', '坐标转换权限'),
('coordinate.batch_transform', '/api/coordinate/batch-transform*', 'POST', '批量坐标转换权限'),

-- 通知权限
('notification.websocket', '/api/notification/ws*', 'GET', 'WebSocket连接权限'),

-- 管理权限
('admin.api_keys', '/api/admin/api-keys*', 'GET,POST,PUT,DELETE', 'API密钥管理权限'),
('admin.permissions', '/api/admin/permissions*', 'GET', '权限管理权限'),
('admin.stats', '/api/admin/stats*', 'GET', '统计信息权限'),
('admin.health', '/api/admin/health*', 'GET', '管理健康检查权限'),

-- 系统权限
('system.health', '/health*', 'GET', '系统健康检查权限')

ON CONFLICT (permission_name) DO NOTHING;

-- 插入测试API密钥
INSERT INTO api_keys (key_name, api_key_hash, key_type, description, owner_name, rate_limit_per_hour, rate_limit_per_day) VALUES
('测试前端应用', 'frontend_test_key_hash_12345', 'frontend', '用于前端H5应用测试', '前端开发团队', 5000, 50000),
('平台A数据同步', 'platform_a_sync_key_hash_12345', 'sync', '平台A调度数据同步', '平台A技术团队', 2000, 20000),
('海信数据同步', 'hisense_sync_key_hash_12345', 'sync', '海信调度数据同步', '海信技术团队', 2000, 20000),
('第三方合作伙伴', 'partner_demo_key_hash_12345', 'partner', '第三方合作伙伴演示', '合作伙伴', 1000, 10000),
('系统管理员', 'admin_key_hash_12345', 'admin', '系统管理员权限', '系统管理员', 10000, 100000)

ON CONFLICT (api_key_hash) DO NOTHING;

-- 配置前端应用权限
INSERT INTO api_key_permissions (api_key_id, permission_id)
SELECT ak.id, ap.id 
FROM api_keys ak, api_permissions ap 
WHERE ak.key_type = 'frontend' 
  AND ap.permission_name IN (
    'home.nearby_stops', 'home.stop_detail', 'home.stop_alternatives', 'home.stop_predictions', 'home.search',
    'search.lines', 'search.stops', 'search.all', 'search.suggestions', 'search.hot_keywords',
    'lines.list', 'lines.detail', 'stops.list', 'stops.detail', 'stops.nearby',
    'realtime.vehicles', 'realtime.predictions', 'coordinate.transform', 'notification.websocket', 'system.health'
  )
ON CONFLICT DO NOTHING;

-- 配置数据同步权限
INSERT INTO api_key_permissions (api_key_id, permission_id)
SELECT ak.id, ap.id 
FROM api_keys ak, api_permissions ap 
WHERE ak.key_type = 'sync' 
  AND ap.permission_name IN (
    'datasync.gps', 'datasync.base_data', 'datasync.status', 'datasync.health', 'system.health'
  )
ON CONFLICT DO NOTHING;

-- 配置第三方合作伙伴权限（受限）
INSERT INTO api_key_permissions (api_key_id, permission_id)
SELECT ak.id, ap.id 
FROM api_keys ak, api_permissions ap 
WHERE ak.key_type = 'partner' 
  AND ap.permission_name IN (
    'lines.list', 'lines.detail', 'stops.list', 'stops.detail', 'stops.nearby',
    'search.lines', 'search.stops', 'realtime.predictions', 'system.health'
  )
ON CONFLICT DO NOTHING;

-- 配置管理员权限（所有权限）
INSERT INTO api_key_permissions (api_key_id, permission_id)
SELECT ak.id, ap.id 
FROM api_keys ak, api_permissions ap 
WHERE ak.key_type = 'admin'
ON CONFLICT DO NOTHING;

-- 添加表注释
COMMENT ON TABLE api_keys IS 'API密钥管理表，存储所有API访问密钥';
COMMENT ON TABLE api_permissions IS 'API权限定义表，定义系统中所有可访问的权限';
COMMENT ON TABLE api_key_permissions IS 'API密钥权限关联表，控制密钥的访问权限';
COMMENT ON TABLE api_access_audit IS 'API访问审计日志表，记录所有API访问行为';
COMMENT ON TABLE api_rate_limits IS 'API速率限制记录表，用于实现速率控制';

-- 显示设置结果
SELECT 'API认证系统设置完成！' as message;

SELECT 
    '测试API密钥' as category,
    key_name as name,
    key_type as type,
    api_key_hash as test_key,
    rate_limit_per_hour as hourly_limit
FROM api_keys 
WHERE key_name LIKE '测试%' OR key_name LIKE '%测试%'
ORDER BY key_type;

SELECT 
    '权限统计' as category,
    COUNT(*) as total_permissions
FROM api_permissions;

SELECT 
    '密钥权限统计' as category,
    ak.key_name,
    ak.key_type,
    COUNT(akp.permission_id) as permission_count
FROM api_keys ak
LEFT JOIN api_key_permissions akp ON ak.id = akp.api_key_id
GROUP BY ak.id, ak.key_name, ak.key_type
ORDER BY ak.key_type;
