using Microsoft.Extensions.Logging;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Shared.Constants;

namespace BusSystem.Core.Services;

/// <summary>
/// 到站预测服务实现
/// </summary>
public class PredictionService : IPredictionService
{
    private readonly IVehiclePositionRepository _positionRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IBusStopRepository _stopRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IRedisService _redisService;
    private readonly ILogger<PredictionService> _logger;

    public PredictionService(
        IVehiclePositionRepository positionRepository,
        IBusLineRepository lineRepository,
        IBusStopRepository stopRepository,
        IVehicleRepository vehicleRepository,
        IRedisService redisService,
        ILogger<PredictionService> logger)
    {
        _positionRepository = positionRepository;
        _lineRepository = lineRepository;
        _stopRepository = stopRepository;
        _vehicleRepository = vehicleRepository;
        _redisService = redisService;
        _logger = logger;
    }

    public async Task<object> PredictArrivalTimeAsync(int vehicleId, int stopId)
    {
        try
        {
            var vehicle = await _vehicleRepository.GetWithLineAsync(vehicleId);
            var stop = await _stopRepository.GetByIdAsync(stopId);
            var latestPosition = await _positionRepository.GetLatestPositionAsync(vehicleId);

            if (vehicle == null || stop == null || latestPosition == null)
            {
                return new { Error = "车辆、站点或位置信息不存在" };
            }

            // 获取线路站点信息
            var lineWithStops = await _lineRepository.GetWithStopsAsync(vehicle.LineId);
            if (lineWithStops?.LineStops == null)
            {
                return new { Error = "线路站点信息不存在" };
            }

            var targetLineStop = lineWithStops.LineStops.FirstOrDefault(ls => ls.StopId == stopId);
            if (targetLineStop == null)
            {
                return new { Error = "该站点不在此线路上" };
            }

            // 简单的距离和速度计算预测
            var distance = CalculateDistance(latestPosition.Latitude, latestPosition.Longitude, 
                                           stop.Latitude, stop.Longitude);

            var averageSpeed = latestPosition.Speed ?? await GetAverageSpeedAsync(vehicleId);
            if (averageSpeed <= 0) averageSpeed = 30; // 默认30km/h

            var estimatedTimeMinutes = (distance / 1000) / (averageSpeed / 60); // 转换为分钟
            var estimatedArrival = DateTime.UtcNow.AddMinutes(estimatedTimeMinutes);

            // 考虑交通状况和历史数据的调整因子
            var adjustmentFactor = await GetTrafficAdjustmentFactorAsync(vehicle.LineId, stopId);
            estimatedTimeMinutes *= adjustmentFactor;
            estimatedArrival = DateTime.UtcNow.AddMinutes(estimatedTimeMinutes);

            var result = new
            {
                VehicleId = vehicleId,
                VehicleNumber = vehicle.VehicleNumber,
                StopId = stopId,
                StopName = stop.StopName,
                CurrentPosition = new
                {
                    Latitude = latestPosition.Latitude,
                    Longitude = latestPosition.Longitude,
                    Timestamp = latestPosition.Timestamp
                },
                Distance = Math.Round(distance, 0), // 米
                EstimatedArrivalTime = estimatedArrival,
                EstimatedMinutes = Math.Round(estimatedTimeMinutes, 1),
                Confidence = CalculateConfidence(distance, averageSpeed, latestPosition.Timestamp)
            };

            // 缓存预测结果
            await CachePredictionAsync(vehicleId, stopId, result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测到站时间失败，车辆ID: {VehicleId}, 站点ID: {StopId}", vehicleId, stopId);
            throw;
        }
    }

    public async Task<IEnumerable<object>> GetStopArrivalPredictionsAsync(int stopId)
    {
        try
        {
            var stop = await _stopRepository.GetWithLinesAsync(stopId);
            if (stop?.LineStops == null)
            {
                return new List<object>();
            }

            var predictions = new List<object>();

            foreach (var lineStop in stop.LineStops)
            {
                var linePositions = await _positionRepository.GetLineVehiclePositionsAsync(lineStop.LineId);
                
                foreach (var position in linePositions)
                {
                    var prediction = await PredictArrivalTimeAsync(position.VehicleId, stopId);
                    predictions.Add(prediction);
                }
            }

            return predictions.OrderBy(p => 
            {
                var dict = p as dynamic;
                return dict?.EstimatedMinutes ?? double.MaxValue;
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点到站预测失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    public async Task<IEnumerable<object>> GetLineArrivalPredictionsAsync(int lineId)
    {
        try
        {
            var lineWithStops = await _lineRepository.GetWithStopsAsync(lineId);
            if (lineWithStops?.LineStops == null)
            {
                return new List<object>();
            }

            var predictions = new List<object>();
            var linePositions = await _positionRepository.GetLineVehiclePositionsAsync(lineId);

            foreach (var position in linePositions)
            {
                foreach (var lineStop in lineWithStops.LineStops.OrderBy(ls => ls.SequenceNumber))
                {
                    var prediction = await PredictArrivalTimeAsync(position.VehicleId, lineStop.StopId);
                    predictions.Add(prediction);
                }
            }

            return predictions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路到站预测失败，线路ID: {LineId}", lineId);
            throw;
        }
    }

    public async Task<object> PredictNextStopArrivalAsync(int vehicleId)
    {
        try
        {
            var latestPosition = await _positionRepository.GetLatestPositionAsync(vehicleId);
            if (latestPosition?.NextStopId == null)
            {
                return new { Error = "无法确定下一站" };
            }

            return await PredictArrivalTimeAsync(vehicleId, latestPosition.NextStopId.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测下一站到站时间失败，车辆ID: {VehicleId}", vehicleId);
            throw;
        }
    }

    public async Task<double> PredictTravelTimeAsync(int fromStopId, int toStopId, int lineId, DateTime? departureTime = null)
    {
        try
        {
            // 简化实现：基于距离和平均速度
            var fromStop = await _stopRepository.GetByIdAsync(fromStopId);
            var toStop = await _stopRepository.GetByIdAsync(toStopId);

            if (fromStop == null || toStop == null)
            {
                return 0;
            }

            var distance = CalculateDistance(fromStop.Latitude, fromStop.Longitude, 
                                           toStop.Latitude, toStop.Longitude);

            var averageSpeed = await GetLineAverageSpeedAsync(lineId);
            var travelTimeMinutes = (distance / 1000) / (averageSpeed / 60);

            // 考虑时间段的交通状况
            var timeFactor = GetTimeOfDayFactor(departureTime ?? DateTime.Now);
            return travelTimeMinutes * timeFactor;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预测行程时间失败，从站点 {FromStopId} 到站点 {ToStopId}", fromStopId, toStopId);
            return 0;
        }
    }

    public async Task UpdatePredictionModelAsync(int vehicleId, int stopId, DateTime actualArrivalTime)
    {
        try
        {
            // 获取之前的预测结果
            var cachedPrediction = await GetCachedPredictionAsync(vehicleId, stopId);
            if (cachedPrediction != null)
            {
                // 计算预测误差并更新模型参数
                // 这里可以实现机器学习模型的更新逻辑
                _logger.LogInformation("更新预测模型，车辆ID: {VehicleId}, 站点ID: {StopId}, 实际到站时间: {ActualTime}", 
                    vehicleId, stopId, actualArrivalTime);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新预测模型失败，车辆ID: {VehicleId}, 站点ID: {StopId}", vehicleId, stopId);
        }
    }

    public async Task<object> GetPredictionAccuracyStatsAsync(int lineId, DateTime date)
    {
        try
        {
            // 简化实现：返回模拟的准确率统计
            return new
            {
                LineId = lineId,
                Date = date.ToString("yyyy-MM-dd"),
                TotalPredictions = 150,
                AccuratePredictions = 128,
                AccuracyRate = 85.3,
                AverageError = 2.5, // 分钟
                MaxError = 8.2,
                MinError = 0.1
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取预测准确率统计失败，线路ID: {LineId}, 日期: {Date}", lineId, date);
            throw;
        }
    }

    #region 私有辅助方法

    private static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLon = (lon2 - lon1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    private async Task<double> GetAverageSpeedAsync(int vehicleId)
    {
        try
        {
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-1); // 最近1小时
            var positions = await _positionRepository.GetVehicleTrajectoryAsync(vehicleId, startTime, endTime);
            
            var speeds = positions.Where(p => p.Speed.HasValue && p.Speed > 0).Select(p => p.Speed!.Value);
            return speeds.Any() ? speeds.Average() : 30; // 默认30km/h
        }
        catch
        {
            return 30; // 默认速度
        }
    }

    private async Task<double> GetLineAverageSpeedAsync(int lineId)
    {
        try
        {
            var positions = await _positionRepository.GetLineVehiclePositionsAsync(lineId);
            var speeds = positions.Where(p => p.Speed.HasValue && p.Speed > 0).Select(p => p.Speed!.Value);
            return speeds.Any() ? speeds.Average() : 30;
        }
        catch
        {
            return 30;
        }
    }

    private async Task<double> GetTrafficAdjustmentFactorAsync(int lineId, int stopId)
    {
        // 简化实现：根据时间段返回交通调整因子
        var hour = DateTime.Now.Hour;
        if (hour >= 7 && hour <= 9 || hour >= 17 && hour <= 19)
        {
            return 1.3; // 高峰期增加30%时间
        }
        return 1.0;
    }

    private static double CalculateConfidence(double distance, double speed, DateTime lastUpdate)
    {
        var timeSinceUpdate = (DateTime.UtcNow - lastUpdate).TotalMinutes;
        var baseConfidence = 0.9;
        
        // 距离越近，置信度越高
        if (distance < 500) baseConfidence += 0.05;
        else if (distance > 2000) baseConfidence -= 0.1;
        
        // 数据越新，置信度越高
        if (timeSinceUpdate > 5) baseConfidence -= 0.2;
        
        return Math.Max(0.1, Math.Min(1.0, baseConfidence));
    }

    private static double GetTimeOfDayFactor(DateTime time)
    {
        var hour = time.Hour;
        if (hour >= 7 && hour <= 9 || hour >= 17 && hour <= 19)
        {
            return 1.4; // 高峰期
        }
        if (hour >= 22 || hour <= 6)
        {
            return 0.8; // 夜间
        }
        return 1.0; // 平峰期
    }

    private async Task CachePredictionAsync(int vehicleId, int stopId, object prediction)
    {
        try
        {
            var cacheKey = $"{SystemConstants.RedisKeys.RealtimeData}:prediction:{vehicleId}:{stopId}";
            await _redisService.SetAsync(cacheKey, prediction, TimeSpan.FromMinutes(2));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "缓存预测结果失败");
        }
    }

    private async Task<object?> GetCachedPredictionAsync(int vehicleId, int stopId)
    {
        try
        {
            var cacheKey = $"{SystemConstants.RedisKeys.RealtimeData}:prediction:{vehicleId}:{stopId}";
            return await _redisService.GetAsync<object>(cacheKey);
        }
        catch
        {
            return null;
        }
    }

    #endregion
}
