using BusSystem.Core.Entities;

namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// 到站预测服务接口
/// </summary>
public interface IPredictionService
{
    /// <summary>
    /// 预测车辆到站时间
    /// </summary>
    Task<object> PredictArrivalTimeAsync(int vehicleId, int stopId);

    /// <summary>
    /// 获取站点的到站预测
    /// </summary>
    Task<IEnumerable<object>> GetStopArrivalPredictionsAsync(int stopId);

    /// <summary>
    /// 获取线路的到站预测
    /// </summary>
    Task<IEnumerable<object>> GetLineArrivalPredictionsAsync(int lineId);

    /// <summary>
    /// 预测车辆下一站到站时间
    /// </summary>
    Task<object> PredictNextStopArrivalAsync(int vehicleId);

    /// <summary>
    /// 预测行程时间
    /// </summary>
    Task<double> PredictTravelTimeAsync(int fromStopId, int toStopId, int lineId, DateTime? departureTime = null);

    /// <summary>
    /// 更新预测模型
    /// </summary>
    Task UpdatePredictionModelAsync(int vehicleId, int stopId, DateTime actualArrivalTime);

    /// <summary>
    /// 获取预测准确率统计
    /// </summary>
    Task<object> GetPredictionAccuracyStatsAsync(int lineId, DateTime date);
}
