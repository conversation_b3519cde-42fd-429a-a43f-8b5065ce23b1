using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// 公交线路Repository实现
/// </summary>
public class BusLineRepository : Repository<BusLine>, IBusLineRepository
{
    public BusLineRepository(BusSystemDbContext context) : base(context)
    {
    }

    public async Task<BusLine?> GetByLineNumberAsync(string lineNumber)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.LineNumber == lineNumber);
    }

    public async Task<IEnumerable<BusLine>> GetAllByLineNumberAsync(string lineNumber)
    {
        return await _dbSet
            .Where(x => x.LineNumber == lineNumber)
            .OrderBy(x => x.Direction)
            .ToListAsync();
    }

    public async Task<BusLine?> GetByExternalIdAsync(string externalId)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.ExternalId == externalId);
    }

    public async Task<BusLine?> GetByLineNumberAndDirectionAsync(string lineNumber, int direction)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.LineNumber == lineNumber && x.Direction == direction);
    }

    public async Task<BusLine?> GetWithStopsAsync(int lineId)
    {
        return await _dbSet
            .Include(x => x.LineStops)
                .ThenInclude(ls => ls.Stop)
            .FirstOrDefaultAsync(x => x.Id == lineId);
    }

    public async Task<BusLine?> GetWithVehiclesAsync(int lineId)
    {
        return await _dbSet
            .Include(x => x.Vehicles)
            .FirstOrDefaultAsync(x => x.Id == lineId);
    }

    public async Task<IEnumerable<BusLine>> SearchAsync(string keyword)
    {
        return await _dbSet
            .Where(x => x.LineNumber.Contains(keyword) || 
                       x.LineName.Contains(keyword) ||
                       x.StartStopName.Contains(keyword) ||
                       x.EndStopName.Contains(keyword))
            .OrderBy(x => x.LineNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusLine>> GetActiveAsync()
    {
        return await _dbSet
            .Where(x => x.Status == 1)
            .OrderBy(x => x.LineNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusLine>> GetByCompanyAsync(int companyId)
    {
        return await _dbSet
            .Where(x => x.CompanyId == companyId)
            .OrderBy(x => x.LineNumber)
            .ToListAsync();
    }
}
