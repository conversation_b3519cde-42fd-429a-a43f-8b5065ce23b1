using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using BusSystem.Core.Interfaces.Services;

namespace BusSystem.Api.Authentication;

/// <summary>
/// API Key认证处理器
/// </summary>
public class ApiKeyAuthenticationHandler : AuthenticationHandler<ApiKeyAuthenticationSchemeOptions>
{
    private readonly IApiKeyService _apiKeyService;
    private readonly ILogger<ApiKeyAuthenticationHandler> _logger;

    public ApiKeyAuthenticationHandler(
        IOptionsMonitor<ApiKeyAuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        IApiKeyService apiKeyService)
        : base(options, logger, encoder)
    {
        _apiKeyService = apiKeyService;
        _logger = logger.CreateLogger<ApiKeyAuthenticationHandler>();
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        try
        {
            // 从请求头获取API Key
            var apiKey = GetApiKeyFromRequest();
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                return AuthenticateResult.Fail("缺少API密钥");
            }

            // 验证API Key
            var apiKeyInfo = await _apiKeyService.ValidateApiKeyAsync(apiKey);
            if (apiKeyInfo == null)
            {
                _logger.LogWarning("无效的API密钥: {ApiKey}", MaskApiKey(apiKey));
                return AuthenticateResult.Fail("无效的API密钥");
            }

            // 检查API Key是否激活
            if (!apiKeyInfo.IsActive)
            {
                _logger.LogWarning("API密钥已禁用: {KeyName}", apiKeyInfo.KeyName);
                return AuthenticateResult.Fail("API密钥已禁用");
            }

            // 检查API Key是否过期
            if (apiKeyInfo.ExpiresAt.HasValue && apiKeyInfo.ExpiresAt.Value < DateTime.UtcNow)
            {
                _logger.LogWarning("API密钥已过期: {KeyName}", apiKeyInfo.KeyName);
                return AuthenticateResult.Fail("API密钥已过期");
            }

            // 检查IP白名单
            if (!IsIpAllowed(apiKeyInfo.AllowedIps))
            {
                _logger.LogWarning("IP地址不在白名单中: {ClientIp}, KeyName: {KeyName}", 
                    GetClientIp(), apiKeyInfo.KeyName);
                return AuthenticateResult.Fail("IP地址不被允许");
            }

            // 检查速率限制
            var rateLimitResult = await _apiKeyService.CheckRateLimitAsync(apiKeyInfo.Id);
            if (!rateLimitResult.IsAllowed)
            {
                _logger.LogWarning("API密钥超出速率限制: {KeyName}, 当前: {Current}, 限制: {Limit}", 
                    apiKeyInfo.KeyName, rateLimitResult.CurrentCount, rateLimitResult.Limit);
                return AuthenticateResult.Fail($"请求频率超限: {rateLimitResult.Message}");
            }

            // 创建用户身份
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, apiKeyInfo.Id.ToString()),
                new(ClaimTypes.Name, apiKeyInfo.KeyName),
                new("api_key_type", apiKeyInfo.KeyType),
                new("api_key_owner", apiKeyInfo.OwnerName),
                new("rate_limit_per_hour", apiKeyInfo.RateLimitPerHour.ToString()),
                new("rate_limit_per_day", apiKeyInfo.RateLimitPerDay.ToString())
            };

            // 添加权限声明
            foreach (var permission in apiKeyInfo.Permissions)
            {
                claims.Add(new Claim("permission", permission.PermissionName));
            }

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            // 异步更新使用统计（不等待结果）
            _ = Task.Run(async () =>
            {
                try
                {
                    await _apiKeyService.UpdateApiKeyUsageAsync(apiKeyInfo.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新API密钥使用统计失败: {KeyName}", apiKeyInfo.KeyName);
                }
            });

            _logger.LogDebug("API密钥认证成功: {KeyName}", apiKeyInfo.KeyName);
            return AuthenticateResult.Success(ticket);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API密钥认证过程中发生异常");
            return AuthenticateResult.Fail("认证过程中发生错误");
        }
    }

    protected override Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        Response.StatusCode = 401;
        Response.Headers.Append("WWW-Authenticate", "ApiKey");
        return Task.CompletedTask;
    }

    protected override Task HandleForbiddenAsync(AuthenticationProperties properties)
    {
        Response.StatusCode = 403;
        return Task.CompletedTask;
    }

    private string? GetApiKeyFromRequest()
    {
        // 优先从 X-API-Key 头获取
        if (Request.Headers.TryGetValue("X-API-Key", out var apiKeyHeader))
        {
            return apiKeyHeader.FirstOrDefault();
        }

        // 其次从 Authorization 头获取 (Bearer token)
        if (Request.Headers.TryGetValue("Authorization", out var authHeader))
        {
            var authValue = authHeader.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(authValue) && authValue.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                return authValue.Substring("Bearer ".Length).Trim();
            }
        }

        // 最后从查询参数获取（不推荐，但提供兼容性）
        if (Request.Query.TryGetValue("api_key", out var queryApiKey))
        {
            return queryApiKey.FirstOrDefault();
        }

        return null;
    }

    private bool IsIpAllowed(string[]? allowedIps)
    {
        if (allowedIps == null || allowedIps.Length == 0)
        {
            return true; // 没有IP限制
        }

        var clientIp = GetClientIp();
        if (string.IsNullOrWhiteSpace(clientIp))
        {
            return false;
        }

        return allowedIps.Contains(clientIp) || allowedIps.Contains("*");
    }

    private string? GetClientIp()
    {
        // 检查 X-Forwarded-For 头（代理/负载均衡器）
        if (Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedFor))
        {
            var ip = forwardedFor.FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim();
            if (!string.IsNullOrWhiteSpace(ip))
            {
                return ip;
            }
        }

        // 检查 X-Real-IP 头
        if (Request.Headers.TryGetValue("X-Real-IP", out var realIp))
        {
            var ip = realIp.FirstOrDefault()?.Trim();
            if (!string.IsNullOrWhiteSpace(ip))
            {
                return ip;
            }
        }

        // 使用连接的远程IP
        return Context.Connection.RemoteIpAddress?.ToString();
    }

    private static string MaskApiKey(string apiKey)
    {
        if (string.IsNullOrWhiteSpace(apiKey) || apiKey.Length <= 8)
        {
            return "****";
        }

        return $"{apiKey.Substring(0, 4)}****{apiKey.Substring(apiKey.Length - 4)}";
    }
}

/// <summary>
/// API Key认证方案选项
/// </summary>
public class ApiKeyAuthenticationSchemeOptions : AuthenticationSchemeOptions
{
    public const string DefaultScheme = "ApiKey";
    public string Scheme => DefaultScheme;
    public string AuthenticationType = DefaultScheme;
}

/// <summary>
/// API Key认证扩展方法
/// </summary>
public static class ApiKeyAuthenticationExtensions
{
    public static AuthenticationBuilder AddApiKeyAuthentication(
        this AuthenticationBuilder builder,
        Action<ApiKeyAuthenticationSchemeOptions>? configureOptions = null)
    {
        return builder.AddScheme<ApiKeyAuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(
            ApiKeyAuthenticationSchemeOptions.DefaultScheme,
            configureOptions);
    }
}
