{"format": 1, "restore": {"E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Infrastructure\\BusSystem.Infrastructure.csproj": {}}, "projects": {"E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Core\\BusSystem.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Core\\BusSystem.Core.csproj", "projectName": "BusSystem.Core", "projectPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Core\\BusSystem.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj": {"projectPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "NetTopologySuite": {"target": "Package", "version": "[2.5.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Infrastructure\\BusSystem.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Infrastructure\\BusSystem.Infrastructure.csproj", "projectName": "BusSystem.Infrastructure", "projectPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Infrastructure\\BusSystem.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Core\\BusSystem.Core.csproj": {"projectPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Core\\BusSystem.Core.csproj"}, "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj": {"projectPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL.NetTopologySuite": {"target": "Package", "version": "[8.0.8, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj", "projectName": "BusSystem.Shared", "projectPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\BusSystem.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Coding\\Solution\\实时公交\\src\\BusSystem.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}