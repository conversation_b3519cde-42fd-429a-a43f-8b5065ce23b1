# 实时公交预测系统架构设计

## 1. 系统整体架构

### 1.1 架构概览
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              前端展示层                                         │
│  Web前端 + 移动端 + 管理后台 + 实时倒计时显示                                    │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ↕ HTTP/WebSocket
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              API网关层                                          │
│  负载均衡 + 路由 + 限流 + 认证 + 监控                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ↕ HTTP
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            应用服务层                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │   预测服务集群   │  │   实时数据服务   │  │   聚合查询服务   │                │
│  │ PredictionAPI   │  │  RealtimeAPI    │  │ AggregationAPI  │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ↕ 
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            核心业务层                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │  智能预测引擎    │  │  路径生成引擎    │  │  时间表管理器    │                │
│  │ SmartPredictor  │  │ RouteGenerator  │  │ TimetableManager│                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │  热点分析器      │  │  缓存管理器      │  │  数据同步器      │                │
│  │ HotspotAnalyzer │  │  CacheManager   │  │ DataSynchronizer│                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ↕
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            数据访问层                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │   业务数据仓储   │  │   时序数据仓储   │  │   缓存数据仓储   │                │
│  │ BusinessRepo    │  │ TimeSeriesRepo  │  │   CacheRepo     │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ↕
┌─────────────────────────────────────────────────────────────────────────────────┐
│                            数据存储层                                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │   PostgreSQL    │  │   TimescaleDB   │  │     Redis       │                │
│  │   主业务数据     │  │   GPS轨迹数据    │  │   缓存+消息队列  │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 核心数据流
```
GPS数据接收 → 实时位置更新 → 智能预测触发 → 时间表查询 → 结果缓存 → 前端查询 → 实时显示
     ↓              ↓              ↓            ↓          ↓         ↓         ↓
  TimescaleDB → Redis缓存 → 预测计算 → 站点累加 → 多级缓存 → API响应 → 倒计时更新
```

## 2. 核心组件设计

### 2.1 智能预测引擎 (SmartPredictor)

#### 2.1.1 组件职责
- 实现"下一站实时计算+后续站点查表累加"的核心算法
- 管理多维度调整因子的计算和应用
- 协调各种预测策略的执行

#### 2.1.2 核心接口设计
```csharp
public interface ISmartPredictor
{
    /// <summary>
    /// 智能预测车辆到多个站点的时间
    /// </summary>
    Task<List<StopPrediction>> PredictMultipleStopsAsync(int vehicleId, List<int> stopIds);
    
    /// <summary>
    /// 预测车辆到下一站的时间（实时计算）
    /// </summary>
    Task<NextStopPrediction> PredictNextStopAsync(int vehicleId);
    
    /// <summary>
    /// 批量预测站点的所有车辆到站时间
    /// </summary>
    Task<List<VehiclePrediction>> PredictStopArrivalsAsync(int stopId);
    
    /// <summary>
    /// 更新预测模型（基于实际到站反馈）
    /// </summary>
    Task UpdatePredictionModelAsync(int vehicleId, int stopId, DateTime actualArrival);
}

public class SmartPredictor : ISmartPredictor
{
    private readonly ITimetableManager _timetableManager;
    private readonly IHotspotAnalyzer _hotspotAnalyzer;
    private readonly ICacheManager _cacheManager;
    private readonly ILocationInferenceService _locationService;
    
    public async Task<List<StopPrediction>> PredictMultipleStopsAsync(int vehicleId, List<int> stopIds)
    {
        // 1. 获取车辆当前状态
        var vehicleState = await _locationService.GetVehicleLocationStateAsync(vehicleId);
        
        // 2. 计算到下一站的时间（实时GPS计算）
        var nextStopPrediction = await PredictNextStopAsync(vehicleId);
        
        // 3. 查表累加后续站点时间
        var predictions = new List<StopPrediction>();
        var currentTime = nextStopPrediction.EstimatedArrivalTime;
        
        foreach (var stopId in stopIds.Skip(1)) // 跳过下一站
        {
            var segmentTime = await _timetableManager.GetTravelTimeAsync(
                vehicleState.LineId, vehicleState.Direction, 
                previousStopId, stopId, currentTime);
            
            currentTime = currentTime.AddSeconds(segmentTime);
            
            predictions.Add(new StopPrediction
            {
                VehicleId = vehicleId,
                StopId = stopId,
                EstimatedArrivalTime = currentTime,
                Confidence = CalculateConfidence(stopId, nextStopPrediction.StopId),
                CalculationMethod = "timetable_lookup"
            });
        }
        
        return predictions;
    }
}
```

### 2.2 时间表管理器 (TimetableManager)

#### 2.2.1 组件职责
- 管理站点间运行时间表的生成、更新和查询
- 实现多维度调整因子的计算和应用
- 提供高性能的时间表查询服务

#### 2.2.2 核心接口设计
```csharp
public interface ITimetableManager
{
    /// <summary>
    /// 获取站点间运行时间（应用调整因子）
    /// </summary>
    Task<int> GetTravelTimeAsync(int lineId, int direction, int fromStopId, int toStopId, DateTime queryTime);
    
    /// <summary>
    /// 批量生成线路时间表
    /// </summary>
    Task<TimetableGenerationResult> GenerateTimetableAsync(int lineId, TimetableGenerationOptions options);
    
    /// <summary>
    /// 更新时间表（基于新的GPS数据）
    /// </summary>
    Task UpdateTimetableAsync(int lineId, DateTime startDate, DateTime endDate);
    
    /// <summary>
    /// 获取时间表质量评估
    /// </summary>
    Task<TimetableQualityReport> AssessTimetableQualityAsync(int lineId);
}
```

### 2.3 热点分析器 (HotspotAnalyzer)

#### 2.3.1 组件职责
- 识别和管理热点站点
- 计算站点热度评分
- 为预测策略提供热点信息

#### 2.3.2 核心接口设计
```csharp
public interface IHotspotAnalyzer
{
    /// <summary>
    /// 获取站点热度评分
    /// </summary>
    Task<double> GetStopHotnessAsync(int stopId);
    
    /// <summary>
    /// 获取线路的热点站点列表
    /// </summary>
    Task<List<int>> GetHotStopsAsync(int lineId);
    
    /// <summary>
    /// 刷新热点分析缓存
    /// </summary>
    Task RefreshHotspotAnalysisAsync();
    
    /// <summary>
    /// 记录站点查询（用于热度计算）
    /// </summary>
    Task RecordStopQueryAsync(int stopId);
}
```

### 2.4 缓存管理器 (CacheManager)

#### 2.4.1 分层缓存架构
```
L1缓存 (内存)     L2缓存 (Redis)      L3存储 (数据库)
┌─────────────┐   ┌─────────────┐    ┌─────────────┐
│   热点数据   │   │   温数据     │    │   冷数据     │
│   30秒TTL   │   │  2-5分钟TTL │    │   持久存储   │
│   高命中率   │   │   中命中率   │    │   低访问频率 │
└─────────────┘   └─────────────┘    └─────────────┘
```

#### 2.4.2 核心接口设计
```csharp
public interface ICacheManager
{
    /// <summary>
    /// 分层缓存获取
    /// </summary>
    Task<T?> GetAsync<T>(string key, CacheLevel level = CacheLevel.Auto) where T : class;
    
    /// <summary>
    /// 分层缓存设置
    /// </summary>
    Task SetAsync<T>(string key, T value, CacheLevel level, TimeSpan? expiry = null);
    
    /// <summary>
    /// 批量缓存预测结果
    /// </summary>
    Task CachePredictionsAsync(List<StopPrediction> predictions);
    
    /// <summary>
    /// 清理过期缓存
    /// </summary>
    Task CleanupExpiredCacheAsync();
}

public enum CacheLevel
{
    Hot = 3,    // L1内存 + L2Redis
    Warm = 2,   // L2Redis
    Cold = 1,   // L3数据库
    Auto = 0    // 自动判断
}
```

## 3. 数据库设计

### 3.1 核心业务表
```sql
-- 站点间运行时间表
CREATE TABLE station_travel_times (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    direction INTEGER NOT NULL,
    from_stop_id INTEGER NOT NULL,
    to_stop_id INTEGER NOT NULL,
    base_travel_time_seconds INTEGER NOT NULL,
    confidence_score DECIMAL(5,2) DEFAULT 100,
    sample_count INTEGER DEFAULT 0,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);

-- 预测结果存储表
CREATE TABLE vehicle_arrival_predictions (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id INTEGER NOT NULL,
    stop_id INTEGER NOT NULL,
    line_id INTEGER NOT NULL,
    estimated_arrival_time TIMESTAMPTZ NOT NULL,
    gps_base_time TIMESTAMPTZ NOT NULL,
    calculated_at TIMESTAMPTZ DEFAULT NOW(),
    confidence_score DECIMAL(5,2) DEFAULT 100,
    expires_at TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- 时间调整因子表
CREATE TABLE travel_time_factors (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    from_stop_id INTEGER NOT NULL,
    to_stop_id INTEGER NOT NULL,
    time_period VARCHAR(20) NOT NULL,
    day_type VARCHAR(20) NOT NULL,
    adjustment_factor DECIMAL(5,3) DEFAULT 1.000,
    confidence DECIMAL(5,2) DEFAULT 0,
    sample_count INTEGER DEFAULT 0,
    UNIQUE(line_id, from_stop_id, to_stop_id, time_period, day_type)
);
```

### 3.2 索引优化策略
```sql
-- 预测查询优化索引
CREATE INDEX idx_predictions_stop_active ON vehicle_arrival_predictions(stop_id, is_active, expires_at);
CREATE INDEX idx_predictions_vehicle_active ON vehicle_arrival_predictions(vehicle_id, is_active);

-- 时间表查询优化索引
CREATE INDEX idx_travel_times_route ON station_travel_times(line_id, direction, from_stop_id, to_stop_id);
CREATE INDEX idx_travel_factors_lookup ON travel_time_factors(line_id, from_stop_id, to_stop_id, time_period, day_type);
```

## 4. API接口设计

### 4.1 预测查询接口
```csharp
[ApiController]
[Route("api/prediction")]
public class PredictionController : ControllerBase
{
    /// <summary>
    /// 获取站点的所有车辆到站预测
    /// </summary>
    [HttpGet("stop/{stopId}")]
    public async Task<ActionResult<List<VehiclePrediction>>> GetStopPredictions(int stopId)
    {
        var predictions = await _smartPredictor.PredictStopArrivalsAsync(stopId);
        return Ok(predictions);
    }
    
    /// <summary>
    /// 获取车辆到指定站点的预测
    /// </summary>
    [HttpGet("vehicle/{vehicleId}/stop/{stopId}")]
    public async Task<ActionResult<StopPrediction>> GetVehicleStopPrediction(int vehicleId, int stopId)
    {
        var predictions = await _smartPredictor.PredictMultipleStopsAsync(vehicleId, new[] { stopId });
        return Ok(predictions.FirstOrDefault());
    }
    
    /// <summary>
    /// 获取车辆的下一站预测
    /// </summary>
    [HttpGet("vehicle/{vehicleId}/next")]
    public async Task<ActionResult<NextStopPrediction>> GetNextStopPrediction(int vehicleId)
    {
        var prediction = await _smartPredictor.PredictNextStopAsync(vehicleId);
        return Ok(prediction);
    }
}
```

### 4.2 管理接口
```csharp
[ApiController]
[Route("api/admin/timetable")]
public class TimetableManagementController : ControllerBase
{
    /// <summary>
    /// 生成线路时间表
    /// </summary>
    [HttpPost("generate/{lineId}")]
    public async Task<ActionResult<TimetableGenerationResult>> GenerateTimetable(
        int lineId, [FromBody] TimetableGenerationOptions options)
    {
        var result = await _timetableManager.GenerateTimetableAsync(lineId, options);
        return Ok(result);
    }
    
    /// <summary>
    /// 获取时间表质量报告
    /// </summary>
    [HttpGet("quality/{lineId}")]
    public async Task<ActionResult<TimetableQualityReport>> GetQualityReport(int lineId)
    {
        var report = await _timetableManager.AssessTimetableQualityAsync(lineId);
        return Ok(report);
    }
}
```

## 5. 部署架构

### 5.1 微服务部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  prediction-api:
    image: bus-prediction-api:latest
    replicas: 3
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=...
      - Redis__ConnectionString=...
    
  realtime-api:
    image: bus-realtime-api:latest
    replicas: 2
    
  aggregation-api:
    image: bus-aggregation-api:latest
    replicas: 2
    
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bus_system
      - POSTGRES_USER=bus_user
      - POSTGRES_PASSWORD=...
    
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
```

### 5.2 监控和日志
```yaml
  prometheus:
    image: prom/prometheus
    
  grafana:
    image: grafana/grafana
    
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.0.0
    
  kibana:
    image: docker.elastic.co/kibana/kibana:8.0.0
```

这个架构设计为新会话的实施提供了清晰的技术蓝图和实现指导。
