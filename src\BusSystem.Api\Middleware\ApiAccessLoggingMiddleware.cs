using System.Diagnostics;
using System.Security.Claims;
using System.Text;
using BusSystem.Core.Interfaces.Services;
using static BusSystem.Core.Interfaces.Services.IApiKeyService;

namespace BusSystem.Api.Middleware;

/// <summary>
/// API访问日志中间件
/// 自动记录所有API访问行为，用于审计和统计分析
/// </summary>
public class ApiAccessLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiAccessLoggingMiddleware> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public ApiAccessLoggingMiddleware(
        RequestDelegate next,
        ILogger<ApiAccessLoggingMiddleware> logger,
        IServiceScopeFactory serviceScopeFactory)
    {
        _next = next;
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 只记录API请求（以/api开头的路径）
        if (!context.Request.Path.StartsWithSegments("/api"))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var originalBodyStream = context.Response.Body;
        
        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;

        var requestBody = await ReadRequestBodyAsync(context.Request);
        
        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            
            // 恢复原始响应流
            await responseBody.CopyToAsync(originalBodyStream);
            context.Response.Body = originalBodyStream;

            // 异步记录访问日志（不阻塞请求）
            _ = Task.Run(async () =>
            {
                try
                {
                    await LogApiAccessAsync(context, requestBody, responseBody, stopwatch.ElapsedMilliseconds);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "记录API访问日志失败");
                }
            });
        }
    }

    private async Task<string> ReadRequestBodyAsync(HttpRequest request)
    {
        try
        {
            if (request.ContentLength == null || request.ContentLength == 0)
            {
                return string.Empty;
            }

            request.EnableBuffering();
            var buffer = new byte[Convert.ToInt32(request.ContentLength)];
            await request.Body.ReadAsync(buffer, 0, buffer.Length);
            request.Body.Position = 0;

            var requestBody = Encoding.UTF8.GetString(buffer);
            
            // 限制请求体长度，避免日志过大
            return requestBody.Length > 1000 ? requestBody.Substring(0, 1000) + "..." : requestBody;
        }
        catch
        {
            return string.Empty;
        }
    }

    private async Task LogApiAccessAsync(HttpContext context, string requestBody, MemoryStream responseBody, long responseTimeMs)
    {
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var apiKeyService = scope.ServiceProvider.GetService<IApiKeyService>();
            
            if (apiKeyService == null) return;

            // 获取API Key信息
            var apiKeyId = GetApiKeyId(context);
            var apiKeyHash = GetApiKeyHash(context);

            // 获取响应内容
            responseBody.Position = 0;
            var responseContent = await new StreamReader(responseBody).ReadToEndAsync();
            var responseSize = responseContent.Length;

            // 创建访问日志记录
            var accessLog = new ApiAccessLog
            {
                ApiKeyId = apiKeyId,
                ApiKeyHash = apiKeyHash,
                Endpoint = context.Request.Path.Value ?? string.Empty,
                HttpMethod = context.Request.Method,
                ClientIp = GetClientIp(context),
                UserAgent = context.Request.Headers["User-Agent"].FirstOrDefault() ?? string.Empty,
                RequestSize = requestBody.Length,
                ResponseSize = responseSize,
                ResponseTimeMs = (int)responseTimeMs,
                StatusCode = context.Response.StatusCode,
                ErrorMessage = context.Response.StatusCode >= 400 ? GetErrorMessage(responseContent) : null,
                CreatedAt = DateTime.UtcNow
            };

            // 记录访问日志
            await apiKeyService.LogApiAccessAsync(accessLog);

            // 记录结构化日志
            _logger.LogInformation("API访问: {Method} {Endpoint} - {StatusCode} ({ResponseTime}ms) - API Key: {ApiKeyId}",
                context.Request.Method,
                context.Request.Path,
                context.Response.StatusCode,
                responseTimeMs,
                apiKeyId?.ToString() ?? "None");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理API访问日志时发生异常");
        }
    }

    private static int? GetApiKeyId(HttpContext context)
    {
        var apiKeyIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
        return apiKeyIdClaim != null && int.TryParse(apiKeyIdClaim.Value, out var id) ? id : null;
    }

    private static string? GetApiKeyHash(HttpContext context)
    {
        // 从请求头获取API Key并计算哈希
        var apiKey = GetApiKeyFromRequest(context.Request);
        if (string.IsNullOrWhiteSpace(apiKey)) return null;

        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(apiKey));
        return Convert.ToHexString(hashBytes).ToLower();
    }

    private static string? GetApiKeyFromRequest(HttpRequest request)
    {
        // 优先从 X-API-Key 头获取
        if (request.Headers.TryGetValue("X-API-Key", out var apiKeyHeader))
        {
            return apiKeyHeader.FirstOrDefault();
        }

        // 其次从 Authorization 头获取
        if (request.Headers.TryGetValue("Authorization", out var authHeader))
        {
            var authValue = authHeader.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(authValue) && authValue.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                return authValue.Substring("Bearer ".Length).Trim();
            }
        }

        // 最后从查询参数获取
        if (request.Query.TryGetValue("api_key", out var queryApiKey))
        {
            return queryApiKey.FirstOrDefault();
        }

        return null;
    }

    private static string GetClientIp(HttpContext context)
    {
        // 检查 X-Forwarded-For 头
        if (context.Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedFor))
        {
            var ip = forwardedFor.FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim();
            if (!string.IsNullOrWhiteSpace(ip))
            {
                return ip;
            }
        }

        // 检查 X-Real-IP 头
        if (context.Request.Headers.TryGetValue("X-Real-IP", out var realIp))
        {
            var ip = realIp.FirstOrDefault()?.Trim();
            if (!string.IsNullOrWhiteSpace(ip))
            {
                return ip;
            }
        }

        // 使用连接的远程IP
        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private static string? GetErrorMessage(string responseContent)
    {
        try
        {
            // 尝试从响应内容中提取错误信息
            if (string.IsNullOrWhiteSpace(responseContent)) return null;

            // 限制错误信息长度
            return responseContent.Length > 500 ? responseContent.Substring(0, 500) + "..." : responseContent;
        }
        catch
        {
            return null;
        }
    }
}

// ApiAccessLog类已在BusSystem.Core.Services.IApiKeyService中定义
