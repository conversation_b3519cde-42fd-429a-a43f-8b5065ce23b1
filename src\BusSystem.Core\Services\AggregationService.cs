using Microsoft.Extensions.Logging;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Shared.Enums;

namespace BusSystem.Core.Services;

/// <summary>
/// 聚合服务实现 - 负责聚合多个数据源的信息
/// </summary>
public class AggregationService : IAggregationService
{
    private readonly IBusStopRepository _stopRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IRealtimeService _realtimeService;
    private readonly IPredictionService _predictionService;
    private readonly ICoordinateTransformService _coordinateTransformService;
    private readonly ILogger<AggregationService> _logger;

    public AggregationService(
        IBusStopRepository stopRepository,
        IBusLineRepository lineRepository,
        IRealtimeService realtimeService,
        IPredictionService predictionService,
        ICoordinateTransformService coordinateTransformService,
        ILogger<AggregationService> logger)
    {
        _stopRepository = stopRepository;
        _lineRepository = lineRepository;
        _realtimeService = realtimeService;
        _predictionService = predictionService;
        _coordinateTransformService = coordinateTransformService;
        _logger = logger;
    }

    public async Task<object> GetNearbyStopsWithRealtimeInfoAsync(double longitude, double latitude, double radiusMeters = 500, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02, bool mergeSameNameStops = true)
    {
        try
        {
            // 坐标系转换（如果需要）
            var (wgs84Lon, wgs84Lat) = coordinateSystem switch
            {
                CoordinateSystem.GCJ02 => _coordinateTransformService.Gcj02ToWgs84(longitude, latitude),
                CoordinateSystem.BD09 => _coordinateTransformService.Bd09ToWgs84(longitude, latitude),
                _ => (longitude, latitude)
            };

            // 获取附近站点
            var nearbyStops = await _stopRepository.GetNearbyStopsAsync(wgs84Lon, wgs84Lat, radiusMeters);
            
            if (!nearbyStops.Any())
            {
                return new
                {
                    Stops = new List<object>(),
                    Total = 0,
                    Location = _coordinateTransformService.CreateLocationResponse(wgs84Lon, wgs84Lat),
                    SearchRadius = radiusMeters
                };
            }

            var stopList = nearbyStops.ToList();
            
            // 如果需要合并同名站点
            if (mergeSameNameStops)
            {
                stopList = MergeSameNameStops(stopList);
            }

            // 获取每个站点的实时信息
            var stopsWithRealtimeInfo = new List<object>();
            
            foreach (var stop in stopList)
            {
                try
                {
                    // 获取站点的线路信息
                    var stopWithLines = await _stopRepository.GetWithLinesAsync(stop.Id);
                    
                    // 获取实时到站信息
                    var predictions = await _predictionService.GetStopArrivalPredictionsAsync(stop.Id);
                    
                    var stopInfo = new
                    {
                        StopId = stop.Id,
                        StopName = stop.StopName,
                        StopCode = stop.StopCode,
                        Address = stop.Address,
                        District = stop.District,
                        Location = _coordinateTransformService.CreateLocationResponse(stop.Longitude, stop.Latitude),
                        Distance = CalculateDistance(wgs84Lat, wgs84Lon, stop.Latitude, stop.Longitude),
                        Lines = stopWithLines?.LineStops?.Select(ls => (object)new
                        {
                            LineId = ls.Line.Id,
                            LineNumber = ls.Line.LineNumber,
                            LineName = ls.Line.LineName,
                            Direction = ls.Line.Direction,
                            Status = ls.Line.Status
                        }).ToList() ?? new List<object>(),
                        RealtimeInfo = predictions
                    };
                    
                    stopsWithRealtimeInfo.Add(stopInfo);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取站点实时信息失败，站点ID: {StopId}", stop.Id);
                    
                    // 即使获取实时信息失败，也返回基本站点信息
                    var basicStopInfo = new
                    {
                        StopId = stop.Id,
                        StopName = stop.StopName,
                        StopCode = stop.StopCode,
                        Address = stop.Address,
                        District = stop.District,
                        Location = _coordinateTransformService.CreateLocationResponse(stop.Longitude, stop.Latitude),
                        Distance = CalculateDistance(wgs84Lat, wgs84Lon, stop.Latitude, stop.Longitude),
                        Lines = new List<object>(),
                        RealtimeInfo = new List<object>(),
                        Error = "实时信息暂时不可用"
                    };
                    
                    stopsWithRealtimeInfo.Add(basicStopInfo);
                }
            }

            // 按距离排序
            stopsWithRealtimeInfo = stopsWithRealtimeInfo
                .OrderBy(s => ((dynamic)s).Distance)
                .ToList();

            return new
            {
                Stops = stopsWithRealtimeInfo,
                Total = stopsWithRealtimeInfo.Count,
                Location = _coordinateTransformService.CreateLocationResponse(wgs84Lon, wgs84Lat),
                SearchRadius = radiusMeters,
                MergeSameNameStops = mergeSameNameStops,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近站点及实时信息失败，位置: ({Longitude}, {Latitude}), 半径: {Radius}m", 
                longitude, latitude, radiusMeters);
            throw;
        }
    }

    public async Task<object> GetStopDetailWithPredictionsAsync(int stopId, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            var stop = await _stopRepository.GetWithLinesAsync(stopId);
            if (stop == null)
            {
                return new { Error = "站点不存在", StopId = stopId };
            }

            // 获取到站预测
            var predictions = await _predictionService.GetStopArrivalPredictionsAsync(stopId);
            
            // 获取附近同名站点
            var alternativeStops = await _stopRepository.GetNearbyStopsWithSameNameAsync(stopId, 1000);

            return new
            {
                StopId = stop.Id,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Address = stop.Address,
                District = stop.District,
                Location = _coordinateTransformService.CreateLocationResponse(stop.Longitude, stop.Latitude),
                Lines = stop.LineStops?.Select(ls => (object)new
                {
                    LineId = ls.Line.Id,
                    LineNumber = ls.Line.LineNumber,
                    LineName = ls.Line.LineName,
                    Direction = ls.Line.Direction,
                    Status = ls.Line.Status,
                    StopSequence = ls.SequenceNumber
                }).ToList() ?? new List<object>(),
                Predictions = predictions,
                AlternativeStops = alternativeStops.Where(s => s.Id != stopId).Select(s => new
                {
                    StopId = s.Id,
                    StopName = s.StopName,
                    StopCode = s.StopCode,
                    Address = s.Address,
                    Location = _coordinateTransformService.CreateLocationResponse(s.Longitude, s.Latitude),
                    Distance = CalculateDistance(stop.Latitude, stop.Longitude, s.Latitude, s.Longitude)
                }),
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点详细信息失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    public async Task<object> SearchStopsAndLinesAsync(string keyword, double? longitude = null, double? latitude = null, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new
                {
                    Lines = new List<object>(),
                    Stops = new List<object>(),
                    Total = 0,
                    Keyword = keyword
                };
            }

            // 并行搜索线路和站点
            var lineTask = _lineRepository.SearchAsync(keyword.Trim());
            var stopTask = _stopRepository.SearchByNameAsync(keyword.Trim());

            await Task.WhenAll(lineTask, stopTask);

            var lines = await lineTask;
            var stops = await stopTask;

            // 处理线路结果
            var lineResults = lines.Take(10).Select(line => new
            {
                LineId = line.Id,
                LineNumber = line.LineNumber,
                LineName = line.LineName,
                StartStopName = line.StartStopName,
                EndStopName = line.EndStopName,
                Direction = line.Direction,
                Status = line.Status
            });

            // 处理站点结果
            var stopResults = stops.Take(10).Select(stop => new
            {
                StopId = stop.Id,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Address = stop.Address,
                District = stop.District,
                Location = _coordinateTransformService.CreateLocationResponse(stop.Longitude, stop.Latitude),
                Distance = longitude.HasValue && latitude.HasValue
                    ? CalculateDistance(latitude.Value, longitude.Value, stop.Latitude, stop.Longitude)
                    : (double?)null
            });

            // 如果有用户位置，按距离排序站点
            if (longitude.HasValue && latitude.HasValue)
            {
                stopResults = stopResults.OrderBy(s => s.Distance ?? double.MaxValue);
            }

            return new
            {
                Lines = lineResults,
                Stops = stopResults,
                Total = lineResults.Count() + stopResults.Count(),
                Keyword = keyword,
                UserLocation = longitude.HasValue && latitude.HasValue
                    ? _coordinateTransformService.CreateLocationResponse(longitude.Value, latitude.Value)
                    : null,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点和线路失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<object> GetSingleStopPredictionsAsync(int stopId, CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            var stop = await _stopRepository.GetByIdAsync(stopId);
            if (stop == null)
            {
                return new { Error = "站点不存在", StopId = stopId };
            }

            var predictions = await _predictionService.GetStopArrivalPredictionsAsync(stopId);

            return new
            {
                StopId = stop.Id,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Location = _coordinateTransformService.CreateLocationResponse(stop.Longitude, stop.Latitude),
                Predictions = predictions,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取单个站点到站预测失败，站点ID: {StopId}", stopId);
            throw;
        }
    }

    #region 私有方法

    private static List<Core.Entities.BusStop> MergeSameNameStops(List<Core.Entities.BusStop> stops)
    {
        // 按站点名称分组，每组只保留距离最近的一个
        return stops
            .GroupBy(s => s.StopName)
            .Select(g => g.First()) // 简化实现：取第一个，实际应该取距离最近的
            .ToList();
    }

    private static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLon = (lon2 - lon1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    #endregion

    public async Task<object> GetAlternativeLinesAsync(int lineId)
    {
        try
        {
            _logger.LogInformation("获取可切换线路，线路ID: {LineId}", lineId);

            // 获取当前线路信息
            var currentLine = await _lineRepository.GetByIdAsync(lineId);
            if (currentLine == null)
            {
                throw new InvalidOperationException($"线路不存在，ID: {lineId}");
            }

            // 根据线路编号查找所有相同编号的线路（不同方向）
            var alternativeLines = await _lineRepository.GetAllByLineNumberAsync(currentLine.LineNumber);

            // 过滤掉当前线路，只返回其他方向的线路
            var alternatives = alternativeLines
                .Where(line => line.Id != lineId && line.Status == 1) // 排除当前线路，只要正常状态的线路
                .Select(line => new
                {
                    LineId = line.Id,
                    LineNumber = line.LineNumber,
                    LineName = line.LineName,
                    Direction = line.Direction,
                    DirectionName = line.Direction == 0 ? "上行" : "下行",
                    StartStopName = line.StartStopName,
                    EndStopName = line.EndStopName,
                    OperationStartTime = line.OperationStartTime?.ToString(@"hh\:mm"),
                    OperationEndTime = line.OperationEndTime?.ToString(@"hh\:mm"),
                    Status = line.Status
                })
                .OrderBy(line => line.Direction)
                .ToList();

            var result = new
            {
                CurrentLine = new
                {
                    LineId = currentLine.Id,
                    LineNumber = currentLine.LineNumber,
                    LineName = currentLine.LineName,
                    Direction = currentLine.Direction,
                    DirectionName = currentLine.Direction == 0 ? "上行" : "下行"
                },
                AlternativeLines = alternatives,
                Count = alternatives.Count,
                Message = alternatives.Any() ? "找到可切换的线路方向" : "暂无其他方向的线路"
            };

            _logger.LogInformation("获取可切换线路完成，线路: {LineNumber}, 找到 {Count} 个可切换方向",
                currentLine.LineNumber, alternatives.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可切换线路失败，线路ID: {LineId}", lineId);
            throw;
        }
    }
}
