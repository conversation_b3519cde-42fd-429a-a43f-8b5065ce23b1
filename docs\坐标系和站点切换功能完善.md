# 坐标系转换和站点切换功能完善

## 功能概述

本次更新完善了两个重要功能：
1. **坐标系转换功能** - 在所有返回坐标的接口中集成坐标系参数支持
2. **站点切换功能** - 支持同名站点之间的切换操作

## 1. 坐标系转换功能

### 1.1 设计思路

- **后端统一存储**: 数据库中统一使用WGS84坐标系存储所有坐标数据
- **前端按需转换**: 前端根据使用的地图类型请求对应的坐标系
- **服务层处理**: 坐标转换逻辑在服务层实现，保持架构一致性

### 1.2 支持的坐标系

- **WGS84**: GPS原始坐标系，国际标准坐标系
- **GCJ02**: 国测局坐标系（火星坐标），适用于高德地图、腾讯地图
- **BD09**: 百度坐标系，适用于百度地图

### 1.3 更新的接口

#### 首页附近站点接口
```
GET /api/home/<USER>
```

**参数说明：**
- `mergeSameNameStops`: 是否合并同名站点（默认true）
  - `true`: 合并同名站点，选择距离最近的作为主站点，并提供切换信息
  - `false`: 分别返回所有站点，包括同名站点

#### 站点详情接口
```
GET /api/home/<USER>/{stopId}/detail?coordinateSystem={system}
```

#### 搜索接口
```
GET /api/home/<USER>
```

#### 单站点预测接口（新增）
```
GET /api/home/<USER>/{stopId}/predictions?coordinateSystem={system}
```

### 1.4 使用示例

```javascript
// 高德地图使用GCJ02坐标系，合并同名站点（推荐）
const response = await fetch('/api/home/<USER>');

// 百度地图使用BD09坐标系，不合并同名站点
const response = await fetch('/api/home/<USER>');
```

### 1.5 合并同名站点的响应示例

**启用合并时（mergeSameNameStops=true）：**
```json
{
  "success": true,
  "data": {
    "stops": [
      {
        "stopId": 123,
        "stopName": "天安门东",
        "stopCode": "BJ001001",
        "location": { "longitude": 116.3974, "latitude": 39.9093 },
        "distance": 85,
        "hasAlternatives": true,
        "alternativeStops": [
          {
            "stopId": 456,
            "stopCode": "BJ001002",
            "distance": 95
          }
        ],
        "lines": [...]
      }
    ]
  }
}
```

**不合并时（mergeSameNameStops=false）：**
```json
{
  "success": true,
  "data": {
    "stops": [
      {
        "stopId": 123,
        "stopName": "天安门东",
        "distance": 85,
        "lines": [...]
      },
      {
        "stopId": 456,
        "stopName": "天安门东",
        "distance": 95,
        "lines": [...]
      }
    ]
  }
}
```

## 2. 站点切换功能

### 2.1 业务场景

- 公交线路有上行和下行方向
- 同一个站点在道路两边各有一个，站点名称相同但ID不同
- 用户在站点信息页面需要能够切换到对面的站点

### 2.2 实现方案

#### 新增Repository方法
- `GetStopsByNameAsync()` - 根据站点名称获取所有同名站点
- `GetNearbyStopsWithSameNameAsync()` - 获取附近的同名站点

#### 新增Service方法
- `GetAlternativeStopsAsync()` - 获取可切换的站点列表

#### 新增API接口
```
GET /api/home/<USER>/{stopId}/alternatives
```

### 2.3 接口响应示例

```json
{
  "success": true,
  "data": [
    {
      "stopId": 456,
      "stopName": "天安门东",
      "stopCode": "BJ001002",
      "location": {
        "longitude": 116.3975,
        "latitude": 39.9094
      },
      "address": "东长安街北侧",
      "district": "东城区",
      "stopType": 1,
      "distance": 85.6
    }
  ]
}
```

### 2.4 前端使用流程

1. 用户进入站点信息页面
2. 调用 `/api/home/<USER>/{stopId}/alternatives` 获取可切换的站点
3. 如果有可切换站点，显示切换按钮
4. 用户点击切换后，使用新的stopId重新加载页面数据

## 3. 单站点到站预测功能

### 3.1 功能说明

独立出单个站点的到站预测功能，提供专门的API接口，优化数据获取效率。

### 3.2 接口定义

```
GET /api/home/<USER>/{stopId}/predictions?coordinateSystem={system}
```

### 3.3 响应示例

```json
{
  "success": true,
  "data": {
    "stopId": 123,
    "stopName": "天安门东",
    "stopCode": "BJ001001",
    "location": {
      "longitude": 116.3974,
      "latitude": 39.9093
    },
    "address": "东长安街南侧",
    "district": "东城区",
    "stopType": 1,
    "totalPredictions": 8,
    "predictions": [
      {
        "vehicleId": "BJ001",
        "lineId": 1,
        "lineNumber": "1路",
        "estimatedMinutes": 3.5,
        "estimatedArrivalTime": "2025-08-22T14:35:30Z",
        "distance": 850,
        "status": "正常运行"
      }
    ],
    "lastUpdated": "2025-08-22T14:32:00Z"
  }
}
```

## 4. 技术实现细节

### 4.1 坐标转换实现

```csharp
// 在服务层进行坐标转换
var transformedCoordinate = _coordinateTransformService.TransformForMapType(
    stop.Longitude, stop.Latitude, CoordinateSystem.WGS84, coordinateSystem);

// 返回转换后的坐标
Location = new
{
    Longitude = transformedCoordinate.longitude,
    Latitude = transformedCoordinate.latitude
}
```

### 4.2 同名站点查询实现

```csharp
// 查询附近的同名站点
public async Task<IEnumerable<BusStop>> GetNearbyStopsWithSameNameAsync(int stopId, double radiusMeters = 1000)
{
    var targetStop = await _dbSet.FirstOrDefaultAsync(x => x.Id == stopId);
    if (targetStop == null) return new List<BusStop>();

    var geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
    var queryPoint = geometryFactory.CreatePoint(new Coordinate(targetStop.Longitude, targetStop.Latitude));

    return await _dbSet
        .Where(x => x.StopName == targetStop.StopName && 
                   x.Id != stopId && 
                   x.Status == 1 &&
                   x.Location.Distance(queryPoint) <= radiusMeters)
        .OrderBy(x => x.Location.Distance(queryPoint))
        .ToListAsync();
}
```

## 5. 最佳实践建议

### 5.1 坐标系使用建议

- **高德地图、腾讯地图**: 使用GCJ02坐标系
- **百度地图**: 使用BD09坐标系
- **GPS设备、国际地图**: 使用WGS84坐标系
- **中国境外**: 无需转换，直接使用WGS84

### 5.2 站点切换建议

- 在站点信息页面检查是否有可切换站点
- 提供明显的切换按钮或提示
- 切换后保持用户的操作状态（如收藏、订阅等）

### 5.3 性能优化建议

- 批量坐标转换时使用 `BatchTransform` 方法
- 缓存同名站点查询结果
- 合理设置附近站点的搜索半径（建议1000米内）

## 6. 后续扩展

### 6.1 可能的功能扩展

- 支持更多坐标系（如CGCS2000等）
- 智能推荐最佳切换站点
- 站点切换历史记录
- 基于用户位置的智能站点推荐

### 6.2 性能优化方向

- 坐标转换结果缓存
- 地理空间索引优化
- 异步批量处理

---

**更新时间**: 2025-08-22  
**版本**: v1.0  
**状态**: 已完成
