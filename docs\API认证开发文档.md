# API认证系统开发文档

## 📋 概述

本文档详细说明了实时公交系统API认证架构的技术实现，为后续开发WEB管理后台提供完整的技术参考。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    API认证系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  Controller Layer                                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  HomeController │  │ AdminController │  ...             │
│  │  [ApiAuthorize] │  │ [ApiAuthorize]  │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  Authentication & Authorization Layer                      │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ ApiKeyAuthenticationHandler                             ││
│  │ ├─ API Key提取和验证                                    ││
│  │ ├─ 速率限制检查                                         ││
│  │ └─ 用户身份创建                                         ││
│  └─────────────────────────────────────────────────────────┘│
│  ┌─────────────────────────────────────────────────────────┐│
│  │ ApiPermissionAuthorizationHandler                       ││
│  │ ├─ 权限匹配验证                                         ││
│  │ ├─ 端点模式匹配                                         ││
│  │ └─ HTTP方法检查                                         ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ ApiKeyService                                           ││
│  │ ├─ API Key管理                                          ││
│  │ ├─ 权限验证                                             ││
│  │ ├─ 速率限制                                             ││
│  │ └─ 访问日志                                             ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Repository Layer                                           │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ ApiKeyRepository                                        ││
│  │ ├─ 数据库操作                                           ││
│  │ ├─ SQL查询优化                                          ││
│  │ └─ 事务管理                                             ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Database Layer                                             │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ PostgreSQL Tables                                       ││
│  │ ├─ api_keys (API密钥)                                   ││
│  │ ├─ api_permissions (权限定义)                           ││
│  │ ├─ api_key_permissions (权限关联)                       ││
│  │ ├─ api_access_audit (访问日志)                          ││
│  │ └─ api_rate_limits (速率限制)                           ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 🔐 认证流程

### 1. API Key认证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Auth as ApiKeyAuthenticationHandler
    participant Service as ApiKeyService
    participant Repo as ApiKeyRepository
    participant DB as 数据库

    Client->>Auth: 请求 + X-API-Key Header
    Auth->>Auth: 提取API Key
    Auth->>Auth: 计算SHA-256哈希
    Auth->>Service: ValidateApiKeyAsync(apiKey)
    Service->>Repo: GetByHashAsync(hash)
    Repo->>DB: 查询api_keys表
    DB-->>Repo: 返回API Key信息
    Repo-->>Service: 返回ApiKeyInfo
    Service->>Service: 验证激活状态、过期时间
    Service->>Service: 检查速率限制
    Service-->>Auth: 返回验证结果
    Auth->>Auth: 创建用户身份(Claims)
    Auth-->>Client: 认证成功/失败
```

### 2. 权限验证流程

```mermaid
sequenceDiagram
    participant Auth as 已认证请求
    participant AuthZ as ApiPermissionAuthorizationHandler
    participant Service as ApiKeyService
    participant Repo as ApiKeyRepository

    Auth->>AuthZ: 访问受保护资源
    AuthZ->>AuthZ: 获取API Key ID
    AuthZ->>AuthZ: 获取请求端点和HTTP方法
    AuthZ->>Service: HasPermissionAsync(keyId, endpoint, method)
    Service->>Repo: GetPermissionsAsync(keyId)
    Repo-->>Service: 返回权限列表
    Service->>Service: 权限模式匹配
    Service-->>AuthZ: 返回权限检查结果
    AuthZ-->>Auth: 允许访问/拒绝访问(403)
```

## 📊 数据库设计

### 核心表结构

#### 1. api_keys - API密钥表
```sql
CREATE TABLE api_keys (
    id SERIAL PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL,           -- 密钥名称
    api_key_hash VARCHAR(64) NOT NULL UNIQUE, -- SHA-256哈希值
    key_type VARCHAR(20) NOT NULL,            -- 类型: sync/frontend/partner/admin
    description TEXT,                         -- 描述
    owner_name VARCHAR(100),                  -- 所有者
    is_active BOOLEAN DEFAULT true,           -- 是否激活
    rate_limit_per_hour INTEGER DEFAULT 1000, -- 每小时限制
    rate_limit_per_day INTEGER DEFAULT 10000, -- 每天限制
    allowed_ips TEXT[],                       -- IP白名单
    expires_at TIMESTAMPTZ,                   -- 过期时间
    last_used_at TIMESTAMPTZ,                 -- 最后使用时间
    usage_count BIGINT DEFAULT 0,             -- 使用次数
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. api_permissions - 权限定义表
```sql
CREATE TABLE api_permissions (
    id SERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE, -- 权限名称
    endpoint_pattern VARCHAR(200) NOT NULL,       -- 端点模式
    http_methods VARCHAR(100) DEFAULT 'GET,POST,PUT,DELETE', -- HTTP方法
    description TEXT,                              -- 描述
    is_active BOOLEAN DEFAULT true,                -- 是否激活
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. api_key_permissions - 权限关联表
```sql
CREATE TABLE api_key_permissions (
    api_key_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    granted_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    granted_by VARCHAR(100),
    
    PRIMARY KEY (api_key_id, permission_id),
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES api_permissions(id) ON DELETE CASCADE
);
```

### 权限配置示例

```sql
-- 前端应用权限
INSERT INTO api_permissions VALUES
('home.nearby_stops', '/api/home/<USER>', 'GET', '附近站点查询'),
('search.all', '/api/search/all*', 'GET', '综合搜索'),
('lines.list', '/api/lines*', 'GET', '线路列表');

-- 数据同步权限
INSERT INTO api_permissions VALUES
('datasync.gps', '/api/datasync/gps*', 'POST', 'GPS数据同步'),
('datasync.status', '/api/datasync/status*', 'GET', '同步状态查询');

-- 管理权限
INSERT INTO api_permissions VALUES
('admin.api_keys', '/api/admin/api-keys*', 'GET,POST,PUT,DELETE', 'API密钥管理'),
('admin.stats', '/api/admin/stats*', 'GET', '统计信息');
```

## 🔧 核心接口

### 1. IApiKeyService - 核心服务接口

```csharp
public interface IApiKeyService
{
    // 认证相关
    Task<ApiKeyInfo?> ValidateApiKeyAsync(string apiKey);
    Task<bool> HasPermissionAsync(int apiKeyId, string endpoint, string httpMethod);
    Task<RateLimitResult> CheckRateLimitAsync(int apiKeyId);
    
    // 管理相关
    Task<ApiKeyInfo> CreateApiKeyAsync(CreateApiKeyRequest request);
    Task UpdateApiKeyAsync(int apiKeyId, UpdateApiKeyRequest request);
    Task DisableApiKeyAsync(int apiKeyId);
    Task<IEnumerable<ApiKeyInfo>> GetApiKeysAsync(string? keyType = null, bool? isActive = null);
    
    // 权限管理
    Task<IEnumerable<ApiPermission>> GetApiKeyPermissionsAsync(int apiKeyId);
    
    // 日志和统计
    Task LogApiAccessAsync(ApiAccessLog accessLog);
    Task UpdateApiKeyUsageAsync(int apiKeyId);
}
```

### 2. 数据传输对象

#### ApiKeyInfo - API密钥信息
```csharp
public class ApiKeyInfo
{
    public int Id { get; set; }
    public string KeyName { get; set; } = string.Empty;
    public string KeyType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int RateLimitPerHour { get; set; }
    public int RateLimitPerDay { get; set; }
    public string[]? AllowedIps { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public long UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<ApiPermission> Permissions { get; set; } = new();
}
```

#### CreateApiKeyRequest - 创建API密钥请求
```csharp
public class CreateApiKeyRequest
{
    public string KeyName { get; set; } = string.Empty;
    public string KeyType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public int RateLimitPerHour { get; set; } = 1000;
    public int RateLimitPerDay { get; set; } = 10000;
    public string[]? AllowedIps { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public List<string> PermissionNames { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}
```

## 🎯 权限系统

### 权限类型分类

| 类型 | 说明 | 典型权限 | 使用场景 |
|------|------|----------|----------|
| `sync` | 数据同步 | `datasync.*` | 调度平台对接 |
| `frontend` | 前端应用 | `home.*`, `search.*` | H5应用、小程序 |
| `partner` | 第三方合作 | 受限的业务接口 | 外部集成 |
| `admin` | 系统管理 | 所有权限 | 管理后台 |

### 权限匹配规则

#### 1. 通配符匹配
- `home.*` 匹配 `/api/home/<USER>/api/home/<USER>
- `datasync.*` 匹配 `/api/datasync/gps`, `/api/datasync/status` 等

#### 2. 参数化匹配
- `/api/stops/*/detail` 匹配 `/api/stops/123/detail`
- `/api/lines/{id}` 匹配 `/api/lines/456`

#### 3. HTTP方法控制
- `GET,POST` 只允许GET和POST请求
- `*` 允许所有HTTP方法

### 权限配置最佳实践

```csharp
// 创建前端应用API Key
var frontendKey = new CreateApiKeyRequest
{
    KeyName = "H5前端应用",
    KeyType = "frontend",
    Description = "用于H5前端应用的API访问",
    OwnerName = "前端开发团队",
    RateLimitPerHour = 5000,
    RateLimitPerDay = 50000,
    PermissionNames = new List<string>
    {
        "home.nearby_stops",
        "home.search", 
        "search.all",
        "lines.list",
        "stops.nearby",
        "realtime.predictions",
        "system.health"
    }
};

// 创建数据同步API Key
var syncKey = new CreateApiKeyRequest
{
    KeyName = "平台A数据同步",
    KeyType = "sync",
    Description = "平台A调度数据同步专用",
    OwnerName = "平台A技术团队",
    RateLimitPerHour = 2000,
    RateLimitPerDay = 20000,
    AllowedIps = new[] { "*************", "*********" },
    PermissionNames = new List<string>
    {
        "datasync.gps",
        "datasync.base_data",
        "datasync.status",
        "datasync.health"
    }
};
```

## 🔨 开发指南

### 1. 为新控制器添加认证

```csharp
[ApiController]
[Route("api/[controller]")]
[ApiAuthorize] // 添加统一认证
public class NewController : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult> GetData()
    {
        // 控制器方法自动受API Key保护
        return Ok();
    }
    
    [HttpPost("admin-only")]
    [ApiAuthorize("admin")] // 需要特定权限
    public async Task<ActionResult> AdminOnlyAction()
    {
        return Ok();
    }
}
```

### 2. 添加新权限

```sql
-- 1. 在数据库中添加权限定义
INSERT INTO api_permissions (permission_name, endpoint_pattern, http_methods, description) 
VALUES ('new_feature.access', '/api/new-feature*', 'GET,POST', '新功能访问权限');

-- 2. 为相应的API Key分配权限
INSERT INTO api_key_permissions (api_key_id, permission_id)
SELECT ak.id, ap.id 
FROM api_keys ak, api_permissions ap 
WHERE ak.key_type = 'frontend' AND ap.permission_name = 'new_feature.access';
```

### 3. 自定义权限验证

```csharp
[HttpGet("custom-check")]
public async Task<ActionResult> CustomPermissionCheck()
{
    // 获取当前API Key ID
    var apiKeyId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
    
    // 自定义权限检查
    var hasPermission = await _apiKeyService.HasPermissionAsync(
        apiKeyId, 
        "/api/custom/endpoint", 
        "GET"
    );
    
    if (!hasPermission)
    {
        return Forbid();
    }
    
    return Ok();
}
```

## 📈 监控和统计

### 1. 访问统计查询

```sql
-- API Key使用统计
SELECT 
    ak.key_name,
    ak.key_type,
    COUNT(aal.id) as request_count,
    AVG(aal.response_time_ms) as avg_response_time,
    COUNT(CASE WHEN aal.status_code >= 400 THEN 1 END) as error_count
FROM api_keys ak
LEFT JOIN api_access_audit aal ON ak.id = aal.api_key_id
WHERE aal.created_at >= NOW() - INTERVAL '24 hours'
GROUP BY ak.id, ak.key_name, ak.key_type
ORDER BY request_count DESC;

-- 热门接口统计
SELECT 
    endpoint,
    COUNT(*) as request_count,
    AVG(response_time_ms) as avg_response_time
FROM api_access_audit 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY endpoint
ORDER BY request_count DESC
LIMIT 10;
```

### 2. 速率限制监控

```sql
-- 当前速率限制状态
SELECT 
    ak.key_name,
    ak.rate_limit_per_hour,
    arl.request_count,
    arl.time_window
FROM api_keys ak
JOIN api_rate_limits arl ON ak.id = arl.api_key_id
WHERE arl.time_window = date_trunc('hour', NOW())
ORDER BY (arl.request_count::float / ak.rate_limit_per_hour) DESC;
```

## 🚀 部署和维护

### 1. 数据库初始化

```bash
# 执行数据库设置脚本
psql -h localhost -U bus_user -d bus_system -f scripts/setup-api-auth.sql
```

### 2. 配置验证

```bash
# 测试API认证功能
./scripts/test-api-auth.sh http://localhost:5205
```

### 3. 定期维护任务

```sql
-- 清理过期的速率限制记录（保留24小时）
DELETE FROM api_rate_limits 
WHERE time_window < NOW() - INTERVAL '24 hours';

-- 清理过期的访问日志（保留30天）
DELETE FROM api_access_audit 
WHERE created_at < NOW() - INTERVAL '30 days';

-- 更新API Key统计信息
UPDATE api_keys 
SET usage_count = (
    SELECT COUNT(*) 
    FROM api_access_audit 
    WHERE api_key_id = api_keys.id
);
```

## 🔧 故障排查

### 常见问题

1. **401 Unauthorized**
   - 检查API Key是否正确
   - 确认API Key是否激活
   - 验证API Key是否过期

2. **403 Forbidden**
   - 检查API Key权限配置
   - 确认端点模式匹配
   - 验证HTTP方法是否允许

3. **429 Too Many Requests**
   - 检查速率限制配置
   - 确认请求频率是否超限
   - 考虑调整限制或优化请求

### 调试技巧

```csharp
// 启用详细日志
builder.Logging.SetMinimumLevel(LogLevel.Debug);

// 在ApiKeyAuthenticationHandler中添加调试日志
_logger.LogDebug("API Key验证: {ApiKey}, 结果: {Result}", 
    MaskApiKey(apiKey), result);
```

## 🖥️ WEB管理后台开发指南

### 管理后台功能规划

#### 1. API密钥管理
- **密钥列表**: 显示所有API密钥，支持筛选和搜索
- **创建密钥**: 表单创建新的API密钥，选择类型和权限
- **编辑密钥**: 修改密钥信息、权限配置
- **禁用/启用**: 快速禁用或启用API密钥
- **密钥详情**: 查看密钥详细信息和使用统计

#### 2. 权限管理
- **权限列表**: 显示所有可用权限
- **权限分配**: 为API密钥分配或移除权限
- **权限模板**: 预定义权限组合，快速分配

#### 3. 使用统计
- **访问统计**: API调用次数、成功率、响应时间
- **热门接口**: 最常用的API接口排行
- **错误分析**: 错误请求统计和分析
- **实时监控**: 当前活跃的API密钥和请求

#### 4. 系统管理
- **速率限制**: 配置和监控API速率限制
- **IP白名单**: 管理IP访问控制
- **日志查看**: 查看API访问日志
- **系统设置**: 全局配置管理

### 前端技术栈建议

#### 推荐技术栈
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: Zustand 或 Redux Toolkit
- **HTTP客户端**: Axios
- **路由**: React Router v6
- **图表**: ECharts 或 Chart.js
- **构建工具**: Vite

#### 项目结构
```
web-admin/
├── src/
│   ├── components/          # 通用组件
│   │   ├── Layout/         # 布局组件
│   │   ├── Table/          # 表格组件
│   │   └── Charts/         # 图表组件
│   ├── pages/              # 页面组件
│   │   ├── ApiKeys/        # API密钥管理
│   │   ├── Permissions/    # 权限管理
│   │   ├── Statistics/     # 统计分析
│   │   └── Settings/       # 系统设置
│   ├── services/           # API服务
│   │   ├── apiKeyService.ts
│   │   ├── permissionService.ts
│   │   └── statisticsService.ts
│   ├── types/              # TypeScript类型定义
│   │   ├── apiKey.ts
│   │   ├── permission.ts
│   │   └── statistics.ts
│   ├── utils/              # 工具函数
│   │   ├── request.ts      # HTTP请求封装
│   │   ├── auth.ts         # 认证工具
│   │   └── format.ts       # 格式化工具
│   └── hooks/              # 自定义Hooks
│       ├── useApiKeys.ts
│       ├── usePermissions.ts
│       └── useStatistics.ts
├── public/
└── package.json
```

### 核心API接口

#### 1. API密钥管理接口
```typescript
// 获取API密钥列表
GET /api/admin/api-keys?keyType=frontend&isActive=true&page=1&size=10

// 创建API密钥
POST /api/admin/api-keys
{
  "keyName": "新应用",
  "keyType": "frontend",
  "description": "描述",
  "ownerName": "所有者",
  "rateLimitPerHour": 5000,
  "permissionNames": ["home.*", "search.*"]
}

// 更新API密钥
PUT /api/admin/api-keys/{id}
{
  "description": "更新后的描述",
  "rateLimitPerHour": 10000
}

// 禁用API密钥
DELETE /api/admin/api-keys/{id}

// 获取API密钥权限
GET /api/admin/api-keys/{id}/permissions

// 更新API密钥权限
PUT /api/admin/api-keys/{id}/permissions
{
  "permissionNames": ["home.*", "search.*", "lines.list"]
}
```

#### 2. 统计分析接口
```typescript
// 获取使用统计
GET /api/admin/stats/usage?startDate=2025-08-01&endDate=2025-08-31

// 获取热门接口
GET /api/admin/stats/popular-endpoints?limit=10

// 获取错误统计
GET /api/admin/stats/errors?period=7d

// 获取实时监控数据
GET /api/admin/stats/realtime
```

### 前端开发示例

#### 1. API密钥列表组件
```typescript
// src/pages/ApiKeys/ApiKeyList.tsx
import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Modal } from 'antd';
import { useApiKeys } from '../../hooks/useApiKeys';
import type { ApiKeyInfo } from '../../types/apiKey';

const ApiKeyList: React.FC = () => {
  const { apiKeys, loading, fetchApiKeys, deleteApiKey } = useApiKeys();
  const [selectedKey, setSelectedKey] = useState<ApiKeyInfo | null>(null);

  const columns = [
    {
      title: '密钥名称',
      dataIndex: 'keyName',
      key: 'keyName',
    },
    {
      title: '类型',
      dataIndex: 'keyType',
      key: 'keyType',
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '速率限制',
      dataIndex: 'rateLimitPerHour',
      key: 'rateLimitPerHour',
      render: (limit: number) => `${limit}/小时`,
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsedAt',
      key: 'lastUsedAt',
      render: (date: string) => date ? new Date(date).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: ApiKeyInfo) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Button type="link" onClick={() => handleViewPermissions(record)}>
            权限
          </Button>
          <Button
            type="link"
            danger
            onClick={() => handleDelete(record)}
          >
            禁用
          </Button>
        </Space>
      ),
    },
  ];

  const handleEdit = (apiKey: ApiKeyInfo) => {
    setSelectedKey(apiKey);
    // 打开编辑模态框
  };

  const handleViewPermissions = (apiKey: ApiKeyInfo) => {
    // 查看权限详情
  };

  const handleDelete = (apiKey: ApiKeyInfo) => {
    Modal.confirm({
      title: '确认禁用',
      content: `确定要禁用API密钥 "${apiKey.keyName}" 吗？`,
      onOk: () => deleteApiKey(apiKey.id),
    });
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={() => setSelectedKey({} as ApiKeyInfo)}>
          创建API密钥
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={apiKeys}
        loading={loading}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />
    </div>
  );
};

const getTypeColor = (type: string) => {
  const colors = {
    frontend: 'blue',
    sync: 'green',
    partner: 'orange',
    admin: 'red',
  };
  return colors[type as keyof typeof colors] || 'default';
};

export default ApiKeyList;
```

#### 2. 自定义Hook示例
```typescript
// src/hooks/useApiKeys.ts
import { useState, useEffect } from 'react';
import { message } from 'antd';
import { apiKeyService } from '../services/apiKeyService';
import type { ApiKeyInfo, CreateApiKeyRequest } from '../types/apiKey';

export const useApiKeys = () => {
  const [apiKeys, setApiKeys] = useState<ApiKeyInfo[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchApiKeys = async (params?: {
    keyType?: string;
    isActive?: boolean;
    page?: number;
    size?: number;
  }) => {
    setLoading(true);
    try {
      const response = await apiKeyService.getApiKeys(params);
      setApiKeys(response.data.apiKeys);
    } catch (error) {
      message.error('获取API密钥列表失败');
    } finally {
      setLoading(false);
    }
  };

  const createApiKey = async (request: CreateApiKeyRequest) => {
    try {
      await apiKeyService.createApiKey(request);
      message.success('API密钥创建成功');
      fetchApiKeys();
    } catch (error) {
      message.error('创建API密钥失败');
    }
  };

  const deleteApiKey = async (id: number) => {
    try {
      await apiKeyService.deleteApiKey(id);
      message.success('API密钥已禁用');
      fetchApiKeys();
    } catch (error) {
      message.error('禁用API密钥失败');
    }
  };

  useEffect(() => {
    fetchApiKeys();
  }, []);

  return {
    apiKeys,
    loading,
    fetchApiKeys,
    createApiKey,
    deleteApiKey,
  };
};
```

#### 3. API服务封装
```typescript
// src/services/apiKeyService.ts
import { request } from '../utils/request';
import type { ApiKeyInfo, CreateApiKeyRequest, UpdateApiKeyRequest } from '../types/apiKey';

export const apiKeyService = {
  // 获取API密钥列表
  getApiKeys: (params?: {
    keyType?: string;
    isActive?: boolean;
    page?: number;
    size?: number;
  }) => {
    return request.get<{
      apiKeys: ApiKeyInfo[];
      total: number;
    }>('/api/admin/api-keys', { params });
  },

  // 创建API密钥
  createApiKey: (data: CreateApiKeyRequest) => {
    return request.post<ApiKeyInfo>('/api/admin/api-keys', data);
  },

  // 更新API密钥
  updateApiKey: (id: number, data: UpdateApiKeyRequest) => {
    return request.put(`/api/admin/api-keys/${id}`, data);
  },

  // 禁用API密钥
  deleteApiKey: (id: number) => {
    return request.delete(`/api/admin/api-keys/${id}`);
  },

  // 获取API密钥权限
  getApiKeyPermissions: (id: number) => {
    return request.get(`/api/admin/api-keys/${id}/permissions`);
  },

  // 更新API密钥权限
  updateApiKeyPermissions: (id: number, permissionNames: string[]) => {
    return request.put(`/api/admin/api-keys/${id}/permissions`, {
      permissionNames,
    });
  },
};
```

### 开发部署建议

#### 1. 开发环境配置
```json
// package.json
{
  "name": "bus-system-admin",
  "version": "1.0.0",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "antd": "^5.12.0",
    "axios": "^1.6.0",
    "react-router-dom": "^6.20.0",
    "zustand": "^4.4.0",
    "echarts": "^5.4.0",
    "dayjs": "^1.11.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.2.0",
    "typescript": "^5.3.0",
    "vite": "^5.0.0"
  }
}
```

#### 2. 代理配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:5205',
        changeOrigin: true,
      },
    },
  },
});
```

---

**文档维护**: 系统开发团队
**最后更新**: 2025-08-25
**版本**: v1.0
