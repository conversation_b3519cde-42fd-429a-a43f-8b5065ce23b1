using Microsoft.Extensions.Logging;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Shared.Constants;

namespace BusSystem.Core.Services;

/// <summary>
/// 实时数据服务实现
/// </summary>
public class RealtimeService : IRealtimeService
{
    private readonly IVehiclePositionRepository _positionRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IRedisService _redisService;
    private readonly INotificationService _notificationService;
    private readonly ILogger<RealtimeService> _logger;

    public RealtimeService(
        IVehiclePositionRepository positionRepository,
        IVehicleRepository vehicleRepository,
        IRedisService redisService,
        INotificationService notificationService,
        ILogger<RealtimeService> logger)
    {
        _positionRepository = positionRepository;
        _vehicleRepository = vehicleRepository;
        _redisService = redisService;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<VehiclePosition> UpdateVehiclePositionAsync(VehiclePosition position)
    {
        try
        {
            // 验证车辆是否存在
            var vehicle = await _vehicleRepository.GetByIdAsync(position.VehicleId);
            if (vehicle == null)
            {
                throw new InvalidOperationException($"车辆不存在，ID: {position.VehicleId}");
            }

            // 设置时间戳
            if (position.Timestamp == default)
            {
                position.Timestamp = DateTime.UtcNow;
            }

            // 保存到时序数据库
            var result = await _positionRepository.AddAsync(position);

            // 缓存到Redis
            await CacheVehiclePositionAsync(result);

            // 更新Redis Geo位置
            var geoKey = $"{SystemConstants.RedisKeys.VehiclePositions}:geo";
            await _redisService.GeoAddAsync(geoKey, position.Longitude, position.Latitude, position.VehicleId.ToString());

            // 推送实时位置更新通知
            try
            {
                var positionData = new
                {
                    VehicleId = position.VehicleId,
                    LineId = vehicle.LineId,
                    Timestamp = position.Timestamp,
                    Location = new
                    {
                        Longitude = position.Longitude,
                        Latitude = position.Latitude
                    },
                    Speed = position.Speed,
                    Direction = position.Direction,
                    Status = position.Status
                };

                await _notificationService.NotifyVehiclePositionUpdateAsync(position.VehicleId, positionData);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "推送车辆位置更新通知失败，车辆ID: {VehicleId}", position.VehicleId);
                // 推送失败不影响主要功能
            }

            _logger.LogInformation("车辆位置更新成功，车辆ID: {VehicleId}, 位置: ({Longitude}, {Latitude})",
                position.VehicleId, position.Longitude, position.Latitude);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新车辆位置失败，车辆ID: {VehicleId}", position.VehicleId);
            throw;
        }
    }

    public async Task<IEnumerable<VehiclePosition>> UpdateVehiclePositionsAsync(IEnumerable<VehiclePosition> positions)
    {
        try
        {
            var positionList = positions.ToList();
            var now = DateTime.UtcNow;

            // 设置时间戳
            foreach (var position in positionList.Where(p => p.Timestamp == default))
            {
                position.Timestamp = now;
            }

            // 批量保存到时序数据库
            var results = await _positionRepository.AddRangeAsync(positionList);

            // 批量缓存到Redis和更新Geo位置
            var tasks = positionList.Select(async position =>
            {
                await CacheVehiclePositionAsync(position);
                var geoKey = $"{SystemConstants.RedisKeys.VehiclePositions}:geo";
                await _redisService.GeoAddAsync(geoKey, position.Longitude, position.Latitude, position.VehicleId.ToString());
            });

            await Task.WhenAll(tasks);

            _logger.LogInformation("批量更新车辆位置成功，数量: {Count}", positionList.Count);
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量更新车辆位置失败");
            throw;
        }
    }

    public async Task<VehiclePosition?> GetVehicleLatestPositionAsync(int vehicleId)
    {
        try
        {
            // 先尝试从Redis缓存获取
            var cachedPosition = await GetCachedVehiclePositionAsync(vehicleId);
            if (cachedPosition != null)
            {
                return cachedPosition;
            }

            // 从数据库获取
            var position = await _positionRepository.GetLatestPositionAsync(vehicleId);
            if (position != null)
            {
                // 缓存到Redis
                await CacheVehiclePositionAsync(position);
            }

            return position;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆最新位置失败，车辆ID: {VehicleId}", vehicleId);
            throw;
        }
    }

    public async Task<IEnumerable<VehiclePosition>> GetLineRealtimePositionsAsync(int lineId)
    {
        try
        {
            return await _positionRepository.GetLineVehiclePositionsAsync(lineId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路实时位置失败，线路ID: {LineId}", lineId);
            throw;
        }
    }

    public async Task<IEnumerable<VehiclePosition>> GetNearbyVehiclesAsync(double longitude, double latitude, double radiusMeters = 1000)
    {
        try
        {
            // 使用Redis Geo查询附近车辆
            var geoKey = $"{SystemConstants.RedisKeys.VehiclePositions}:geo";
            var nearbyResults = await _redisService.GeoRadiusAsync(geoKey, longitude, latitude, radiusMeters);

            if (!nearbyResults.Any())
            {
                return new List<VehiclePosition>();
            }

            // 获取这些车辆的详细位置信息
            var vehicleIds = nearbyResults.Select(r => int.Parse(r.Member)).ToList();
            return await _positionRepository.GetLatestPositionsAsync(vehicleIds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近车辆失败，位置: ({Longitude}, {Latitude}), 半径: {Radius}", 
                longitude, latitude, radiusMeters);
            throw;
        }
    }

    public async Task<IEnumerable<VehiclePosition>> GetVehicleTrajectoryAsync(int vehicleId, DateTime startTime, DateTime endTime)
    {
        try
        {
            return await _positionRepository.GetVehicleTrajectoryAsync(vehicleId, startTime, endTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆轨迹失败，车辆ID: {VehicleId}, 时间范围: {StartTime} - {EndTime}", 
                vehicleId, startTime, endTime);
            throw;
        }
    }

    public async Task CacheVehiclePositionAsync(VehiclePosition position)
    {
        try
        {
            var cacheKey = $"{SystemConstants.RedisKeys.VehiclePositions}:{position.VehicleId}";
            var expiry = TimeSpan.FromMinutes(10); // 10分钟过期
            await _redisService.SetAsync(cacheKey, position, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "缓存车辆位置失败，车辆ID: {VehicleId}", position.VehicleId);
            // 缓存失败不影响主要功能，只记录警告
        }
    }

    public async Task<VehiclePosition?> GetCachedVehiclePositionAsync(int vehicleId)
    {
        try
        {
            var cacheKey = $"{SystemConstants.RedisKeys.VehiclePositions}:{vehicleId}";
            return await _redisService.GetAsync<VehiclePosition>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取缓存车辆位置失败，车辆ID: {VehicleId}", vehicleId);
            return null;
        }
    }

    public async Task CleanupOldPositionDataAsync(TimeSpan retentionPeriod)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow - retentionPeriod;
            await _positionRepository.DeleteOldDataAsync(cutoffTime);
            
            _logger.LogInformation("清理过期位置数据完成，保留期: {RetentionPeriod}, 截止时间: {CutoffTime}", 
                retentionPeriod, cutoffTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期位置数据失败");
            throw;
        }
    }

    public async Task<object> GetVehicleRealtimeStatusAsync(int vehicleId)
    {
        try
        {
            var position = await GetVehicleLatestPositionAsync(vehicleId);
            var vehicle = await _vehicleRepository.GetWithLineAsync(vehicleId);

            if (position == null || vehicle == null)
            {
                return new { VehicleId = vehicleId, Status = "离线", LastUpdate = (DateTime?)null };
            }

            var isOnline = (DateTime.UtcNow - position.Timestamp).TotalMinutes <= 5; // 5分钟内有数据认为在线

            return new
            {
                VehicleId = vehicleId,
                VehicleNumber = vehicle.VehicleNumber,
                LineId = vehicle.LineId,
                LineName = vehicle.Line?.LineName,
                Status = isOnline ? "在线" : "离线",
                LastUpdate = position.Timestamp,
                Location = new
                {
                    Longitude = position.Longitude,
                    Latitude = position.Latitude
                },
                Speed = position.Speed,
                Direction = position.Direction,
                CurrentStopId = position.CurrentStopId,
                NextStopId = position.NextStopId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取车辆实时状态失败，车辆ID: {VehicleId}", vehicleId);
            throw;
        }
    }

    public async Task<object> GetLineRealtimeStatsAsync(int lineId)
    {
        try
        {
            var positions = await GetLineRealtimePositionsAsync(lineId);
            var vehicles = await _vehicleRepository.GetByLineIdAsync(lineId);

            var onlineVehicles = positions.Where(p => (DateTime.UtcNow - p.Timestamp).TotalMinutes <= 5).ToList();
            var totalVehicles = vehicles.Count();

            return new
            {
                LineId = lineId,
                TotalVehicles = totalVehicles,
                OnlineVehicles = onlineVehicles.Count,
                OfflineVehicles = totalVehicles - onlineVehicles.Count,
                OnlineRate = totalVehicles > 0 ? Math.Round((double)onlineVehicles.Count / totalVehicles * 100, 2) : 0,
                LastUpdate = onlineVehicles.Any() ? onlineVehicles.Max(p => p.Timestamp) : (DateTime?)null,
                AverageSpeed = onlineVehicles.Where(p => p.Speed.HasValue).Any() 
                    ? Math.Round(onlineVehicles.Where(p => p.Speed.HasValue).Average(p => p.Speed!.Value), 2) 
                    : 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取线路实时统计失败，线路ID: {LineId}", lineId);
            throw;
        }
    }
}
