using BusSystem.Shared.Models.DataSync;

namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// 数据同步服务接口
/// </summary>
public interface IDataSyncService
{
    /// <summary>
    /// 同步GPS数据（支持批量，单次最多1000条）
    /// </summary>
    /// <param name="request">GPS数据同步请求</param>
    /// <returns>同步结果</returns>
    Task<SyncDataResponse> SyncGpsDataAsync(GpsDataSyncRequest request);

    /// <summary>
    /// 同步线路数据（自动处理新增/更新）
    /// </summary>
    /// <param name="request">线路数据同步请求</param>
    /// <returns>同步结果</returns>
    Task<SyncDataResponse> SyncLineDataAsync(LineSyncRequest request);

    /// <summary>
    /// 同步站点数据（自动处理新增/更新）
    /// </summary>
    /// <param name="request">站点数据同步请求</param>
    /// <returns>同步结果</returns>
    Task<SyncDataResponse> SyncStopDataAsync(StopSyncRequest request);

    /// <summary>
    /// 同步车辆数据（自动处理新增/更新）
    /// </summary>
    /// <param name="request">车辆数据同步请求</param>
    /// <returns>同步结果</returns>
    Task<SyncDataResponse> SyncVehicleDataAsync(VehicleSyncRequest request);

    /// <summary>
    /// 同步线路站点关系（专用接口，用于增量更新）
    /// </summary>
    /// <param name="request">线路站点关系同步请求</param>
    /// <returns>同步结果</returns>
    Task<SyncDataResponse> SyncLineStopsAsync(LineStopsSyncRequest request);

    // 注意：ValidateDataSourceAsync方法已移除
    // 数据源权限验证现在通过统一的API认证系统处理
    // 使用[ApiAuthorize("datasync.xxx")]特性来控制权限
}
