using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 实时推送通知控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class NotificationController : ControllerBase
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<NotificationController> _logger;

    public NotificationController(INotificationService notificationService, ILogger<NotificationController> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }

    /// <summary>
    /// 获取WebSocket连接统计信息
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetConnectionStats()
    {
        try
        {
            var stats = await _notificationService.GetConnectionStatsAsync();
            return Ok(ApiResponse<object>.Ok(stats));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取连接统计信息失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 清理无效连接
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<ActionResult<ApiResponse>> CleanupConnections()
    {
        try
        {
            await _notificationService.CleanupInvalidConnectionsAsync();
            return Ok(ApiResponse.Ok("清理完成"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理无效连接失败");
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 广播系统通知
    /// </summary>
    /// <param name="message">通知消息</param>
    [HttpPost("broadcast")]
    public async Task<ActionResult<ApiResponse>> BroadcastNotification([FromBody] object message)
    {
        try
        {
            if (message == null)
            {
                return BadRequest(ApiResponse.Fail("消息内容不能为空"));
            }

            await _notificationService.BroadcastMessageAsync(message);
            return Ok(ApiResponse.Ok("广播发送成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "广播通知失败");
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 推送车辆位置更新（内部接口）
    /// </summary>
    /// <param name="vehicleId">车辆ID</param>
    /// <param name="positionData">位置数据</param>
    [HttpPost("vehicle/{vehicleId}/position")]
    public async Task<ActionResult<ApiResponse>> NotifyVehiclePosition(int vehicleId, [FromBody] object positionData)
    {
        try
        {
            if (positionData == null)
            {
                return BadRequest(ApiResponse.Fail("位置数据不能为空"));
            }

            await _notificationService.NotifyVehiclePositionUpdateAsync(vehicleId, positionData);
            return Ok(ApiResponse.Ok("位置更新推送成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "推送车辆位置更新失败，车辆ID: {VehicleId}", vehicleId);
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 推送到站预测更新（内部接口）
    /// </summary>
    /// <param name="stopId">站点ID</param>
    /// <param name="predictionData">预测数据</param>
    [HttpPost("stop/{stopId}/prediction")]
    public async Task<ActionResult<ApiResponse>> NotifyArrivalPrediction(int stopId, [FromBody] object predictionData)
    {
        try
        {
            if (predictionData == null)
            {
                return BadRequest(ApiResponse.Fail("预测数据不能为空"));
            }

            await _notificationService.NotifyArrivalPredictionUpdateAsync(stopId, predictionData);
            return Ok(ApiResponse.Ok("预测更新推送成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "推送到站预测更新失败，站点ID: {StopId}", stopId);
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 推送线路状态更新（内部接口）
    /// </summary>
    /// <param name="lineId">线路ID</param>
    /// <param name="statusData">状态数据</param>
    [HttpPost("line/{lineId}/status")]
    public async Task<ActionResult<ApiResponse>> NotifyLineStatus(int lineId, [FromBody] object statusData)
    {
        try
        {
            if (statusData == null)
            {
                return BadRequest(ApiResponse.Fail("状态数据不能为空"));
            }

            await _notificationService.NotifyLineStatusUpdateAsync(lineId, statusData);
            return Ok(ApiResponse.Ok("状态更新推送成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "推送线路状态更新失败，线路ID: {LineId}", lineId);
            return StatusCode(500, ApiResponse.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取WebSocket连接指南
    /// </summary>
    [HttpGet("guide")]
    public ActionResult<ApiResponse<object>> GetWebSocketGuide()
    {
        var guide = new
        {
            WebSocketUrl = "ws://localhost:5205/ws",
            ConnectionExample = "ws://localhost:5205/ws?userId=123",
            MessageFormats = new
            {
                Subscribe = new
                {
                    Type = "Subscribe",
                    MessageId = "uuid",
                    Timestamp = "2025-08-20T09:00:00Z",
                    Data = new
                    {
                        Action = "subscribe",
                        Target = "line", // "line" | "stop" | "area"
                        LineId = 1, // 当Target为line时
                        StopId = 1, // 当Target为stop时
                        Longitude = 116.3974, // 当Target为area时
                        Latitude = 39.9093, // 当Target为area时
                        RadiusMeters = 500 // 当Target为area时
                    }
                },
                Unsubscribe = new
                {
                    Type = "Unsubscribe",
                    MessageId = "uuid",
                    Timestamp = "2025-08-20T09:00:00Z",
                    Data = new
                    {
                        Action = "unsubscribe",
                        Target = "line",
                        LineId = 1
                    }
                },
                Heartbeat = new
                {
                    Type = "Heartbeat",
                    MessageId = "uuid",
                    Timestamp = "2025-08-20T09:00:00Z"
                }
            },
            ResponseTypes = new[]
            {
                "ConnectionAck - 连接确认",
                "Subscribe - 订阅确认",
                "VehiclePositionUpdate - 车辆位置更新",
                "ArrivalPredictionUpdate - 到站预测更新",
                "LineStatusUpdate - 线路状态更新",
                "SystemNotification - 系统通知",
                "Heartbeat - 心跳响应",
                "Error - 错误消息"
            }
        };

        return Ok(ApiResponse<object>.Ok(guide));
    }
}
