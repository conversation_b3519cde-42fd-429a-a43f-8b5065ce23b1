using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// API密钥数据访问实现
/// </summary>
public class ApiKeyRepository : IApiKeyRepository
{
    private readonly BusSystemDbContext _context;

    public ApiKeyRepository(BusSystemDbContext context)
    {
        _context = context;
    }

    public async Task<ApiKeyInfo?> GetByHashAsync(string apiKeyHash)
    {
        var sql = @"
            SELECT ak.id, ak.key_name, ak.key_type, ak.description, ak.owner_name, 
                   ak.is_active, ak.rate_limit_per_hour, ak.rate_limit_per_day, 
                   ak.allowed_ips, ak.expires_at, ak.last_used_at, ak.usage_count, ak.created_at
            FROM api_keys ak 
            WHERE ak.api_key_hash = {0} AND ak.is_active = true";

        var result = await _context.Database
            .SqlQueryRaw<ApiKeyQueryResult>(sql, apiKeyHash)
            .FirstOrDefaultAsync();

        if (result == null) return null;

        return new ApiKeyInfo
        {
            Id = result.Id,
            KeyName = result.KeyName,
            KeyType = result.KeyType,
            Description = result.Description ?? string.Empty,
            OwnerName = result.OwnerName ?? string.Empty,
            IsActive = result.IsActive,
            RateLimitPerHour = result.RateLimitPerHour,
            RateLimitPerDay = result.RateLimitPerDay,
            AllowedIps = result.AllowedIps,
            ExpiresAt = result.ExpiresAt,
            LastUsedAt = result.LastUsedAt,
            UsageCount = result.UsageCount,
            CreatedAt = result.CreatedAt
        };
    }

    public async Task<ApiKeyInfo?> GetByIdAsync(int apiKeyId)
    {
        var sql = @"
            SELECT ak.id, ak.key_name, ak.key_type, ak.description, ak.owner_name, 
                   ak.is_active, ak.rate_limit_per_hour, ak.rate_limit_per_day, 
                   ak.allowed_ips, ak.expires_at, ak.last_used_at, ak.usage_count, ak.created_at
            FROM api_keys ak 
            WHERE ak.id = {0}";

        var result = await _context.Database
            .SqlQueryRaw<ApiKeyQueryResult>(sql, apiKeyId)
            .FirstOrDefaultAsync();

        if (result == null) return null;

        return new ApiKeyInfo
        {
            Id = result.Id,
            KeyName = result.KeyName,
            KeyType = result.KeyType,
            Description = result.Description ?? string.Empty,
            OwnerName = result.OwnerName ?? string.Empty,
            IsActive = result.IsActive,
            RateLimitPerHour = result.RateLimitPerHour,
            RateLimitPerDay = result.RateLimitPerDay,
            AllowedIps = result.AllowedIps,
            ExpiresAt = result.ExpiresAt,
            LastUsedAt = result.LastUsedAt,
            UsageCount = result.UsageCount,
            CreatedAt = result.CreatedAt
        };
    }

    public async Task<IEnumerable<Core.Entities.ApiPermission>> GetPermissionsAsync(int apiKeyId)
    {
        var sql = @"
            SELECT ap.id, ap.permission_name, ap.endpoint_pattern, ap.http_methods, 
                   ap.description, ap.is_active
            FROM api_permissions ap
            INNER JOIN api_key_permissions akp ON ap.id = akp.permission_id
            WHERE akp.api_key_id = {0} AND ap.is_active = true";

        var results = await _context.Database
            .SqlQueryRaw<ApiPermissionQueryResult>(sql, apiKeyId)
            .ToListAsync();

        return results.Select(r => new Core.Entities.ApiPermission
        {
            Id = r.Id,
            PermissionName = r.PermissionName,
            EndpointPattern = r.EndpointPattern,
            HttpMethods = r.HttpMethods,
            Description = r.Description ?? string.Empty,
            IsActive = r.IsActive
        });
    }

    public async Task<int> GetHourlyRequestCountAsync(int apiKeyId, DateTime timeWindow)
    {
        var sql = @"
            SELECT COALESCE(request_count, 0) as count
            FROM api_rate_limits 
            WHERE api_key_id = {0} AND time_window = {1}";

        var result = await _context.Database
            .SqlQueryRaw<int>(sql, apiKeyId, timeWindow)
            .FirstOrDefaultAsync();

        return result;
    }

    public async Task IncrementRequestCountAsync(int apiKeyId)
    {
        var currentHour = DateTime.UtcNow.Date.AddHours(DateTime.UtcNow.Hour);
        
        var sql = @"
            INSERT INTO api_rate_limits (api_key_id, time_window, request_count, created_at, updated_at)
            VALUES ({0}, {1}, 1, {2}, {2})
            ON CONFLICT (api_key_id, time_window) 
            DO UPDATE SET 
                request_count = api_rate_limits.request_count + 1,
                updated_at = {2}";

        await _context.Database.ExecuteSqlRawAsync(sql, apiKeyId, currentHour, DateTime.UtcNow);
    }

    public async Task LogAccessAsync(ApiAccessLog accessLog)
    {
        var sql = @"
            INSERT INTO api_access_audit 
            (api_key_id, api_key_hash, endpoint, http_method, client_ip, user_agent, 
             request_size, response_size, response_time_ms, status_code, error_message, created_at)
            VALUES 
            ({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11})";

        await _context.Database.ExecuteSqlRawAsync(sql,
            accessLog.ApiKeyId,
            accessLog.ApiKeyHash,
            accessLog.Endpoint,
            accessLog.HttpMethod,
            accessLog.ClientIp,
            accessLog.UserAgent,
            accessLog.RequestSize,
            accessLog.ResponseSize,
            accessLog.ResponseTimeMs,
            accessLog.StatusCode,
            accessLog.ErrorMessage,
            accessLog.CreatedAt);
    }

    public async Task UpdateLastUsedAsync(int apiKeyId, DateTime lastUsedAt)
    {
        var sql = @"
            UPDATE api_keys 
            SET last_used_at = {1}, usage_count = usage_count + 1, updated_at = {2}
            WHERE id = {0}";

        await _context.Database.ExecuteSqlRawAsync(sql, apiKeyId, lastUsedAt, DateTime.UtcNow);
    }

    public async Task<ApiKeyInfo> CreateAsync(ApiKeyInfo apiKeyInfo, string apiKeyHash)
    {
        var sql = @"
            INSERT INTO api_keys 
            (key_name, api_key_hash, key_type, description, owner_name, owner_contact,
             is_active, rate_limit_per_hour, rate_limit_per_day, allowed_ips, expires_at, created_at, updated_at)
            VALUES 
            ({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11}, {12})
            RETURNING id";

        var newId = await _context.Database
            .SqlQueryRaw<int>(sql,
                apiKeyInfo.KeyName,
                apiKeyHash,
                apiKeyInfo.KeyType,
                apiKeyInfo.Description,
                apiKeyInfo.OwnerName,
                null, // owner_contact
                apiKeyInfo.IsActive,
                apiKeyInfo.RateLimitPerHour,
                apiKeyInfo.RateLimitPerDay,
                apiKeyInfo.AllowedIps,
                apiKeyInfo.ExpiresAt,
                apiKeyInfo.CreatedAt,
                DateTime.UtcNow)
            .FirstAsync();
        
        apiKeyInfo.Id = newId;
        return apiKeyInfo;
    }

    public async Task UpdateAsync(int apiKeyId, UpdateApiKeyRequest request)
    {
        var setParts = new List<string>();
        var parameters = new List<object> { apiKeyId };
        var paramIndex = 1;

        if (!string.IsNullOrWhiteSpace(request.KeyName))
        {
            setParts.Add($"key_name = {{{paramIndex}}}");
            parameters.Add(request.KeyName);
            paramIndex++;
        }

        if (!string.IsNullOrWhiteSpace(request.Description))
        {
            setParts.Add($"description = {{{paramIndex}}}");
            parameters.Add(request.Description);
            paramIndex++;
        }

        if (request.RateLimitPerHour.HasValue)
        {
            setParts.Add($"rate_limit_per_hour = {{{paramIndex}}}");
            parameters.Add(request.RateLimitPerHour.Value);
            paramIndex++;
        }

        if (request.IsActive.HasValue)
        {
            setParts.Add($"is_active = {{{paramIndex}}}");
            parameters.Add(request.IsActive.Value);
            paramIndex++;
        }

        if (setParts.Any())
        {
            setParts.Add($"updated_at = {{{paramIndex}}}");
            parameters.Add(DateTime.UtcNow);

            var sql = $"UPDATE api_keys SET {string.Join(", ", setParts)} WHERE id = {{0}}";
            await _context.Database.ExecuteSqlRawAsync(sql, parameters.ToArray());
        }
    }

    public async Task SetPermissionsAsync(int apiKeyId, IEnumerable<string> permissionNames)
    {
        // 删除现有权限
        var deleteSql = "DELETE FROM api_key_permissions WHERE api_key_id = {0}";
        await _context.Database.ExecuteSqlRawAsync(deleteSql, apiKeyId);

        // 获取权限ID并插入新权限
        if (permissionNames.Any())
        {
            var permissionIds = await GetPermissionIdsByNamesAsync(permissionNames);
            
            foreach (var permissionId in permissionIds)
            {
                var insertSql = @"
                    INSERT INTO api_key_permissions (api_key_id, permission_id, granted_at)
                    VALUES ({0}, {1}, {2})";
                
                await _context.Database.ExecuteSqlRawAsync(insertSql, apiKeyId, permissionId, DateTime.UtcNow);
            }
        }
    }

    public async Task DisableAsync(int apiKeyId)
    {
        var sql = "UPDATE api_keys SET is_active = false, updated_at = {1} WHERE id = {0}";
        await _context.Database.ExecuteSqlRawAsync(sql, apiKeyId, DateTime.UtcNow);
    }

    public async Task<IEnumerable<ApiKeyInfo>> GetAllAsync(string? keyType = null, bool? isActive = null)
    {
        var whereConditions = new List<string>();
        var parameters = new List<object>();
        var paramIndex = 0;

        if (!string.IsNullOrWhiteSpace(keyType))
        {
            whereConditions.Add($"key_type = {{{paramIndex}}}");
            parameters.Add(keyType);
            paramIndex++;
        }

        if (isActive.HasValue)
        {
            whereConditions.Add($"is_active = {{{paramIndex}}}");
            parameters.Add(isActive.Value);
            paramIndex++;
        }

        var whereClause = whereConditions.Any() ? $"WHERE {string.Join(" AND ", whereConditions)}" : "";

        var sql = $@"
            SELECT id, key_name, key_type, description, owner_name, 
                   is_active, rate_limit_per_hour, rate_limit_per_day, 
                   allowed_ips, expires_at, last_used_at, usage_count, created_at
            FROM api_keys 
            {whereClause}
            ORDER BY created_at DESC";

        var results = await _context.Database
            .SqlQueryRaw<ApiKeyQueryResult>(sql, parameters.ToArray())
            .ToListAsync();

        return results.Select(r => new ApiKeyInfo
        {
            Id = r.Id,
            KeyName = r.KeyName,
            KeyType = r.KeyType,
            Description = r.Description ?? string.Empty,
            OwnerName = r.OwnerName ?? string.Empty,
            IsActive = r.IsActive,
            RateLimitPerHour = r.RateLimitPerHour,
            RateLimitPerDay = r.RateLimitPerDay,
            AllowedIps = r.AllowedIps,
            ExpiresAt = r.ExpiresAt,
            LastUsedAt = r.LastUsedAt,
            UsageCount = r.UsageCount,
            CreatedAt = r.CreatedAt
        });
    }

    public async Task<IEnumerable<Core.Entities.ApiPermission>> GetAllPermissionsAsync()
    {
        var sql = @"
            SELECT id, permission_name, endpoint_pattern, http_methods, description, is_active
            FROM api_permissions 
            WHERE is_active = true
            ORDER BY permission_name";

        var results = await _context.Database
            .SqlQueryRaw<ApiPermissionQueryResult>(sql)
            .ToListAsync();

        return results.Select(r => new Core.Entities.ApiPermission
        {
            Id = r.Id,
            PermissionName = r.PermissionName,
            EndpointPattern = r.EndpointPattern,
            HttpMethods = r.HttpMethods,
            Description = r.Description ?? string.Empty,
            IsActive = r.IsActive
        });
    }

    public async Task<IEnumerable<int>> GetPermissionIdsByNamesAsync(IEnumerable<string> permissionNames)
    {
        if (!permissionNames.Any()) return Enumerable.Empty<int>();

        var namesList = permissionNames.ToList();
        var placeholders = string.Join(",", namesList.Select((_, i) => $"{{{i}}}"));
        
        var sql = $@"
            SELECT id 
            FROM api_permissions 
            WHERE permission_name IN ({placeholders}) AND is_active = true";

        var results = await _context.Database
            .SqlQueryRaw<int>(sql, namesList.Cast<object>().ToArray())
            .ToListAsync();

        return results;
    }

    public async Task<int> CleanupExpiredRateLimitsAsync(DateTime cutoffTime)
    {
        var sql = "DELETE FROM api_rate_limits WHERE time_window < {0}";
        return await _context.Database.ExecuteSqlRawAsync(sql, cutoffTime);
    }

    public async Task<int> CleanupExpiredAccessLogsAsync(DateTime cutoffTime)
    {
        var sql = "DELETE FROM api_access_audit WHERE created_at < {0}";
        return await _context.Database.ExecuteSqlRawAsync(sql, cutoffTime);
    }
}

/// <summary>
/// API密钥查询结果
/// </summary>
public class ApiKeyQueryResult
{
    public int Id { get; set; }
    public string KeyName { get; set; } = string.Empty;
    public string KeyType { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? OwnerName { get; set; }
    public bool IsActive { get; set; }
    public int RateLimitPerHour { get; set; }
    public int RateLimitPerDay { get; set; }
    public string[]? AllowedIps { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public long UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// API权限查询结果
/// </summary>
public class ApiPermissionQueryResult
{
    public int Id { get; set; }
    public string PermissionName { get; set; } = string.Empty;
    public string EndpointPattern { get; set; } = string.Empty;
    public string HttpMethods { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}
