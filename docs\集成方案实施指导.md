# 路径生成与预测集成方案实施指导

## 1. 实施前准备

### 1.1 环境检查清单
- [ ] PostgreSQL + PostGIS 扩展正常运行
- [ ] TimescaleDB 扩展正常运行，GPS数据完整
- [ ] Redis 服务正常运行
- [ ] .NET 8 开发环境配置完成
- [ ] 数据库连接字符串配置正确

### 1.2 数据准备检查
```sql
-- 检查GPS数据量
SELECT 
    COUNT(*) as total_gps_points,
    COUNT(DISTINCT vehicle_id) as vehicle_count,
    MIN(timestamp) as earliest_data,
    MAX(timestamp) as latest_data
FROM vehicle_positions 
WHERE timestamp >= NOW() - INTERVAL '30 days';

-- 检查线路数据
SELECT 
    COUNT(*) as total_lines,
    COUNT(CASE WHEN route_geometry IS NOT NULL THEN 1 END) as lines_with_geometry
FROM bus_lines 
WHERE status = 1;

-- 检查站点数据
SELECT 
    COUNT(*) as total_stops,
    COUNT(CASE WHEN location IS NOT NULL THEN 1 END) as stops_with_location
FROM bus_stops 
WHERE status = 1;
```

### 1.3 备份策略
```bash
# 备份主数据库
pg_dump -h localhost -U postgres -d bus_system > backup_main_$(date +%Y%m%d).sql

# 备份TimescaleDB
pg_dump -h localhost -U postgres -d bus_system_timescale > backup_timescale_$(date +%Y%m%d).sql
```

## 2. 第一阶段实施步骤

### 2.1 Day 1: GPS数据质量评估

#### 2.1.1 创建数据质量评估脚本
```sql
-- 创建数据质量评估视图
CREATE OR REPLACE VIEW v_gps_data_quality AS
WITH line_stats AS (
    SELECT 
        v.line_id,
        bl.line_number,
        bl.line_name,
        COUNT(*) as gps_count,
        COUNT(DISTINCT v.id) as vehicle_count,
        COUNT(DISTINCT DATE(vp.timestamp)) as data_days,
        AVG(vp.speed) as avg_speed,
        COUNT(CASE WHEN vp.speed > 80 OR vp.speed < 0 THEN 1 END) as abnormal_speed_count,
        MIN(vp.timestamp) as earliest_data,
        MAX(vp.timestamp) as latest_data
    FROM vehicle_positions vp
    JOIN vehicles v ON vp.vehicle_id = v.id
    JOIN bus_lines bl ON v.line_id = bl.id
    WHERE vp.timestamp >= NOW() - INTERVAL '30 days'
    GROUP BY v.line_id, bl.line_number, bl.line_name
)
SELECT 
    *,
    ROUND((gps_count::DECIMAL / NULLIF(data_days, 0)), 2) as avg_gps_per_day,
    ROUND((abnormal_speed_count::DECIMAL / NULLIF(gps_count, 0) * 100), 2) as abnormal_speed_rate,
    CASE 
        WHEN gps_count >= 1000 AND data_days >= 15 AND vehicle_count >= 2 THEN '优秀'
        WHEN gps_count >= 500 AND data_days >= 10 AND vehicle_count >= 1 THEN '良好'
        WHEN gps_count >= 200 AND data_days >= 5 THEN '一般'
        ELSE '较差'
    END as quality_level
FROM line_stats
ORDER BY gps_count DESC;
```

#### 2.1.2 执行质量评估
```bash
# 导出质量评估报告
psql -h localhost -U postgres -d bus_system -c "
COPY (SELECT * FROM v_gps_data_quality) 
TO '/tmp/gps_quality_report.csv' 
WITH CSV HEADER;
"
```

### 2.2 Day 2: 路径生成算法实现

#### 2.2.1 创建必要的数据库表
```sql
-- 路径生成历史表
CREATE TABLE IF NOT EXISTS route_generation_history (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    direction INTEGER NOT NULL,
    generated_path GEOMETRY(LINESTRING, 4326),
    quality_score DECIMAL(5,2),
    generation_method VARCHAR(50) DEFAULT 'gps_clustering',
    gps_points_used INTEGER,
    trajectories_used INTEGER,
    data_start_date DATE,
    data_end_date DATE,
    generation_options JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);

-- 路径编辑记录表
CREATE TABLE IF NOT EXISTS route_edit_logs (
    id SERIAL PRIMARY KEY,
    line_id INTEGER NOT NULL,
    direction INTEGER NOT NULL,
    editor_id VARCHAR(100) NOT NULL,
    edit_type VARCHAR(20), -- 'create', 'update', 'approve'
    old_path GEOMETRY(LINESTRING, 4326),
    new_path GEOMETRY(LINESTRING, 4326),
    edit_reason TEXT,
    quality_score_before DECIMAL(5,2),
    quality_score_after DECIMAL(5,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    FOREIGN KEY (line_id) REFERENCES bus_lines(id)
);

-- 创建索引
CREATE INDEX idx_route_generation_history_line_direction 
ON route_generation_history(line_id, direction);
CREATE INDEX idx_route_generation_history_path 
ON route_generation_history USING GIST (generated_path);
CREATE INDEX idx_route_edit_logs_line_direction 
ON route_edit_logs(line_id, direction);
```

#### 2.2.2 实现核心服务类
```csharp
// 在 src/BusSystem.Core/Services/ 目录下创建
// RoutePathGenerationService.cs
// RouteQualityAssessmentService.cs
// RouteDistanceCalculationService.cs

// 在 src/BusSystem.Core/Interfaces/Services/ 目录下创建对应接口
// IRoutePathGenerationService.cs
// IRouteQualityAssessmentService.cs
// IRouteDistanceCalculationService.cs
```

### 2.3 Day 3: 批量路径生成

#### 2.3.1 创建批量生成控制器
```csharp
[ApiController]
[Route("api/route-generation")]
public class RouteGenerationController : ControllerBase
{
    [HttpPost("batch-generate")]
    public async Task<ActionResult<BatchGenerationResult>> BatchGenerateRoutes(
        [FromBody] BatchGenerationOptions options)
    {
        var result = await _batchGenerationService.GenerateAllRoutePathsAsync(options);
        return Ok(result);
    }
    
    [HttpGet("quality-report")]
    public async Task<ActionResult<List<RouteQualityReport>>> GetQualityReport()
    {
        var reports = await _qualityAssessmentService.GenerateAllQualityReportsAsync();
        return Ok(reports);
    }
}
```

#### 2.3.2 执行批量生成
```bash
# 调用批量生成API
curl -X POST "http://localhost:5000/api/route-generation/batch-generate" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-07-01",
    "endDate": "2024-08-27",
    "minQualityScore": 70,
    "segmentLengthMeters": 100,
    "clusteringRadiusMeters": 20
  }'
```

## 3. 第二阶段实施步骤

### 3.1 Day 5: 路径数据集成

#### 3.1.1 扩展BusLine实体
```csharp
// 在 BusLine.cs 中添加
public LineString? RouteGeometryUp { get; set; }
public LineString? RouteGeometryDown { get; set; }
public DateTime? RouteGeneratedAt { get; set; }
public double? RouteQualityScore { get; set; }
```

#### 3.1.2 更新数据库迁移
```bash
# 创建新的迁移
dotnet ef migrations add AddRouteGeometryFields -c BusSystemDbContext

# 应用迁移
dotnet ef database update -c BusSystemDbContext
```

### 3.2 Day 6-7: 精确距离计算实现

#### 3.2.1 实现RouteDistanceCalculationService
```csharp
// 关键方法实现
public async Task<double> CalculateRouteDistanceAsync(int lineId, int direction, 
    double fromLat, double fromLon, int toStopId)
{
    // 实现基于路径的精确距离计算
    // 详细代码见完整方案文档
}
```

### 3.3 Day 8: 位置推断优化

#### 3.3.1 更新PredictionService
```csharp
// 修改现有的PredictNextStopArrivalAsync方法
public async Task<object> PredictNextStopArrivalAsync(int vehicleId)
{
    var latestPosition = await _positionRepository.GetLatestPositionAsync(vehicleId);
    if (latestPosition == null)
    {
        return new { Error = "无法获取车辆位置信息" };
    }

    // 优先使用调度系统提供的NextStopId
    if (latestPosition.NextStopId.HasValue)
    {
        return await PredictArrivalTimeAsync(vehicleId, latestPosition.NextStopId.Value);
    }

    // 使用位置推断服务推断下一站
    var locationState = await _locationInferenceService.InferLocationStateAsync(latestPosition);
    if (locationState.NextStopId.HasValue)
    {
        return await PredictArrivalTimeAsync(vehicleId, locationState.NextStopId.Value);
    }

    return new { Error = "无法确定下一站", Suggestion = "请检查车辆是否在正常运营线路上" };
}
```

## 4. 测试验证步骤

### 4.1 单元测试
```bash
# 运行所有单元测试
dotnet test src/BusSystem.Tests/

# 运行特定测试类
dotnet test src/BusSystem.Tests/ --filter "ClassName=RoutePathGenerationServiceTests"
```

### 4.2 集成测试
```bash
# 启动测试环境
docker-compose -f scripts/docker-compose.test.yml up -d

# 运行集成测试
dotnet test src/BusSystem.IntegrationTests/
```

### 4.3 性能测试
```bash
# 使用Apache Bench测试预测API性能
ab -n 1000 -c 10 "http://localhost:5000/api/prediction/vehicle/1/stop/1"

# 测试批量预测性能
ab -n 100 -c 5 "http://localhost:5000/api/prediction/stop/1"
```

## 5. 监控和验证

### 5.1 关键指标监控
```sql
-- 监控路径数据覆盖率
SELECT 
    COUNT(*) as total_lines,
    COUNT(CASE WHEN route_geometry_up IS NOT NULL THEN 1 END) as up_coverage,
    COUNT(CASE WHEN route_geometry_down IS NOT NULL THEN 1 END) as down_coverage,
    ROUND(COUNT(CASE WHEN route_geometry_up IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*) * 100, 2) as up_coverage_rate,
    ROUND(COUNT(CASE WHEN route_geometry_down IS NOT NULL THEN 1 END)::DECIMAL / COUNT(*) * 100, 2) as down_coverage_rate
FROM bus_lines 
WHERE status = 1;

-- 监控预测精度
SELECT 
    DATE(created_at) as prediction_date,
    COUNT(*) as total_predictions,
    AVG(ABS(EXTRACT(EPOCH FROM (actual_arrival_time - predicted_arrival_time))/60)) as avg_error_minutes,
    COUNT(CASE WHEN ABS(EXTRACT(EPOCH FROM (actual_arrival_time - predicted_arrival_time))/60) <= 2 THEN 1 END) as accurate_predictions
FROM arrival_predictions_history 
WHERE created_at >= NOW() - INTERVAL '7 days'
  AND actual_arrival_time IS NOT NULL
GROUP BY DATE(created_at)
ORDER BY prediction_date DESC;
```

### 5.2 问题排查指南
```bash
# 检查服务状态
systemctl status bus-system-api

# 查看应用日志
tail -f /var/log/bus-system/application.log

# 检查数据库连接
psql -h localhost -U postgres -d bus_system -c "SELECT NOW();"

# 检查Redis连接
redis-cli ping
```

## 6. 回滚计划

### 6.1 数据回滚
```sql
-- 如果需要回滚路径数据
UPDATE bus_lines 
SET route_geometry_up = NULL, 
    route_geometry_down = NULL, 
    route_generated_at = NULL,
    route_quality_score = NULL
WHERE route_generated_at > '2024-08-27';
```

### 6.2 代码回滚
```bash
# 回滚到指定版本
git checkout <previous_commit_hash>

# 重新部署
dotnet publish -c Release
systemctl restart bus-system-api
```

这个实施指导提供了详细的操作步骤和验证方法，确保集成方案能够顺利实施并达到预期效果。
