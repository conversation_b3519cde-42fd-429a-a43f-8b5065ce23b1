#!/bin/bash

# API认证功能测试脚本
# 使用方法: ./test-api-auth.sh [API_BASE_URL]

API_BASE_URL=${1:-"http://localhost:5000"}
echo "🔐 测试API认证功能"
echo "服务地址: $API_BASE_URL"
echo "========================================"

# 检查服务是否可用
echo "🔍 检查服务状态..."
if ! curl -s --connect-timeout 5 "$API_BASE_URL/health" > /dev/null; then
    echo "❌ 服务不可用，请先启动应用程序"
    echo "启动命令: cd src/BusSystem.Api && dotnet run"
    exit 1
fi
echo "✅ 服务正常运行"
echo ""

# 测试1: 无API Key访问（应该返回401）
echo "🧪 测试1: 无API Key访问首页接口（预期: 401未授权）"
HTTP_CODE=$(curl -s -w "%{http_code}" -o /tmp/response.json \
  -X GET "$API_BASE_URL/api/home/<USER>" \
  -H "Content-Type: application/json")

if [ "$HTTP_CODE" = "401" ]; then
    echo "✅ 测试通过: 返回401未授权"
else
    echo "❌ 测试失败: 预期401，实际$HTTP_CODE"
    cat /tmp/response.json 2>/dev/null || echo ""
fi
echo ""

# 测试2: 无效API Key访问（应该返回401）
echo "测试2: 使用无效API Key访问"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -X GET "$API_BASE_URL/api/home/<USER>" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: invalid_api_key"
echo ""

# 测试3: 使用测试API Key访问（需要先在数据库中创建）
echo "测试3: 使用测试API Key访问"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -X GET "$API_BASE_URL/api/home/<USER>" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: frontend_test_key_hash"
echo ""

# 测试4: 测试数据同步接口（需要sync类型的API Key）
echo "测试4: 测试数据同步接口"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -X POST "$API_BASE_URL/api/datasync/gps" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: platform_a_sync_key_hash" \
  -d '{
    "dataSource": "test_platform",
    "timestamp": "2025-08-22T10:00:00Z",
    "gpsData": [
      {
        "vehicleId": "TEST001",
        "longitude": 116.3974,
        "latitude": 39.9093,
        "speed": 25.5,
        "gpsTime": "2025-08-22T10:00:00Z",
        "status": 1
      }
    ]
  }'
echo ""

# 测试5: 测试搜索接口
echo "测试5: 测试搜索接口"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -X GET "$API_BASE_URL/api/search/lines?keyword=1路" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: frontend_test_key_hash"
echo ""

# 测试6: 测试管理员接口（需要admin权限）
echo "测试6: 测试管理员接口"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -X GET "$API_BASE_URL/api/admin/api-keys" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: admin_key_hash"
echo ""

# 测试7: 健康检查接口
echo "测试7: 健康检查接口"
curl -s -w "\nHTTP状态码: %{http_code}\n" \
  -X GET "$API_BASE_URL/api/datasync/health"
echo ""

echo "========================================"
echo "测试完成！"
echo ""
echo "预期结果："
echo "- 测试1和2应该返回401（未授权）"
echo "- 测试3、4、5应该返回200或相应的业务状态码"
echo "- 测试6需要管理员权限，可能返回401或200"
echo "- 测试7应该返回200（健康检查通常不需要认证）"
echo ""
echo "注意：需要先执行数据库脚本创建API Key数据"
