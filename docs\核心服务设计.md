# 实时公交系统核心服务设计文档

## 1. 服务架构概述

### 1.1 分层微服务架构（解耦设计）
```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway Layer                        │
│                   (统一入口、认证、限流)                    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Business Service Layer                     │
│        (业务逻辑、数据处理、与调度平台无关)                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Line Service│ │ Stop Service│ │Real Service │           │
│  │ Search Svc  │ │Predict Svc  │ │Notify Svc   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Data Access Layer                         │
│              (统一数据访问、缓存管理)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │PostgreSQL   │ │TimescaleDB  │ │ Redis Geo   │           │
│  │   DAO       │ │    DAO      │ │    DAO      │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Data Sync Adapter Layer                     │
│                    (调度平台适配层)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   智能调度  │ │   海信调度  │ │   其他平台  │           │
│  │  Adapter    │ │  Adapter    │ │  Adapter    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 服务职责划分（解耦后）
- **API Gateway**: 统一入口、路由分发、限流熔断
- **Business Services**: 业务逻辑处理，与数据源无关
  - **Aggregation Service**: 聚合查询服务，整合多个数据源（⭐核心服务）
  - **Line Service**: 线路信息管理、线路数据查询
  - **Stop Service**: 站点信息管理、站点数据查询
  - **Realtime Service**: 实时数据处理、车辆位置更新
  - **Prediction Service**: 到站时间预测、算法优化
  - **Search Service**: 搜索功能、索引管理
  - **Notification Service**: 实时推送、WebSocket管理
- **Data Access Layer**: 统一数据访问接口，屏蔽底层存储差异
- **Data Sync Adapters**: 调度平台适配器，负责数据同步和转换

## 2. 核心服务详细设计

### 2.1 Aggregation Service (聚合查询服务) ⭐

#### 2.1.1 功能职责
- **多数据源聚合**: 整合站点、线路、实时数据、预测信息
- **一站式查询**: 提供完整的业务场景查询接口
- **性能优化**: 并行查询多个服务，减少客户端请求次数
- **数据整合**: 统一数据格式，提供一致的响应结构

#### 2.1.2 核心接口设计
```csharp
public interface IAggregationService
{
    // 主要聚合接口：获取附近站点及实时信息
    Task<object> GetNearbyStopsWithRealtimeInfoAsync(
        double longitude, double latitude,
        double radiusMeters = 500,
        CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02,
        bool mergeSameNameStops = true);

    // 站点详情聚合：站点信息 + 线路信息 + 到站预测
    Task<object> GetStopDetailWithPredictionsAsync(
        int stopId,
        CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02);

    // 综合搜索：站点 + 线路搜索
    Task<object> SearchStopsAndLinesAsync(
        string keyword,
        double? longitude = null,
        double? latitude = null,
        CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02);

    // 单站点预测聚合
    Task<object> GetSingleStopPredictionsAsync(
        int stopId,
        CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02);
}
```

#### 2.1.3 实现架构
```csharp
public class AggregationService : IAggregationService
{
    private readonly IBusStopRepository _stopRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IRealtimeService _realtimeService;
    private readonly IPredictionService _predictionService;
    private readonly ICoordinateTransformService _coordinateTransformService;

    // 并行查询多个数据源，提升性能
    public async Task<object> GetNearbyStopsWithRealtimeInfoAsync(...)
    {
        // 1. 获取附近站点
        var nearbyStops = await _stopRepository.GetNearbyStopsAsync(...);

        // 2. 并行获取每个站点的详细信息
        var tasks = nearbyStops.Select(async stop => {
            var stopWithLines = await _stopRepository.GetWithLinesAsync(stop.Id);
            var predictions = await _predictionService.GetStopArrivalPredictionsAsync(stop.Id);
            return new { Stop = stop, Lines = stopWithLines?.LineStops, Predictions = predictions };
        });

        var results = await Task.WhenAll(tasks);

        // 3. 数据整合和格式化
        return FormatAggregatedResponse(results);
    }
}
```

#### 2.1.4 性能优化策略
- **并行查询**: 使用Task.WhenAll并行获取多个数据源
- **缓存策略**: 对聚合结果进行适当缓存
- **数据预加载**: 使用Include预加载关联数据
- **异常处理**: 单个服务失败不影响整体响应

### 2.2 API Gateway服务

#### 2.1.1 功能职责
- 统一API入口和路由分发
- 请求认证和授权
- 请求限流和熔断保护
- 请求日志记录和监控
- 响应数据格式化

#### 2.1.2 技术实现
```typescript
// 路由配置
const routes = {
  '/api/v1/stops/*': 'stop-service',
  '/api/v1/lines/*': 'line-service',
  '/api/v1/search/*': 'search-service',
  '/api/v1/realtime/*': 'realtime-service'
};

// 限流配置
const rateLimits = {
  default: { requests: 1000, window: 3600 }, // 1000次/小时
  premium: { requests: 10000, window: 3600 } // 10000次/小时
};
```

#### 2.1.3 核心中间件
```typescript
// 认证中间件
export const authMiddleware = async (req, res, next) => {
  const apiKey = req.headers.authorization?.replace('Bearer ', '');
  if (!apiKey || !await validateApiKey(apiKey)) {
    return res.status(401).json({ code: 401, message: 'Unauthorized' });
  }
  req.user = await getUserByApiKey(apiKey);
  next();
};

// 限流中间件
export const rateLimitMiddleware = async (req, res, next) => {
  const key = `rate_limit:${req.user.id}`;
  const current = await redis.incr(key);
  if (current === 1) {
    await redis.expire(key, 3600);
  }
  if (current > req.user.rateLimit) {
    return res.status(429).json({ code: 429, message: 'Rate limit exceeded' });
  }
  next();
};
```

### 2.2 Location Service (位置服务)

#### 2.2.1 功能职责
- 地理位置计算和距离计算
- 附近站点查询和排序
- 地理围栏判断
- 坐标系转换

#### 2.2.2 核心算法
```typescript
// 计算两点间距离 (Haversine公式)
export const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
  const R = 6371000; // 地球半径(米)
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lng2 - lng1) * Math.PI / 180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
};

// 查找附近站点（使用Redis Geo，性能极佳）
export const findNearbyStops = async (lat: number, lng: number, radius: number = 500) => {
  // 使用Redis Geo进行高速查询
  const result = await redis.georadius(
    'geo:bus_stops',
    lng, lat, radius, 'm',
    'WITHDIST', 'WITHCOORD', 'ASC', 'COUNT', 20
  );

  return result.map(([stopKey, distance, [longitude, latitude]]) => ({
    stopId: parseInt(stopKey.split(':')[1]),
    distance: Math.round(parseFloat(distance)),
    latitude: parseFloat(latitude),
    longitude: parseFloat(longitude)
  }));
};

// 复杂地理查询使用PostGIS
export const findStopsInPolygon = async (polygon: string) => {
  const query = `
    SELECT id, stop_name, ST_X(location) as longitude, ST_Y(location) as latitude
    FROM bus_stops
    WHERE ST_Within(location, ST_GeomFromText($1, 4326))
    ORDER BY id
  `;

  return await db.query(query, [polygon]);
};
```

### 2.3 Data Access Layer (数据访问层)

#### 2.3.1 统一数据访问接口
```typescript
// 数据访问层抽象接口
interface IDataAccess {
  // 基础数据访问
  getLine(lineId: string): Promise<LineInfo>;
  getStop(stopId: string): Promise<StopInfo>;
  getBus(busId: string): Promise<BusInfo>;

  // 实时数据访问
  getLineBuses(lineId: string): Promise<RealtimeBus[]>;
  getNearbyStops(lat: number, lng: number, radius: number): Promise<NearbyStop[]>;
  getArrivalPredictions(lineId: string, stopId?: string): Promise<ArrivalPrediction[]>;

  // 数据写入
  saveBusPosition(data: StandardBusData): Promise<void>;
  saveArrivalPrediction(prediction: ArrivalPrediction): Promise<void>;
}

// 数据访问层实现
export class DataAccessService implements IDataAccess {
  constructor(
    private postgresDAO: PostgresDAO,
    private timescaleDAO: TimescaleDAO,
    private redisGeoDAO: RedisGeoDAO,
    private cacheService: CacheService
  ) {}

  async getNearbyStops(lat: number, lng: number, radius: number): Promise<NearbyStop[]> {
    // 使用Redis Geo进行高速查询
    const result = await this.redisGeoDAO.findNearby('geo:bus_stops', lng, lat, radius);

    // 获取详细信息（从缓存或数据库）
    const stops = await Promise.all(
      result.map(async (item) => {
        const stopId = item.member.split(':')[1];
        return await this.getStop(stopId);
      })
    );

    return stops.map((stop, index) => ({
      ...stop,
      distance: result[index].distance
    }));
  }

  async saveBusPosition(data: StandardBusData): Promise<void> {
    // 并行保存到不同存储
    await Promise.all([
      // 保存到TimescaleDB（历史轨迹）
      this.timescaleDAO.insertGPSTrack(data),

      // 更新Redis Geo（实时位置）
      this.redisGeoDAO.updateBusLocation(data.lineId, data.busId, data.location),

      // 更新缓存
      this.cacheService.set(`bus:position:${data.busId}`, data, 60)
    ]);
  }
}
```

### 2.4 Realtime Service (实时数据服务)

#### 2.4.1 业务逻辑处理（与数据源无关）
```typescript
export class RealtimeService {
  constructor(
    private dataAccess: IDataAccess,
    private predictionService: PredictionService,
    private notificationService: NotificationService
  ) {}

  // 处理实时数据更新
  async handleRealtimeUpdate(data: StandardBusData): Promise<void> {
    // 1. 数据验证
    if (!this.validateData(data)) {
      throw new Error('Invalid realtime data');
    }

    // 2. 保存数据
    await this.dataAccess.saveBusPosition(data);

    // 3. 触发到站预测更新
    const predictions = await this.predictionService.updatePredictions(data);

    // 4. 推送实时更新
    await this.notificationService.broadcastUpdate({
      type: 'bus_position_update',
      data: data,
      predictions: predictions
    });
  }

  // 获取线路实时信息
  async getLineRealtimeInfo(lineId: string): Promise<LineRealtimeInfo> {
    const [buses, predictions] = await Promise.all([
      this.dataAccess.getLineBuses(lineId),
      this.dataAccess.getArrivalPredictions(lineId)
    ]);

    return {
      lineId,
      buses,
      predictions,
      lastUpdateTime: new Date()
    };
  }

  private validateData(data: StandardBusData): boolean {
    return data.location.latitude >= -90 && data.location.latitude <= 90 &&
           data.location.longitude >= -180 && data.location.longitude <= 180 &&
           data.timestamp.getTime() > Date.now() - 300000; // 5分钟内的数据
  }
}
```

### 2.4 Prediction Service (预测服务)

#### 2.4.1 功能职责
- 到站时间预测算法
- 历史数据分析
- 机器学习模型训练
- 预测准确性评估

#### 2.4.2 预测算法
```typescript
export class ArrivalPredictionEngine {
  // 基于历史数据的预测
  async predictByHistoricalData(busId: number, stopId: number): Promise<Prediction> {
    const historicalData = await this.getHistoricalTravelTimes(busId, stopId);
    const currentTime = new Date();
    const timeOfDay = currentTime.getHours() * 60 + currentTime.getMinutes();
    const dayOfWeek = currentTime.getDay();
    
    // 筛选相似时间段的历史数据
    const similarData = historicalData.filter(data => 
      Math.abs(data.timeOfDay - timeOfDay) <= 30 && // 30分钟内
      data.dayOfWeek === dayOfWeek
    );
    
    const avgTravelTime = similarData.reduce((sum, data) => sum + data.travelTime, 0) / similarData.length;
    const confidence = Math.min(similarData.length / 10, 1); // 数据量越多置信度越高
    
    return {
      arrivalTime: new Date(Date.now() + avgTravelTime * 1000),
      confidence,
      method: 'historical'
    };
  }

  // 基于实时位置的预测
  async predictByRealtime(busPosition: BusPosition, targetStopId: number): Promise<Prediction> {
    const route = await this.getRouteToStop(busPosition.currentStopId, targetStopId);
    let totalTime = 0;
    let confidence = 0.9;

    for (let i = 0; i < route.length - 1; i++) {
      const segment = route[i];
      const segmentTime = await this.predictSegmentTime(segment, busPosition.speed);
      totalTime += segmentTime;
    }

    // 考虑交通状况调整
    const trafficFactor = await this.getTrafficFactor(route);
    totalTime *= trafficFactor;

    return {
      arrivalTime: new Date(Date.now() + totalTime * 1000),
      confidence: confidence * (1 / trafficFactor), // 交通拥堵降低置信度
      method: 'realtime'
    };
  }

  // 混合预测算法
  async predictArrival(busId: number, stopId: number): Promise<Prediction> {
    const [historicalPred, realtimePred] = await Promise.all([
      this.predictByHistoricalData(busId, stopId),
      this.predictByRealtime(await this.getBusPosition(busId), stopId)
    ]);

    // 根据置信度加权平均
    const totalConfidence = historicalPred.confidence + realtimePred.confidence;
    const weightedTime = (
      historicalPred.arrivalTime.getTime() * historicalPred.confidence +
      realtimePred.arrivalTime.getTime() * realtimePred.confidence
    ) / totalConfidence;

    return {
      arrivalTime: new Date(weightedTime),
      confidence: Math.min(totalConfidence / 2, 1),
      method: 'hybrid'
    };
  }
}
```

### 2.5 Cache Service (缓存服务)

#### 2.5.1 缓存策略
```typescript
export class CacheManager {
  // 多级缓存策略
  private readonly cacheConfig = {
    realtime: { ttl: 30, type: 'redis' },      // 实时数据30秒
    basic: { ttl: 3600, type: 'redis' },       // 基础数据1小时
    search: { ttl: 600, type: 'redis' },       // 搜索结果10分钟
    static: { ttl: 86400, type: 'memory' }     // 静态数据24小时
  };

  async get(key: string, category: string = 'basic'): Promise<any> {
    const config = this.cacheConfig[category];
    
    if (config.type === 'memory') {
      return this.memoryCache.get(key);
    } else {
      const data = await this.redis.get(key);
      return data ? JSON.parse(data) : null;
    }
  }

  async set(key: string, value: any, category: string = 'basic'): Promise<void> {
    const config = this.cacheConfig[category];
    
    if (config.type === 'memory') {
      this.memoryCache.set(key, value, config.ttl);
    } else {
      await this.redis.setex(key, config.ttl, JSON.stringify(value));
    }
  }

  // 缓存预热
  async warmupCache(): Promise<void> {
    // 预加载热门线路数据
    const popularLines = await this.getPopularLines();
    for (const line of popularLines) {
      await this.preloadLineData(line.id);
    }

    // 预加载热门站点数据
    const popularStops = await this.getPopularStops();
    for (const stop of popularStops) {
      await this.preloadStopData(stop.id);
    }
  }
}
```

### 2.6 Notification Service (通知服务)

#### 2.6.1 WebSocket管理
```typescript
export class WebSocketManager {
  private connections = new Map<string, WebSocket>();
  private subscriptions = new Map<string, Set<string>>();

  // 处理客户端连接
  handleConnection(ws: WebSocket, sessionId: string) {
    this.connections.set(sessionId, ws);
    
    ws.on('message', (message) => {
      const data = JSON.parse(message.toString());
      this.handleMessage(sessionId, data);
    });

    ws.on('close', () => {
      this.handleDisconnection(sessionId);
    });
  }

  // 处理订阅消息
  handleMessage(sessionId: string, message: any) {
    switch (message.type) {
      case 'subscribe':
        this.subscribe(sessionId, message.channel, message.params);
        break;
      case 'unsubscribe':
        this.unsubscribe(sessionId, message.channel);
        break;
    }
  }

  // 推送实时更新
  async pushUpdate(channel: string, data: any) {
    const subscribers = this.subscriptions.get(channel) || new Set();
    
    for (const sessionId of subscribers) {
      const ws = this.connections.get(sessionId);
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: `${channel}_update`,
          data,
          timestamp: Date.now()
        }));
      }
    }
  }
}
```

## 3. 服务间通信

### 3.1 同步通信 (HTTP/gRPC)
- 基础数据查询使用HTTP REST API
- 高频调用使用gRPC提升性能
- 服务发现使用Consul或etcd

### 3.2 异步通信 (消息队列)
- 使用Redis Pub/Sub进行实时数据推送
- 使用RabbitMQ处理复杂业务流程
- 事件驱动架构处理数据更新

### 3.3 数据一致性
- 最终一致性模型
- 分布式事务使用Saga模式
- 数据同步使用事件溯源

## 4. 服务监控和治理

### 4.1 健康检查
```typescript
// 服务健康检查
export const healthCheck = {
  '/health': async () => ({
    status: 'healthy',
    timestamp: Date.now(),
    services: {
      database: await checkDatabase(),
      redis: await checkRedis(),
      external: await checkExternalServices()
    }
  })
};
```

### 4.2 服务熔断
```typescript
// 熔断器实现
export class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime = 0;
  private state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN

  async call(fn: Function): Promise<any> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
```

### 4.3 性能监控
- 响应时间监控
- 错误率统计
- 资源使用监控
- 业务指标监控
