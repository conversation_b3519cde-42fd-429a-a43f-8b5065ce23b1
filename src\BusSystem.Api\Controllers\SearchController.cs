using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Shared.Models.Common;
using BusSystem.Api.Authorization;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 搜索API控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[ApiAuthorize] // 添加API认证
public class SearchController : ControllerBase
{
    private readonly ISearchService _searchService;
    private readonly ILogger<SearchController> _logger;

    public SearchController(ISearchService searchService, ILogger<SearchController> logger)
    {
        _searchService = searchService;
        _logger = logger;
    }

    /// <summary>
    /// 搜索线路
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="limit">返回结果数量限制（默认10）</param>
    [HttpGet("lines")]
    public async Task<ActionResult<ApiResponse<object>>> SearchLines(
        [FromQuery] string keyword,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return BadRequest(ApiResponse<object>.Fail("搜索关键词不能为空"));
            }

            if (limit < 1 || limit > 50)
            {
                limit = 10;
            }

            // 验证坐标范围（如果提供）
            if (longitude.HasValue && latitude.HasValue)
            {
                if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
                {
                    return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
                }
            }

            var result = await _searchService.SearchLinesAsync(keyword, longitude, latitude, limit);
            
            // 记录搜索行为
            var resultData = result as dynamic;
            var resultCount = resultData?.Total ?? 0;
            (double, double)? location = null;
            if (longitude.HasValue && latitude.HasValue)
            {
                location = (longitude.Value, latitude.Value);
            }
            await _searchService.RecordSearchBehaviorAsync(keyword, resultCount, location);

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索线路失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索站点
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="limit">返回结果数量限制（默认10）</param>
    [HttpGet("stops")]
    public async Task<ActionResult<ApiResponse<object>>> SearchStops(
        [FromQuery] string keyword,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return BadRequest(ApiResponse<object>.Fail("搜索关键词不能为空"));
            }

            if (limit < 1 || limit > 50)
            {
                limit = 10;
            }

            // 验证坐标范围（如果提供）
            if (longitude.HasValue && latitude.HasValue)
            {
                if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
                {
                    return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
                }
            }

            var result = await _searchService.SearchStopsAsync(keyword, longitude, latitude, limit);
            
            // 记录搜索行为
            var resultData = result as dynamic;
            var resultCount = resultData?.Total ?? 0;
            (double, double)? location = null;
            if (longitude.HasValue && latitude.HasValue)
            {
                location = (longitude.Value, latitude.Value);
            }
            await _searchService.RecordSearchBehaviorAsync(keyword, resultCount, location);

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 综合搜索（线路+站点）
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="lineLimit">线路结果数量限制（默认5）</param>
    /// <param name="stopLimit">站点结果数量限制（默认5）</param>
    [HttpGet("all")]
    public async Task<ActionResult<ApiResponse<object>>> SearchAll(
        [FromQuery] string keyword,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int lineLimit = 5,
        [FromQuery] int stopLimit = 5)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return BadRequest(ApiResponse<object>.Fail("搜索关键词不能为空"));
            }

            if (lineLimit < 1 || lineLimit > 20) lineLimit = 5;
            if (stopLimit < 1 || stopLimit > 20) stopLimit = 5;

            // 验证坐标范围（如果提供）
            if (longitude.HasValue && latitude.HasValue)
            {
                if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
                {
                    return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
                }
            }

            var result = await _searchService.SearchAllAsync(keyword, longitude, latitude, lineLimit, stopLimit);
            
            // 记录搜索行为
            var resultData = result as dynamic;
            var resultCount = resultData?.Total ?? 0;
            (double, double)? location = null;
            if (longitude.HasValue && latitude.HasValue)
            {
                location = (longitude.Value, latitude.Value);
            }
            await _searchService.RecordSearchBehaviorAsync(keyword, resultCount, location);

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "综合搜索失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取搜索建议（自动补全）
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="limit">返回结果数量限制（默认10）</param>
    [HttpGet("suggestions")]
    public async Task<ActionResult<ApiResponse<object>>> GetSearchSuggestions(
        [FromQuery] string keyword,
        [FromQuery] int limit = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return BadRequest(ApiResponse<object>.Fail("搜索关键词不能为空"));
            }

            if (limit < 1 || limit > 20)
            {
                limit = 10;
            }

            var result = await _searchService.GetSearchSuggestionsAsync(keyword, limit);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索建议失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取热门搜索关键词
    /// </summary>
    /// <param name="limit">返回结果数量限制（默认10）</param>
    [HttpGet("hot-keywords")]
    public async Task<ActionResult<ApiResponse<object>>> GetHotSearchKeywords([FromQuery] int limit = 10)
    {
        try
        {
            if (limit < 1 || limit > 20)
            {
                limit = 10;
            }

            var result = await _searchService.GetHotSearchKeywordsAsync(limit);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门搜索关键词失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索统计信息
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetSearchStats()
    {
        try
        {
            // 获取搜索统计数据
            var totalSearches = await GetTotalSearchCountAsync();
            var todaySearches = await GetTodaySearchCountAsync();
            var topKeywords = await GetTopKeywordsAsync();
            var searchTrends = await GetSearchTrendsDataAsync();

            var stats = new
            {
                Summary = new
                {
                    TotalSearches = totalSearches,
                    TodaySearches = todaySearches,
                    AverageSearchesPerDay = totalSearches / Math.Max(1, 30),
                    GrowthRate = 5.2 // 模拟增长率
                },
                TopKeywords = topKeywords,
                SearchTrends = searchTrends,
                CategoryStats = new
                {
                    BusLines = 4500,
                    Landmarks = 3200,
                    TransportHubs = 2800,
                    Services = 1580
                }
            };

            return Ok(ApiResponse<object>.Ok(stats));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索统计信息失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    private async Task<int> GetTotalSearchCountAsync()
    {
        await Task.CompletedTask;
        return 12580 + new Random().Next(0, 100);
    }

    private async Task<int> GetTodaySearchCountAsync()
    {
        await Task.CompletedTask;
        return 456 + new Random().Next(0, 50);
    }

    private async Task<object[]> GetTopKeywordsAsync()
    {
        await Task.CompletedTask;
        return new object[]
        {
            new { Keyword = "1路", Count = 1250, Trend = "up" },
            new { Keyword = "地铁站", Count = 980, Trend = "stable" },
            new { Keyword = "火车站", Count = 756, Trend = "down" },
            new { Keyword = "机场", Count = 620, Trend = "up" },
            new { Keyword = "医院", Count = 580, Trend = "stable" }
        };
    }

    private async Task<object> GetSearchTrendsDataAsync()
    {
        await Task.CompletedTask;
        return new
        {
            LastHour = 45 + new Random().Next(-10, 20),
            LastDay = 456 + new Random().Next(-50, 100),
            LastWeek = 3200 + new Random().Next(-200, 400),
            LastMonth = 12580 + new Random().Next(-500, 1000)
        };
    }
}
