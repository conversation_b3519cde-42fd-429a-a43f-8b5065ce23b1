# API认证架构实施指南

## 📋 概述

本文档详细说明了实时公交系统统一API认证架构的设计和实施方案，包括API Key管理、权限控制、速率限制等完整功能。

## 🎯 设计目标

- **统一认证**: 所有API接口使用统一的API Key认证机制
- **灵活权限**: 支持基于角色和权限的细粒度访问控制
- **多场景支持**: 支持前端应用、第三方集成、数据同步等不同使用场景
- **安全可控**: 包含速率限制、IP白名单、过期控制等安全机制
- **易于管理**: 提供完整的API Key生命周期管理功能

## 🏗️ 架构设计

### 权限层级结构
```
API Key类型
├── sync (数据同步)
│   ├── datasync.gps - GPS数据同步
│   ├── datasync.base_data - 基础数据同步
│   └── datasync.status - 状态查询
├── frontend (前端应用)
│   ├── home.* - 首页相关接口
│   ├── search.* - 搜索相关接口
│   ├── lines.* - 线路相关接口
│   ├── stops.* - 站点相关接口
│   └── realtime.* - 实时数据接口
├── partner (第三方合作)
│   ├── 受限的业务接口访问
│   └── 较低的速率限制
└── admin (系统管理)
    └── 所有接口访问权限
```

### 核心组件
1. **ApiKeyAuthenticationHandler** - API Key认证处理器
2. **ApiPermissionAuthorizationHandler** - 权限授权处理器
3. **IApiKeyService** - API Key管理服务
4. **数据库表** - 存储API Key和权限配置

## 📊 数据库设计

### 核心表结构
- `api_keys` - API密钥主表
- `api_permissions` - 权限定义表
- `api_key_permissions` - 密钥权限关联表
- `api_access_audit` - 访问审计日志表
- `api_rate_limits` - 速率限制记录表

### 权限配置示例
```sql
-- 前端应用权限
INSERT INTO api_permissions VALUES
('home.nearby_stops', '/api/home/<USER>', 'GET', '附近站点查询'),
('search.all', '/api/search/all*', 'GET', '综合搜索'),
('lines.detail', '/api/lines/*', 'GET', '线路详情');

-- 数据同步权限
INSERT INTO api_permissions VALUES
('datasync.gps', '/api/datasync/gps*', 'POST', 'GPS数据同步'),
('datasync.base_data', '/api/datasync/base-data*', 'POST', '基础数据同步');
```

## 🔧 实施步骤

### 第一阶段：基础框架搭建

#### 1. 创建数据库表
```bash
psql -h localhost -U bus_user -d bus_system -f scripts/create-api-auth-tables.sql
```

#### 2. 实现核心服务
- 创建 `ApiKeyService` 实现 `IApiKeyService`
- 实现API Key验证、权限检查、速率限制等核心逻辑

#### 3. 配置认证和授权
在 `Program.cs` 中添加：
```csharp
// 添加认证
builder.Services.AddAuthentication("ApiKey")
    .AddApiKeyAuthentication();

// 添加授权策略
builder.Services.AddApiAuthorizationPolicies();

// 注册服务
builder.Services.AddScoped<IApiKeyService, ApiKeyService>();

// 配置中间件
app.UseAuthentication();
app.UseAuthorization();
```

### 第二阶段：控制器集成

#### 1. 更新现有控制器
为所有控制器添加认证特性：
```csharp
[ApiController]
[Route("api/[controller]")]
[ApiAuthorize] // 添加API认证
public class HomeController : ControllerBase
{
    // 控制器方法
}
```

#### 2. 特定权限控制
对需要特殊权限的接口：
```csharp
[HttpPost("cleanup")]
[ApiAuthorize("datasync.cleanup")] // 需要特定权限
public async Task<ActionResult> CleanupData()
{
    // 方法实现
}
```

### 第三阶段：兼容性处理

#### 1. 保持DataSyncController兼容
- 保留现有的API Key验证逻辑
- 逐步迁移到新的认证系统
- 提供迁移脚本和文档

#### 2. 渐进式部署
- 先在测试环境验证
- 逐个控制器迁移
- 监控和调优

## 🔐 安全特性

### 1. API Key安全
- 使用SHA-256哈希存储API Key
- 支持API Key过期时间设置
- 提供API Key禁用功能

### 2. 访问控制
- IP白名单限制
- 基于权限的细粒度控制
- 支持通配符和参数化端点匹配

### 3. 速率限制
- 每小时和每天的请求限制
- 基于滑动窗口的限流算法
- 超限后的友好错误提示

### 4. 审计日志
- 记录所有API访问行为
- 包含请求详情和响应信息
- 支持安全审计和问题排查

## 📈 监控和管理

### 1. 使用统计
- API Key使用次数统计
- 最后使用时间记录
- 热门接口访问分析

### 2. 性能监控
- 响应时间统计
- 错误率监控
- 速率限制触发统计

### 3. 安全监控
- 异常访问检测
- 频繁失败请求告警
- IP黑名单自动管理

## 🛠️ 管理接口

### 1. API Key管理
```http
# 创建API Key
POST /api/admin/api-keys
{
  "keyName": "前端应用",
  "keyType": "frontend",
  "description": "H5前端应用",
  "ownerName": "前端团队",
  "rateLimitPerHour": 5000,
  "permissionNames": ["home.*", "search.*"]
}

# 获取API Key列表
GET /api/admin/api-keys?keyType=frontend&isActive=true

# 更新API Key
PUT /api/admin/api-keys/{id}
{
  "description": "更新后的描述",
  "rateLimitPerHour": 10000
}

# 禁用API Key
DELETE /api/admin/api-keys/{id}
```

### 2. 权限管理
```http
# 获取权限列表
GET /api/admin/permissions

# 获取API Key权限
GET /api/admin/api-keys/{id}/permissions

# 更新API Key权限
PUT /api/admin/api-keys/{id}/permissions
{
  "permissionNames": ["home.*", "search.*", "lines.list"]
}
```

### 3. 访问统计
```http
# 获取访问统计
GET /api/admin/api-keys/{id}/stats?startDate=2025-08-01&endDate=2025-08-31

# 获取热门接口
GET /api/admin/stats/popular-endpoints?limit=10
```

## 🔄 迁移策略

### 1. 现有系统兼容
- DataSyncController保持现有认证方式
- 新增统一认证作为可选方案
- 提供双重认证支持

### 2. 渐进式迁移
```
阶段1: 新认证系统并行运行
阶段2: 逐步迁移现有接口
阶段3: 统一到新认证系统
阶段4: 移除旧认证代码
```

### 3. 数据迁移
```sql
-- 迁移现有数据源配置到新表
INSERT INTO api_keys (key_name, api_key_hash, key_type, description)
SELECT source_name, api_key_hash, 'sync', source_name 
FROM data_source_configs 
WHERE is_active = true;
```

## 📝 使用示例

### 1. 前端应用调用
```javascript
// 设置API Key
const apiKey = 'your_frontend_api_key';

// 调用接口
const response = await fetch('/api/home/<USER>', {
  headers: {
    'X-API-Key': apiKey,
    'Content-Type': 'application/json'
  }
});
```

### 2. 第三方集成
```python
import requests

# 配置API Key
headers = {
    'X-API-Key': 'your_partner_api_key',
    'Content-Type': 'application/json'
}

# 调用接口
response = requests.get(
    'https://api.bus-system.com/api/lines',
    headers=headers
)
```

### 3. 数据同步
```bash
# 使用curl调用数据同步接口
curl -X POST "https://api.bus-system.com/api/datasync/gps" \
  -H "X-API-Key: your_sync_api_key" \
  -H "Content-Type: application/json" \
  -d '{"dataSource": "platform_a", "gpsData": [...]}'
```

## 🚨 注意事项

### 1. 安全考虑
- API Key必须通过HTTPS传输
- 不要在客户端代码中硬编码API Key
- 定期轮换API Key
- 监控异常访问行为

### 2. 性能考虑
- 使用Redis缓存API Key信息
- 合理设置速率限制
- 定期清理过期的访问日志
- 优化权限检查逻辑

### 3. 运维考虑
- 建立API Key管理流程
- 设置监控和告警
- 准备应急处理方案
- 定期进行安全审计

---

**文档维护**: 系统开发团队  
**最后更新**: 2025-08-22  
**版本**: v1.0
