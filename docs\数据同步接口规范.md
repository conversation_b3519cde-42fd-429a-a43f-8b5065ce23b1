# 实时公交系统数据同步接口规范

## 📋 文档信息

- **文档版本**: v1.0
- **创建时间**: 2025-08-22
- **适用系统**: 实时公交系统 v1.0+
- **接口版本**: v1
- **文档类型**: 数据同步接口规范

## 🎯 概述

本文档详细说明了实时公交系统数据同步接口的使用方法、数据格式、对接流程和注意事项。该接口用于接收外部调度平台、GPS设备等数据源推送的实时数据。

### 适用场景
- 公交调度平台数据同步
- GPS设备实时数据上报
- 第三方系统数据集成
- 基础数据更新同步

## 🔗 接口基础信息

### 服务地址
- **生产环境**: `https://api.bus-system.com`
- **测试环境**: `https://test-api.bus-system.com`
- **开发环境**: `http://localhost:5000`

### 通用请求头
```http
Content-Type: application/json
X-API-Key: {your_api_key}
User-Agent: {your_system_name}/1.0
```

### 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体响应数据
  },
  "timestamp": "2025-08-22T10:00:00Z"
}
```

## 🚌 GPS数据同步接口

### 接口信息
- **接口地址**: `POST /api/datasync/gps`
- **功能说明**: 接收车辆GPS实时位置数据（支持批量，推荐使用）
- **调用频率**: 建议30秒-2分钟一次
- **数据限制**: 单次最多1000条GPS记录

### 请求参数

#### 请求体结构
```json
{
  "dataSource": "platform_a",
  "timestamp": "2025-08-22T10:00:00Z",
  "version": "1.0",
  "batchId": "batch_20250822_001",
  "gpsData": [
    {
      "vehicleId": "BJ001",
      "lineId": 1,
      "longitude": 116.3974,
      "latitude": 39.9093,
      "speed": 25.5,
      "direction": 90,
      "gpsTime": "2025-08-22T10:00:00Z",
      "status": 1,
      "nextStopId": 123,
      "distanceToNextStop": 850.5
    }
  ]
}
```

#### 参数说明

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| dataSource | string | ✅ | 数据源标识 | "platform_a" |
| timestamp | string | ✅ | 同步时间戳(ISO 8601) | "2025-08-22T10:00:00Z" |
| version | string | ❌ | 数据版本号 | "1.0" |
| batchId | string | ❌ | 批次ID | "batch_20250822_001" |
| gpsData | array | ✅ | GPS数据数组 | 见下表 |

#### GPS数据字段说明

| 字段名 | 类型 | 必填 | 范围 | 说明 | 示例值 |
|--------|------|------|------|------|--------|
| vehicleId | string | ✅ | 1-50字符 | 车辆唯一标识 | "BJ001" |
| lineId | integer | ❌ | > 0 | 线路ID | 1 |
| longitude | decimal | ✅ | -180~180 | 经度(WGS84坐标系) | 116.3974 |
| latitude | decimal | ✅ | -90~90 | 纬度(WGS84坐标系) | 39.9093 |
| speed | decimal | ❌ | 0~200 | 速度(km/h) | 25.5 |
| direction | decimal | ❌ | 0~360 | 方向角(度) | 90 |
| gpsTime | string | ✅ | ISO 8601 | GPS时间 | "2025-08-22T10:00:00Z" |
| status | integer | ❌ | 1-4 | 车辆状态 | 1 |
| nextStopId | integer | ❌ | > 0 | 下一站点ID | 123 |
| distanceToNextStop | decimal | ❌ | ≥ 0 | 距下一站距离(米) | 850.5 |

#### 车辆状态说明
- `1`: 正常运营
- `2`: 回库途中
- `3`: 故障停运
- `4`: 离线状态

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "message": "GPS数据同步成功",
  "data": {
    "processedCount": 5,
    "failedCount": 0,
    "processingTimeMs": 125,
    "serverTimestamp": "2025-08-22T10:00:01Z"
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "message": "GPS数据同步失败",
  "data": {
    "processedCount": 3,
    "failedCount": 2,
    "errors": [
      "车辆 BJ002: 坐标超出有效范围",
      "车辆 BJ003: GPS时间不能超过当前时间"
    ],
    "processingTimeMs": 89
  }
}
```



## 🚌 线路数据同步接口

### 接口信息
- **接口地址**: `POST /api/datasync/lines`
- **功能说明**: 同步线路数据（自动处理新增/更新）
- **调用频率**: 按需调用，通常在线路数据变更时
- **数据限制**: 单次最多100条线路记录

### 请求参数
```json
{
  "dataSource": "platform_a",
  "timestamp": "2025-08-22T10:00:00Z",
  "version": "1.0",
  "batchId": "batch_lines_001",
  "lines": [
    {
      "externalLineId": "ext_line_64",
      "lineNumber": "64",
      "lineName": "64路",
      "startStopName": "火车站",
      "endStopName": "机场",
      "operationTime": "06:00-22:00",
      "direction": 0,
      "status": 1,
      "stops": [
        {
          "externalStopId": "ext_stop_001",
          "sequenceNumber": 1,
          "distanceFromStart": 0.0,
          "estimatedTime": 0,
          "isKeyStop": true,
          "status": 1
        },
        {
          "externalStopId": "ext_stop_002",
          "sequenceNumber": 2,
          "distanceFromStart": 1.2,
          "estimatedTime": 3,
          "isKeyStop": false,
          "status": 1
        }
      ]
    }
  ]
}
```

#### 线路数据字段说明
- **externalLineId**: 外部系统线路ID（必填，用于识别和更新）
- **lineNumber**: 线路编号（必填）
- **lineName**: 线路名称（必填）
- **startStopName**: 起点站名称（可选）
- **endStopName**: 终点站名称（可选）
- **operationTime**: 运营时间（可选，如"06:00-22:00"）
- **direction**: 方向，0-上行，1-下行（默认0）
- **status**: 状态，1-正常，2-停运，3-维护（默认1）
- **stops**: 线路站点序列（可选，按运行顺序排列）

#### 线路站点关系字段说明
- **externalStopId**: 外部系统站点ID（必填，必须是已同步的站点）
- **sequenceNumber**: 站点序号（必填，从1开始，按运行顺序）
- **distanceFromStart**: 距离起点距离，单位公里（可选）
- **estimatedTime**: 预计行驶时间，单位分钟（可选）
- **isKeyStop**: 是否为重点站（可选，默认false）
- **status**: 状态，1-正常，0-停用（默认1）

## 🚏 站点数据同步接口

### 接口信息
- **接口地址**: `POST /api/datasync/stops`
- **功能说明**: 同步站点数据（自动处理新增/更新）
- **调用频率**: 按需调用，通常在站点数据变更时
- **数据限制**: 单次最多500条站点记录

### 请求参数
```json
{
  "dataSource": "platform_a",
  "timestamp": "2025-08-22T10:00:00Z",
  "version": "1.0",
  "batchId": "batch_stops_001",
  "stops": [
    {
      "externalStopId": "ext_stop_001",
      "stopName": "火车站",
      "stopCode": "ST001",
      "longitude": 116.3974,
      "latitude": 39.9093,
      "address": "北京市朝阳区火车站广场",
      "district": "朝阳区",
      "status": 1
    }
  ]
}
```

#### 站点数据字段说明
- **externalStopId**: 外部系统站点ID（必填，用于识别和更新）
- **stopName**: 站点名称（必填）
- **stopCode**: 站点编码（可选）
- **longitude**: 经度，WGS84坐标系（必填，范围-180~180）
- **latitude**: 纬度，WGS84坐标系（必填，范围-90~90）
- **address**: 地址（可选）
- **district**: 所属区域（可选）
- **status**: 状态，1-正常，2-停用，3-维护（默认1）

## 🚐 车辆数据同步接口

### 接口信息
- **接口地址**: `POST /api/datasync/vehicles`
- **功能说明**: 同步车辆数据（自动处理新增/更新）
- **调用频率**: 按需调用，通常在车辆数据变更时
- **数据限制**: 单次最多200条车辆记录

### 请求参数
```json
{
  "dataSource": "platform_a",
  "timestamp": "2025-08-22T10:00:00Z",
  "version": "1.0",
  "batchId": "batch_vehicles_001",
  "vehicles": [
    {
      "externalVehicleId": "ext_vehicle_001",
      "plateNumber": "京A12345",
      "externalLineId": "ext_line_64",
      "vehicleType": "普通公交",
      "capacity": 80,
      "status": 1
    }
  ]
}
```

#### 车辆数据字段说明
- **externalVehicleId**: 外部系统车辆ID（必填，用于识别和更新）
- **plateNumber**: 车牌号（必填）
- **externalLineId**: 外部系统线路ID（必填，必须是已同步的线路）
- **vehicleType**: 车辆类型描述（可选，如"普通公交"、"BRT"、"电车"）
- **capacity**: 载客量（可选）
- **status**: 状态，1-运营中，2-回库，3-维护，4-停用（默认1）

## 🔗 线路站点关系同步接口

### 接口信息
- **接口地址**: `POST /api/datasync/line-stops`
- **功能说明**: 专门同步线路站点关系（用于增量更新）
- **调用频率**: 按需调用，适用于站点关系的调整和修复
- **数据限制**: 单次最多100个站点关系

### 使用场景
- **临时调整**: 节假日或施工期间的临时绕行
- **增量更新**: 新增或删除某个站点
- **错误修复**: 修复错误的站点关系数据
- **性能优化**: 小的调整不需要发送完整线路数据

### 请求参数
```json
{
  "dataSource": "platform_a",
  "timestamp": "2025-08-25T10:00:00Z",
  "version": "1.0",
  "batchId": "batch_line_stops_001",
  "externalLineId": "ext_line_64",
  "stops": [
    {
      "externalStopId": "ext_stop_001",
      "sequenceNumber": 1,
      "distanceFromStart": 0.0,
      "estimatedTime": 0,
      "isKeyStop": true,
      "status": 1
    },
    {
      "externalStopId": "ext_stop_002",
      "sequenceNumber": 2,
      "distanceFromStart": 1.2,
      "estimatedTime": 3,
      "isKeyStop": false,
      "status": 1
    }
  ]
}
```

### 字段说明
- **externalLineId**: 外部系统线路ID（必填，必须是已同步的线路）
- **stops**: 站点关系列表（必填，按运行顺序排列）
- 其他字段说明参考上面的"线路站点关系字段说明"

### 响应示例
```json
{
  "success": true,
  "message": "线路站点关系同步成功",
  "data": {
    "processedCount": 2,
    "failedCount": 0,
    "errors": [],
    "processingTimeMs": 120
  }
}
```

### 注意事项
- 此接口会**完全替换**指定线路的所有站点关系
- 线路必须已存在（通过线路同步接口先创建）
- 所有引用的站点必须已存在（通过站点同步接口先创建）
- 建议在非高峰期调用，避免影响实时查询



## 🏥 健康检查接口

### 接口信息
- **接口地址**: `GET /api/datasync/health`
- **功能说明**: 检查数据同步服务健康状态
- **调用频率**: 监控系统定期调用

### 响应示例
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-08-22T10:00:00Z",
    "version": "1.0.0",
    "services": {
      "database": "connected",
      "redis": "connected",
      "timescaleDB": "connected"
    }
  }
}
```

## 🔐 接口安全与认证

### API密钥管理

#### 获取API密钥
1. 联系系统管理员申请API密钥
2. 提供数据源名称和用途说明
3. 获得专用的API密钥和数据源标识

#### 密钥使用规范
- API密钥必须在请求头中传递：`X-API-Key: your_api_key`
- 密钥具有访问权限控制，只能访问授权的接口
- 密钥具有速率限制，超出限制将被拒绝访问
- 密钥需要定期更换，建议3-6个月更换一次

#### 安全注意事项
- ⚠️ **严禁**将API密钥硬编码在客户端代码中
- ⚠️ **严禁**在日志中记录完整的API密钥
- ⚠️ **严禁**通过不安全的渠道传输API密钥
- ✅ 建议使用环境变量或配置文件存储密钥
- ✅ 建议使用HTTPS协议进行数据传输

### 速率限制

| 接口类型 | 默认限制 | 说明 |
|----------|----------|------|
| GPS数据同步 | 1000次/小时 | 单个数据源限制 |
| 基础数据同步 | 100次/小时 | 单个数据源限制 |
| 状态查询 | 500次/小时 | 单个数据源限制 |
| 健康检查 | 无限制 | 监控用途 |

## 🔄 对接流程

### 第一步：环境准备

#### 1.1 获取接入资格
- 向系统管理员申请数据同步权限
- 提供数据源基本信息和技术联系人
- 签署数据安全协议

#### 1.2 获取测试环境访问权限
- 获得测试环境API地址
- 获得测试用API密钥
- 获得数据源标识符

#### 1.3 技术准备
- 确保系统支持HTTPS请求
- 确保系统支持JSON数据格式
- 准备GPS数据和基础数据的转换逻辑

### 第二步：接口测试

#### 2.1 健康检查测试
```bash
curl -X GET "https://test-api.bus-system.com/api/datasync/health"
```

#### 2.2 GPS数据同步测试
```bash
curl -X POST "https://test-api.bus-system.com/api/datasync/gps" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: test_api_key" \
  -d '{
    "dataSource": "test_platform",
    "timestamp": "2025-08-22T10:00:00Z",
    "gpsData": [
      {
        "vehicleId": "TEST001",
        "longitude": 116.3974,
        "latitude": 39.9093,
        "speed": 25.5,
        "gpsTime": "2025-08-22T10:00:00Z",
        "status": 1
      }
    ]
  }'
```

#### 2.3 状态查询测试
```bash
curl -X GET "https://test-api.bus-system.com/api/datasync/status?dataSource=test_platform" \
  -H "X-API-Key: test_api_key"
```

### 第三步：数据格式对接

#### 3.1 坐标系统一
- **要求**: 所有坐标数据必须使用WGS84坐标系
- **转换**: 如果原始数据使用其他坐标系，需要先转换为WGS84
- **精度**: 经纬度保留7位小数

#### 3.2 时间格式统一
- **格式**: ISO 8601标准格式 `YYYY-MM-DDTHH:mm:ssZ`
- **时区**: 统一使用UTC时间
- **示例**: `2025-08-22T10:00:00Z`

#### 3.3 数据验证
- 实现客户端数据验证，减少无效请求
- 确保必填字段完整性
- 验证数据范围和格式正确性

### 第四步：生产环境部署

#### 4.1 生产环境配置
- 更换为生产环境API地址
- 使用生产环境API密钥
- 配置生产环境数据源标识

#### 4.2 监控和日志
- 实现接口调用日志记录
- 监控接口响应时间和成功率
- 设置异常告警机制

#### 4.3 容错处理
- 实现网络异常重试机制
- 实现数据缓存和补发机制
- 设置合理的超时时间

## ⚠️ 重要注意事项

### 数据质量要求

#### GPS数据质量
- **时效性**: GPS数据时间不能超过当前时间5分钟
- **准确性**: 坐标数据必须在合理的地理范围内
- **完整性**: 车辆ID和GPS时间为必填字段
- **一致性**: 同一车辆的连续GPS点应该符合运动规律

#### 基础数据质量
- **唯一性**: 线路、站点、车辆ID必须全局唯一
- **关联性**: 线路站点关系必须正确
- **状态一致性**: 数据状态变更必须及时同步

### 性能优化建议

#### 批量处理
- 建议使用批量接口减少请求次数
- 单次GPS数据建议100-500条
- 避免频繁的小批量请求

#### 数据压缩
- 建议启用HTTP压缩（gzip）
- 可以减少50-70%的传输数据量
- 提升传输效率

#### 缓存策略
- 实现客户端缓存机制
- 避免重复发送相同数据
- 减少服务器压力

### 错误处理规范

#### HTTP状态码说明
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: API密钥无效或权限不足
- `429`: 请求频率超限
- `500`: 服务器内部错误

#### 重试策略
- 对于`429`状态码，建议等待后重试
- 对于`500`状态码，建议指数退避重试
- 对于`400`状态码，检查数据后重试
- 最大重试次数建议不超过3次

#### 异常处理示例
```python
# Python示例
import requests
import time
import json

def sync_gps_data(gps_data, max_retries=3):
    url = "https://api.bus-system.com/api/datasync/gps"
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "your_api_key"
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=gps_data, headers=headers, timeout=30)

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                # 速率限制，等待后重试
                time.sleep(60)
                continue
            elif response.status_code == 500:
                # 服务器错误，指数退避
                time.sleep(2 ** attempt)
                continue
            else:
                # 其他错误，记录日志并返回
                print(f"API调用失败: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"网络请求异常: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
                continue

    return None
```

```java
// Java示例
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

public class BusDataSyncClient {
    private static final String API_URL = "https://api.bus-system.com/api/datasync/gps";
    private static final String API_KEY = "your_api_key";
    private static final int MAX_RETRIES = 3;

    public String syncGpsData(String jsonData) {
        HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(30))
            .build();

        for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
            try {
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(API_URL))
                    .header("Content-Type", "application/json")
                    .header("X-API-Key", API_KEY)
                    .POST(HttpRequest.BodyPublishers.ofString(jsonData))
                    .timeout(Duration.ofSeconds(30))
                    .build();

                HttpResponse<String> response = client.send(request,
                    HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() == 200) {
                    return response.body();
                } else if (response.statusCode() == 429) {
                    Thread.sleep(60000); // 等待1分钟
                } else if (response.statusCode() == 500) {
                    Thread.sleep((long) Math.pow(2, attempt) * 1000);
                } else {
                    System.err.println("API调用失败: " + response.statusCode());
                    return null;
                }
            } catch (Exception e) {
                System.err.println("请求异常: " + e.getMessage());
                if (attempt < MAX_RETRIES - 1) {
                    try {
                        Thread.sleep((long) Math.pow(2, attempt) * 1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        return null;
    }
}

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **紧急联系电话**: +86-xxx-xxxx-xxxx
- **工作时间**: 周一至周五 9:00-18:00
- **在线文档**: https://docs.bus-system.com

### 常见问题

#### Q1: API密钥如何获取？
**A**: 联系系统管理员申请，需要提供以下信息：
- 数据源名称和类型
- 预计数据量和同步频率
- 技术联系人信息
- 数据安全协议签署

#### Q2: 为什么GPS数据同步失败？
**A**: 常见原因及解决方案：
- **坐标超出范围**: 检查经纬度是否在有效范围内（经度-180~180，纬度-90~90）
- **时间格式错误**: 确保使用ISO 8601格式的UTC时间
- **必填字段缺失**: 检查vehicleId和gpsTime字段
- **数据过期**: GPS时间不能超过当前时间5分钟

#### Q3: 如何处理网络超时？
**A**: 建议的处理方式：
- 设置30秒的请求超时时间
- 实现指数退避重试机制
- 最多重试3次
- 记录失败日志用于问题排查

#### Q4: 数据同步频率如何设置？
**A**: 推荐的同步频率：
- **GPS数据**: 30秒-2分钟一次，根据车辆数量调整，单次可发送最多1000条记录
- **基础数据**: 按需同步，通常在数据变更时调用

#### Q5: 如何验证数据同步是否成功？
**A**: 验证方法：
- 检查API响应中的success字段
- 查看processedCount和failedCount统计
- 使用状态查询接口检查同步状态
- 监控系统日志和告警

#### Q6: 坐标系转换如何处理？
**A**: 坐标系要求：
- 系统要求使用WGS84坐标系
- 如果原始数据是GCJ02或BD09，需要先转换
- 可以使用开源的坐标转换库
- 转换后的坐标精度保留7位小数

### 测试工具

#### Postman集合
我们提供了完整的Postman测试集合，包含所有接口的示例请求：
- 下载地址: https://docs.bus-system.com/postman/data-sync-api.json
- 包含测试环境和生产环境配置
- 预设了常用的测试数据

#### 在线API测试
- 测试页面: https://test-api.bus-system.com/swagger
- 可以直接在浏览器中测试接口
- 支持参数验证和响应预览

### 更新通知

#### 版本更新
- 重大版本更新会提前30天通知
- 小版本更新会提前7天通知
- 紧急安全更新会立即通知

#### 通知渠道
- 邮件通知（主要渠道）
- 系统公告
- 技术支持群通知

## 📋 附录

### 数据源标识符规范
- 格式: `{organization}_{platform}_{environment}`
- 示例: `beijing_hisense_prod`, `shanghai_platform_a_test`
- 长度: 3-50个字符
- 字符集: 字母、数字、下划线

### 错误码对照表

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| E001 | API密钥无效 | 检查密钥是否正确，联系管理员 |
| E002 | 数据源未授权 | 确认数据源标识符是否正确 |
| E003 | 请求频率超限 | 降低请求频率，等待后重试 |
| E004 | 数据格式错误 | 检查JSON格式和字段类型 |
| E005 | 坐标超出范围 | 检查经纬度数值是否合理 |
| E006 | 时间格式错误 | 使用ISO 8601格式的UTC时间 |
| E007 | 必填字段缺失 | 检查所有必填字段是否完整 |
| E008 | 数据量超出限制 | 减少单次请求的数据量 |

### 性能基准

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 响应时间 | < 500ms | 95%的请求响应时间 |
| 可用性 | > 99.9% | 月度可用性目标 |
| 吞吐量 | 1000 TPS | 峰值处理能力 |
| 数据准确性 | > 99.99% | 数据处理准确率 |

---

**文档维护**: 系统开发团队
**最后更新**: 2025-08-22
**文档版本**: v1.0
**下次审核**: 2025-09-22

**免责声明**: 本文档仅供技术对接使用，具体的商务合作和法律条款以正式合同为准。
```
