using BusSystem.Core.Entities;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// 线路服务接口
/// </summary>
public interface ILineService
{
    /// <summary>
    /// 获取线路详情
    /// </summary>
    Task<BusLine?> GetLineAsync(int lineId);

    /// <summary>
    /// 根据线路编号获取线路
    /// </summary>
    Task<BusLine?> GetLineByNumberAsync(string lineNumber, int? direction = null);

    /// <summary>
    /// 获取线路及其站点信息
    /// </summary>
    Task<BusLine?> GetLineWithStopsAsync(int lineId);

    /// <summary>
    /// 搜索线路
    /// </summary>
    Task<IEnumerable<BusLine>> SearchLinesAsync(string keyword);

    /// <summary>
    /// 获取活跃线路
    /// </summary>
    Task<IEnumerable<BusLine>> GetActiveLinesAsync();

    /// <summary>
    /// 获取线路列表（分页）
    /// </summary>
    Task<PagedResult<BusLine>> GetLinesPagedAsync(int pageNumber, int pageSize, string? keyword = null);

    /// <summary>
    /// 创建线路
    /// </summary>
    Task<BusLine> CreateLineAsync(BusLine line);

    /// <summary>
    /// 更新线路
    /// </summary>
    Task<BusLine> UpdateLineAsync(BusLine line);

    /// <summary>
    /// 删除线路
    /// </summary>
    Task DeleteLineAsync(int lineId);

    /// <summary>
    /// 获取线路统计信息
    /// </summary>
    Task<object> GetLineStatsAsync(int lineId);
}
