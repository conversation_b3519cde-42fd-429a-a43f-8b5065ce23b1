using BusSystem.Core.Entities;

namespace BusSystem.Core.Interfaces.Repositories;

/// <summary>
/// 公交线路Repository接口
/// </summary>
public interface IBusLineRepository : IRepository<BusLine>
{
    /// <summary>
    /// 根据线路编号获取线路
    /// </summary>
    Task<BusLine?> GetByLineNumberAsync(string lineNumber);

    /// <summary>
    /// 根据线路编号获取所有线路（包含不同方向）
    /// </summary>
    Task<IEnumerable<BusLine>> GetAllByLineNumberAsync(string lineNumber);

    /// <summary>
    /// 根据外部系统ID获取线路（用于数据同步）
    /// </summary>
    Task<BusLine?> GetByExternalIdAsync(string externalId);

    /// <summary>
    /// 根据线路编号和方向获取线路
    /// </summary>
    Task<BusLine?> GetByLineNumberAndDirectionAsync(string lineNumber, int direction);
    
    /// <summary>
    /// 获取线路及其站点信息
    /// </summary>
    Task<BusLine?> GetWithStopsAsync(int lineId);
    
    /// <summary>
    /// 获取线路及其车辆信息
    /// </summary>
    Task<BusLine?> GetWithVehiclesAsync(int lineId);
    
    /// <summary>
    /// 搜索线路（按线路编号或名称）
    /// </summary>
    Task<IEnumerable<BusLine>> SearchAsync(string keyword);
    
    /// <summary>
    /// 获取运营中的线路
    /// </summary>
    Task<IEnumerable<BusLine>> GetActiveAsync();
    
    /// <summary>
    /// 根据公司ID获取线路
    /// </summary>
    Task<IEnumerable<BusLine>> GetByCompanyAsync(int companyId);
}
