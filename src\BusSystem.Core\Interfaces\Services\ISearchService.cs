namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// 搜索服务接口
/// </summary>
public interface ISearchService
{
    /// <summary>
    /// 搜索线路
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="limit">结果数量限制</param>
    /// <returns>线路搜索结果</returns>
    Task<object> SearchLinesAsync(string keyword, double? longitude = null, double? latitude = null, int limit = 10);

    /// <summary>
    /// 搜索站点
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="limit">结果数量限制</param>
    /// <returns>站点搜索结果</returns>
    Task<object> SearchStopsAsync(string keyword, double? longitude = null, double? latitude = null, int limit = 10);

    /// <summary>
    /// 综合搜索（线路+站点）
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="lineLimit">线路结果数量限制</param>
    /// <param name="stopLimit">站点结果数量限制</param>
    /// <returns>综合搜索结果</returns>
    Task<object> SearchAllAsync(string keyword, double? longitude = null, double? latitude = null, int lineLimit = 5, int stopLimit = 5);

    /// <summary>
    /// 获取搜索建议（自动补全）
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="limit">建议数量限制</param>
    /// <returns>搜索建议列表</returns>
    Task<object> GetSearchSuggestionsAsync(string keyword, int limit = 10);

    /// <summary>
    /// 获取热门搜索关键词
    /// </summary>
    /// <param name="limit">关键词数量限制</param>
    /// <returns>热门关键词列表</returns>
    Task<object> GetHotSearchKeywordsAsync(int limit = 10);

    /// <summary>
    /// 记录搜索行为
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="resultCount">搜索结果数量</param>
    /// <param name="userLocation">用户位置（可选）</param>
    /// <returns>是否记录成功</returns>
    Task<bool> RecordSearchBehaviorAsync(string keyword, int resultCount, (double longitude, double latitude)? userLocation = null);

    /// <summary>
    /// 获取搜索统计信息
    /// </summary>
    /// <returns>搜索统计数据</returns>
    Task<object> GetSearchStatsAsync();
}
