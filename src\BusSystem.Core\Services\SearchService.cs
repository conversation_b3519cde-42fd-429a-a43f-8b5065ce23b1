using Microsoft.Extensions.Logging;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;

namespace BusSystem.Core.Services;

/// <summary>
/// 搜索服务实现
/// </summary>
public class SearchService : ISearchService
{
    private readonly IBusLineRepository _lineRepository;
    private readonly IBusStopRepository _stopRepository;
    private readonly ILogger<SearchService> _logger;

    public SearchService(
        IBusLineRepository lineRepository,
        IBusStopRepository stopRepository,
        ILogger<SearchService> logger)
    {
        _lineRepository = lineRepository;
        _stopRepository = stopRepository;
        _logger = logger;
    }

    public async Task<object> SearchLinesAsync(string keyword, double? longitude = null, double? latitude = null, int limit = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new { Lines = new List<object>(), Total = 0 };
            }

            var lines = await _lineRepository.SearchAsync(keyword.Trim());
            var results = lines.Take(limit).Select(line => new
            {
                LineId = line.Id,
                LineNumber = line.LineNumber,
                LineName = line.LineName,
                StartStopName = line.StartStopName,
                EndStopName = line.EndStopName,
                Direction = line.Direction,
                Status = line.Status,
                // TODO: 如果有用户位置，可以计算到起点站的距离
                Distance = longitude.HasValue && latitude.HasValue ? (double?)null : null
            });

            return new
            {
                Lines = results,
                Total = results.Count(),
                Keyword = keyword
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索线路失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<object> SearchStopsAsync(string keyword, double? longitude = null, double? latitude = null, int limit = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new { Stops = new List<object>(), Total = 0 };
            }

            var stops = await _stopRepository.SearchByNameAsync(keyword.Trim());
            var results = stops.Take(limit).Select(stop => new
            {
                StopId = stop.Id,
                StopName = stop.StopName,
                StopCode = stop.StopCode,
                Address = stop.Address,
                District = stop.District,
                Location = new
                {
                    Longitude = stop.Longitude,
                    Latitude = stop.Latitude
                },
                Distance = longitude.HasValue && latitude.HasValue
                    ? CalculateDistance(latitude.Value, longitude.Value, stop.Latitude, stop.Longitude)
                    : (double?)null
            });

            // 如果有用户位置，按距离排序
            if (longitude.HasValue && latitude.HasValue)
            {
                results = results.OrderBy(s => s.Distance ?? double.MaxValue);
            }

            return new
            {
                Stops = results,
                Total = results.Count(),
                Keyword = keyword
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索站点失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<object> SearchAllAsync(string keyword, double? longitude = null, double? latitude = null, int lineLimit = 5, int stopLimit = 5)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new
                {
                    Lines = new List<object>(),
                    Stops = new List<object>(),
                    Total = 0
                };
            }

            // 并行搜索线路和站点
            var lineTask = SearchLinesAsync(keyword, longitude, latitude, lineLimit);
            var stopTask = SearchStopsAsync(keyword, longitude, latitude, stopLimit);

            await Task.WhenAll(lineTask, stopTask);

            var lineResult = await lineTask;
            var stopResult = await stopTask;

            var lineData = lineResult as dynamic;
            var stopData = stopResult as dynamic;

            return new
            {
                Lines = lineData?.Lines ?? new List<object>(),
                Stops = stopData?.Stops ?? new List<object>(),
                Total = (lineData?.Total ?? 0) + (stopData?.Total ?? 0),
                Keyword = keyword
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "综合搜索失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<object> GetSearchSuggestionsAsync(string keyword, int limit = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword) || keyword.Length < 2)
            {
                return new { Suggestions = new List<string>() };
            }

            // TODO: 实现智能搜索建议
            // 可以基于历史搜索记录、热门搜索等
            var suggestions = new List<string>();

            // 简单实现：基于线路名称和站点名称的前缀匹配
            var lines = await _lineRepository.SearchAsync(keyword);
            var stops = await _stopRepository.SearchByNameAsync(keyword);

            suggestions.AddRange(lines.Take(limit / 2).Select(l => l.LineName));
            suggestions.AddRange(stops.Take(limit / 2).Select(s => s.StopName));

            return new
            {
                Suggestions = suggestions.Distinct().Take(limit)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索建议失败，关键词: {Keyword}", keyword);
            throw;
        }
    }

    public async Task<object> GetHotSearchKeywordsAsync(int limit = 10)
    {
        try
        {
            // 实际实现应该查询search_behavior_stats表
            // 基于搜索频率、时间权重、用户行为等综合计算
            await Task.CompletedTask;

            // 模拟从数据库获取热门关键词
            var hotKeywords = await GetHotKeywordsFromDatabase(limit);

            return new
            {
                HotKeywords = hotKeywords,
                UpdateTime = DateTime.UtcNow,
                Period = "Last 7 days",
                TotalSearches = hotKeywords.Cast<dynamic>().Sum(k => (int)k.SearchCount)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取热门搜索关键词失败");
            throw;
        }
    }

    public async Task<bool> RecordSearchBehaviorAsync(string keyword, int resultCount, (double longitude, double latitude)? userLocation = null)
    {
        try
        {
            // 实际实现应该记录到user_search_history和search_behavior_stats表
            await RecordToSearchHistoryAsync(keyword, resultCount, userLocation);
            await UpdateSearchStatsAsync(keyword, resultCount);

            _logger.LogInformation("记录搜索行为成功，关键词: {Keyword}, 结果数: {ResultCount}, 位置: {Location}",
                keyword, resultCount, userLocation);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录搜索行为失败，关键词: {Keyword}", keyword);
            return false;
        }
    }

    public async Task<object> GetSearchStatsAsync()
    {
        try
        {
            // 模拟获取搜索统计数据
            await Task.CompletedTask;

            return new
            {
                TotalSearches = 15420,
                TodaySearches = 1250,
                PopularKeywords = await GetHotKeywordsFromDatabase(5),
                SearchTrends = new[]
                {
                    new { Date = DateTime.Today.AddDays(-6), Searches = 1100 },
                    new { Date = DateTime.Today.AddDays(-5), Searches = 1200 },
                    new { Date = DateTime.Today.AddDays(-4), Searches = 1350 },
                    new { Date = DateTime.Today.AddDays(-3), Searches = 1180 },
                    new { Date = DateTime.Today.AddDays(-2), Searches = 1420 },
                    new { Date = DateTime.Today.AddDays(-1), Searches = 1380 },
                    new { Date = DateTime.Today, Searches = 1250 }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取搜索统计失败");
            throw;
        }
    }

    #region 私有方法

    private async Task<object[]> GetHotKeywordsFromDatabase(int limit)
    {
        // 模拟数据库查询结果
        await Task.CompletedTask;

        var allKeywords = new[]
        {
            new { Keyword = "1路", SearchCount = 1250, Trend = "up", Category = "公交线路" },
            new { Keyword = "地铁站", SearchCount = 980, Trend = "stable", Category = "交通枢纽" },
            new { Keyword = "火车站", SearchCount = 756, Trend = "down", Category = "交通枢纽" },
            new { Keyword = "机场", SearchCount = 620, Trend = "up", Category = "交通枢纽" },
            new { Keyword = "医院", SearchCount = 580, Trend = "stable", Category = "生活服务" },
            new { Keyword = "学校", SearchCount = 520, Trend = "up", Category = "生活服务" },
            new { Keyword = "商场", SearchCount = 480, Trend = "stable", Category = "生活服务" },
            new { Keyword = "公园", SearchCount = 420, Trend = "down", Category = "休闲娱乐" },
            new { Keyword = "体育馆", SearchCount = 380, Trend = "up", Category = "休闲娱乐" },
            new { Keyword = "2路", SearchCount = 350, Trend = "stable", Category = "公交线路" },
            new { Keyword = "3路", SearchCount = 320, Trend = "up", Category = "公交线路" },
            new { Keyword = "银行", SearchCount = 290, Trend = "stable", Category = "生活服务" }
        };

        return allKeywords.Take(limit).ToArray();
    }

    private async Task RecordToSearchHistoryAsync(string keyword, int resultCount, (double longitude, double latitude)? userLocation)
    {
        // 模拟记录到user_search_history表
        await Task.CompletedTask;

        var searchRecord = new
        {
            Keyword = keyword,
            ResultCount = resultCount,
            UserLocation = userLocation,
            SearchTime = DateTime.UtcNow,
            SessionId = Guid.NewGuid().ToString(), // 实际应该从请求上下文获取
            UserAgent = "Unknown", // 实际应该从HTTP头获取
            ClientIp = "Unknown" // 实际应该从请求上下文获取
        };

        _logger.LogDebug("搜索历史记录: {@SearchRecord}", searchRecord);
    }

    private async Task UpdateSearchStatsAsync(string keyword, int resultCount)
    {
        // 模拟更新search_behavior_stats表
        await Task.CompletedTask;

        var statsUpdate = new
        {
            Keyword = keyword,
            SearchCount = 1, // 增加1次搜索
            TotalResults = resultCount,
            LastSearchTime = DateTime.UtcNow,
            UpdateTime = DateTime.UtcNow
        };

        _logger.LogDebug("搜索统计更新: {@StatsUpdate}", statsUpdate);
    }

    private static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLon = (lon2 - lon1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    #endregion
}
