[2025-08-25 10:06:07.311 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:06:07.339 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:06:07.341 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:06:07.342 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:06:46.982 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF3FG5RNVCQ:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FG5RNVCQ"}
[2025-08-25 10:06:48.034 +08:00 INF] ApiKey was not authenticated. Failure message: 缺少API密钥 {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3FG5RNVCQ:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FG5RNVCQ"}
[2025-08-25 10:06:48.043 +08:00 INF] AuthenticationScheme: ApiKey was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3FG5RNVCQ:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FG5RNVCQ"}
[2025-08-25 10:06:48.049 +08:00 INF] HTTP GET /api/home/<USER>"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:06:57.667 +08:00 WRN] 无效的API密钥: fron****2345 {"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3FG5RNVCR:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FG5RNVCR"}
[2025-08-25 10:06:57.669 +08:00 INF] ApiKey was not authenticated. Failure message: 无效的API密钥 {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3FG5RNVCR:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FG5RNVCR"}
[2025-08-25 10:06:57.673 +08:00 INF] AuthenticationScheme: ApiKey was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3FG5RNVCR:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FG5RNVCR"}
[2025-08-25 10:06:57.677 +08:00 INF] HTTP GET /api/home/<USER>"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:08:46.184 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:08:46.212 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:08:46.214 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:08:46.215 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:09:17.633 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF3FHIP3E2E:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3FHIP3E2E"}
[2025-08-25 10:09:19.949 +08:00 INF] HTTP GET /api/home/<USER>"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:09:30.497 +08:00 INF] HTTP POST /api/datasync/gps responded 401 in 124.1332 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:09:39.501 +08:00 INF] HTTP GET /api/admin/api-keys responded 200 in 13.3818 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:09:50.431 +08:00 WRN] API密钥 测试前端应用 没有访问 GET /api/admin/api-keys 的权限 {"SourceContext":"BusSystem.Api.Authorization.ApiPermissionAuthorizationHandler","RequestId":"0HNF3FHIP3E2H:00000001","RequestPath":"/api/admin/api-keys","ConnectionId":"0HNF3FHIP3E2H"}
[2025-08-25 10:09:50.434 +08:00 INF] AuthenticationScheme: ApiKey was forbidden. {"EventId":{"Id":13,"Name":"AuthenticationSchemeForbidden"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3FHIP3E2H:00000001","RequestPath":"/api/admin/api-keys","ConnectionId":"0HNF3FHIP3E2H"}
[2025-08-25 10:09:50.437 +08:00 INF] HTTP GET /api/admin/api-keys responded 403 in 8.2086 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:52:36.207 +08:00 INF] Now listening on: http://localhost:5205 {"EventId":{"Id":14,"Name":"ListeningOnAddress"},"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:52:36.233 +08:00 INF] Application started. Press Ctrl+C to shut down. {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:52:36.234 +08:00 INF] Hosting environment: Development {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:52:36.235 +08:00 INF] Content root path: E:\Coding\Solution\实时公交\src\BusSystem.Api {"SourceContext":"Microsoft.Hosting.Lifetime"}
[2025-08-25 10:53:02.994 +08:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
[2025-08-25 10:53:03.748 +08:00 WRN] The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results. {"EventId":{"Id":10103,"Name":"Microsoft.EntityFrameworkCore.Query.FirstWithoutOrderByAndFilterWarning"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
[2025-08-25 10:53:03.939 +08:00 ERR] Failed executing DbCommand (51ms) [Parameters=[p0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT b."AllowedIps", b."CreatedAt", b."Description", b."ExpiresAt", b."Id", b."IsActive", b."KeyName", b."KeyType", b."LastUsedAt", b."OwnerName", b."RateLimitPerDay", b."RateLimitPerHour", b."UsageCount"
FROM (

                SELECT ak.id, ak.key_name, ak.key_type, ak.description, ak.owner_name, 
                       ak.is_active, ak.rate_limit_per_hour, ak.rate_limit_per_day, 
                       ak.allowed_ips, ak.expires_at, ak.last_used_at, ak.usage_count, ak.created_at
                FROM api_keys ak 
                WHERE ak.api_key_hash = @p0 AND ak.is_active = true
) AS b
LIMIT 1 {"EventId":{"Id":20102,"Name":"Microsoft.EntityFrameworkCore.Database.Command.CommandError"},"SourceContext":"Microsoft.EntityFrameworkCore.Database.Command","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
[2025-08-25 10:53:03.972 +08:00 ERR] An exception occurred while iterating over the results of a query for context type 'BusSystem.Infrastructure.Data.BusSystemDbContext'.
Npgsql.PostgresException (0x80004005): 42P01: relation "api_keys" does not exist

POSITION: 517
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "api_keys" does not exist
    Position: 517
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable {"EventId":{"Id":10100,"Name":"Microsoft.EntityFrameworkCore.Query.QueryIterationFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Query","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
Npgsql.PostgresException (0x80004005): 42P01: relation "api_keys" does not exist

POSITION: 517
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "api_keys" does not exist
    Position: 517
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable
[2025-08-25 10:53:03.981 +08:00 ERR] 验证API密钥时发生异常 {"SourceContext":"BusSystem.Core.Services.ApiKeyService","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
Npgsql.PostgresException (0x80004005): 42P01: relation "api_keys" does not exist

POSITION: 517
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at BusSystem.Infrastructure.Repositories.ApiKeyRepository.GetByHashAsync(String apiKeyHash) in E:\Coding\Solution\实时公交\src\BusSystem.Infrastructure\Repositories\ApiKeyRepository.cs:line 29
   at BusSystem.Core.Services.ApiKeyService.ValidateApiKeyAsync(String apiKey) in E:\Coding\Solution\实时公交\src\BusSystem.Core\Services\ApiKeyService.cs:line 37
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "api_keys" does not exist
    Position: 517
    File: parse_relation.c
    Line: 1392
    Routine: parserOpenTable
[2025-08-25 10:53:03.992 +08:00 WRN] 无效的API密钥: fron****2345 {"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
[2025-08-25 10:53:03.996 +08:00 INF] ApiKey was not authenticated. Failure message: 无效的API密钥 {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
[2025-08-25 10:53:04.005 +08:00 INF] AuthenticationScheme: ApiKey was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
[2025-08-25 10:53:04.010 +08:00 INF] HTTP GET /api/home/<USER>"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:53:04.078 +08:00 ERR] 处理API访问日志时发生异常 {"SourceContext":"BusSystem.Api.Middleware.ApiAccessLoggingMiddleware","RequestId":"0HNF3GA16GE3S:00000001","RequestPath":"/api/home/<USER>","ConnectionId":"0HNF3GA16GE3S"}
System.ObjectDisposedException: Cannot access a closed Stream.
   at System.IO.MemoryStream.set_Position(Int64 value)
   at BusSystem.Api.Middleware.ApiAccessLoggingMiddleware.LogApiAccessAsync(HttpContext context, String requestBody, MemoryStream responseBody, Int64 responseTimeMs) in E:\Coding\Solution\实时公交\src\BusSystem.Api\Middleware\ApiAccessLoggingMiddleware.cs:line 111
[2025-08-25 10:53:14.692 +08:00 INF] ApiKey was not authenticated. Failure message: 缺少API密钥 {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3GA16GE3T:00000001","RequestPath":"/api/datasync/gps","ConnectionId":"0HNF3GA16GE3T"}
[2025-08-25 10:53:14.702 +08:00 INF] AuthenticationScheme: ApiKey was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"BusSystem.Api.Authentication.ApiKeyAuthenticationHandler","RequestId":"0HNF3GA16GE3T:00000001","RequestPath":"/api/datasync/gps","ConnectionId":"0HNF3GA16GE3T"}
[2025-08-25 10:53:14.706 +08:00 INF] HTTP POST /api/datasync/gps responded 401 in 16.5940 ms {"SourceContext":"Serilog.AspNetCore.RequestLoggingMiddleware"}
[2025-08-25 10:53:14.706 +08:00 ERR] 处理API访问日志时发生异常 {"SourceContext":"BusSystem.Api.Middleware.ApiAccessLoggingMiddleware","RequestId":"0HNF3GA16GE3T:00000001","RequestPath":"/api/datasync/gps","ConnectionId":"0HNF3GA16GE3T"}
System.ObjectDisposedException: Cannot access a closed Stream.
   at System.IO.MemoryStream.set_Position(Int64 value)
   at BusSystem.Api.Middleware.ApiAccessLoggingMiddleware.LogApiAccessAsync(HttpContext context, String requestBody, MemoryStream responseBody, Int64 responseTimeMs) in E:\Coding\Solution\实时公交\src\BusSystem.Api\Middleware\ApiAccessLoggingMiddleware.cs:line 111
