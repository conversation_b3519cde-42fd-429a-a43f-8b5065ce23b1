using System.Linq.Expressions;
using BusSystem.Shared.Models.Common;

namespace BusSystem.Core.Interfaces.Repositories;

/// <summary>
/// 基础Repository接口
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface IRepository<T> where T : class
{
    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    Task<T?> GetByIdAsync(int id);
    
    /// <summary>
    /// 获取所有实体
    /// </summary>
    Task<IEnumerable<T>> GetAllAsync();
    
    /// <summary>
    /// 根据条件查找实体
    /// </summary>
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// 根据条件获取单个实体
    /// </summary>
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// 分页查询
    /// </summary>
    Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool orderByDescending = false);
    
    /// <summary>
    /// 添加实体
    /// </summary>
    Task<T> AddAsync(T entity);
    
    /// <summary>
    /// 批量添加实体
    /// </summary>
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    
    /// <summary>
    /// 更新实体
    /// </summary>
    Task<T> UpdateAsync(T entity);
    
    /// <summary>
    /// 删除实体
    /// </summary>
    Task DeleteAsync(T entity);
    
    /// <summary>
    /// 根据ID删除实体
    /// </summary>
    Task DeleteAsync(int id);
    
    /// <summary>
    /// 批量删除实体
    /// </summary>
    Task DeleteRangeAsync(IEnumerable<T> entities);
    
    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    Task<bool> ExistsAsync(int id);
    
    /// <summary>
    /// 根据条件检查实体是否存在
    /// </summary>
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// 获取实体数量
    /// </summary>
    Task<int> CountAsync();
    
    /// <summary>
    /// 根据条件获取实体数量
    /// </summary>
    Task<int> CountAsync(Expression<Func<T, bool>> predicate);
}
