# 架构分层设计指南

## 📋 概述

本文档详细说明了实时公交系统的架构分层设计原则，明确各层的职责划分和服务分布规则。

## 🏗️ 分层架构原则

### **Clean Architecture 分层模型**

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                      │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              BusSystem.Api                              ││
│  │  Controllers, Middleware, Authentication, etc.         ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                    Application Layer                       │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              BusSystem.Core                             ││
│  │  Business Logic, Domain Services, Interfaces           ││
│  │  ├─ Services/           (业务服务实现)                  ││
│  │  ├─ Interfaces/Services/ (业务服务接口)                ││
│  │  ├─ Interfaces/Repositories/ (数据访问接口)            ││
│  │  └─ Entities/           (领域实体)                      ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                  Infrastructure Layer                      │
│  ┌─────────────────────────────────────────────────────────┐│
│  │           BusSystem.Infrastructure                      ││
│  │  Data Access, External Services, Technical Concerns    ││
│  │  ├─ Repositories/       (数据访问实现)                 ││
│  │  ├─ Services/           (基础设施服务)                  ││
│  │  └─ Data/               (数据库上下文)                  ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│                     Shared Layer                           │
│  ┌─────────────────────────────────────────────────────────┐│
│  │             BusSystem.Shared                            ││
│  │  Common Models, Enums, Constants, Utilities            ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 🎯 各层职责详解

### **1. Core层（业务核心层）**

#### **职责范围：**
- ✅ **业务逻辑实现**：核心业务规则和流程
- ✅ **领域服务**：跨实体的业务操作
- ✅ **接口定义**：定义数据访问和外部服务的抽象
- ✅ **领域实体**：业务对象和值对象

#### **应该包含：**
```
Core/
├── Services/                    # 业务服务实现
│   ├── ApiKeyService.cs        # ✅ 业务逻辑
│   ├── LineService.cs          # ✅ 业务逻辑
│   ├── StopService.cs          # ✅ 业务逻辑
│   ├── RealtimeService.cs      # ✅ 业务逻辑
│   ├── PredictionService.cs    # ✅ 业务逻辑
│   └── NotificationService.cs  # ✅ 业务逻辑
├── Interfaces/
│   ├── Services/               # 业务服务接口
│   │   ├── IApiKeyService.cs
│   │   ├── ILineService.cs
│   │   └── ...
│   └── Repositories/           # 数据访问接口
│       ├── IApiKeyRepository.cs
│       ├── IBusLineRepository.cs
│       └── ...
└── Entities/                   # 领域实体
    ├── BusLine.cs
    ├── BusStop.cs
    └── ...
```

#### **不应该包含：**
- ❌ 数据库访问实现
- ❌ HTTP客户端调用
- ❌ 文件系统操作
- ❌ 外部API调用

### **2. Infrastructure层（基础设施层）**

#### **职责范围：**
- ✅ **数据访问实现**：Repository模式实现
- ✅ **外部服务集成**：Redis、HTTP客户端等
- ✅ **技术关注点**：缓存、日志、配置等
- ✅ **数据库上下文**：EF Core DbContext

#### **应该包含：**
```
Infrastructure/
├── Repositories/               # 数据访问实现
│   ├── ApiKeyRepository.cs    # ✅ 数据访问
│   ├── BusLineRepository.cs   # ✅ 数据访问
│   ├── BusStopRepository.cs   # ✅ 数据访问
│   └── Repository.cs          # ✅ 基础Repository
├── Services/                   # 基础设施服务
│   ├── RedisService.cs        # ✅ 外部服务
│   ├── HttpClientService.cs   # ✅ 外部服务
│   └── FileStorageService.cs  # ✅ 外部服务
└── Data/                       # 数据库上下文
    ├── BusSystemDbContext.cs  # ✅ 数据库上下文
    └── TimescaleDbContext.cs  # ✅ 时序数据库上下文
```

#### **不应该包含：**
- ❌ 业务逻辑
- ❌ 业务规则验证
- ❌ 领域服务

## 📊 **当前服务分布分析**

### **✅ 正确的分布：**

#### **Core/Services（业务服务）：**
- `ApiKeyService.cs` ✅ - API密钥管理业务逻辑
- `LineService.cs` ✅ - 线路业务逻辑
- `StopService.cs` ✅ - 站点业务逻辑
- `RealtimeService.cs` ✅ - 实时数据业务逻辑
- `PredictionService.cs` ✅ - 到站预测业务逻辑
- `NotificationService.cs` ✅ - 通知业务逻辑
- `HomePageService.cs` ✅ - 首页聚合业务逻辑
- `DataSyncService.cs` ✅ - 数据同步业务逻辑
- `CoordinateTransformService.cs` ✅ - 坐标转换业务逻辑

#### **Infrastructure/Services（基础设施服务）：**
- `RedisService.cs` ✅ - 缓存服务（外部依赖）

#### **Infrastructure/Repositories（数据访问）：**
- `ApiKeyRepository.cs` ✅ - 数据访问实现
- `BusLineRepository.cs` ✅ - 数据访问实现
- `BusStopRepository.cs` ✅ - 数据访问实现
- `VehicleRepository.cs` ✅ - 数据访问实现
- `VehiclePositionRepository.cs` ✅ - 数据访问实现

### **架构合理性评估：**

| 服务类型 | 当前位置 | 是否合理 | 说明 |
|----------|----------|----------|------|
| 业务服务 | Core/Services | ✅ 合理 | 包含业务逻辑，应该在Core层 |
| 数据访问 | Infrastructure/Repositories | ✅ 合理 | 技术实现，应该在Infrastructure层 |
| 缓存服务 | Infrastructure/Services | ✅ 合理 | 外部依赖，应该在Infrastructure层 |

## 🔧 **服务分类指导原则**

### **放在Core/Services的服务：**
```csharp
// ✅ 包含业务逻辑的服务
public class LineService : ILineService
{
    public async Task<BusLine?> GetLineAsync(int lineId)
    {
        // 业务逻辑：验证、计算、规则处理
        if (lineId <= 0) throw new ArgumentException("Invalid line ID");
        
        var line = await _lineRepository.GetByIdAsync(lineId);
        
        // 业务规则：只返回激活的线路
        return line?.IsActive == true ? line : null;
    }
}
```

### **放在Infrastructure/Services的服务：**
```csharp
// ✅ 外部服务集成
public class RedisService : IRedisService
{
    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        // 技术实现：Redis操作，无业务逻辑
        var value = await _database.StringGetAsync(key);
        return value.HasValue ? JsonSerializer.Deserialize<T>(value) : null;
    }
}

// ✅ HTTP客户端服务
public class ExternalApiService : IExternalApiService
{
    public async Task<WeatherInfo> GetWeatherAsync(double lat, double lng)
    {
        // 技术实现：外部API调用，无业务逻辑
        var response = await _httpClient.GetAsync($"/weather?lat={lat}&lng={lng}");
        return await response.Content.ReadFromJsonAsync<WeatherInfo>();
    }
}
```

## 🎯 **判断标准**

### **放在Core层的标准：**
- ✅ 包含业务规则和逻辑
- ✅ 需要业务知识才能理解
- ✅ 会因为业务需求变化而修改
- ✅ 可以独立于技术实现进行单元测试

### **放在Infrastructure层的标准：**
- ✅ 纯技术实现，无业务逻辑
- ✅ 与外部系统交互（数据库、缓存、API等）
- ✅ 可以被不同的技术方案替换
- ✅ 实现Core层定义的接口

## 💡 **最佳实践建议**

### **1. 依赖方向**
```
Api → Core → Infrastructure
     ↑         ↓
   Shared ←────┘
```

### **2. 接口定义**
- 所有接口定义在Core/Interfaces中
- Infrastructure实现Core定义的接口
- Core不依赖Infrastructure的具体实现

### **3. 服务注册**
```csharp
// Program.cs中的依赖注入
// Core服务（业务逻辑）
builder.Services.AddScoped<ILineService, LineService>();
builder.Services.AddScoped<IStopService, StopService>();

// Infrastructure服务（技术实现）
builder.Services.AddScoped<IApiKeyRepository, ApiKeyRepository>();
builder.Services.AddScoped<IRedisService, RedisService>();
```

### **4. 测试策略**
- Core服务：业务逻辑单元测试，Mock Repository
- Infrastructure服务：集成测试，测试与外部系统的交互

## 🚀 **总结**

当前的架构分层是**合理和正确的**：

1. **✅ Core/Services** - 包含业务逻辑的服务，正确放置
2. **✅ Infrastructure/Repositories** - 数据访问实现，正确放置  
3. **✅ Infrastructure/Services** - 外部服务集成，正确放置

这种分层遵循了Clean Architecture的原则，实现了：
- 业务逻辑与技术实现的分离
- 高内聚、低耦合的设计
- 便于测试和维护的架构

---

**架构设计**: Clean Architecture + DDD  
**分层原则**: 依赖倒置、接口隔离  
**维护状态**: 架构合理，持续优化
