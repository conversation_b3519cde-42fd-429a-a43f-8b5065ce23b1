using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;
using NetTopologySuite.Geometries;

namespace BusSystem.Infrastructure.Data;

/// <summary>
/// 主数据库上下文（PostgreSQL + PostGIS）
/// </summary>
public class BusSystemDbContext : DbContext
{
    public BusSystemDbContext(DbContextOptions<BusSystemDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 公交线路
    /// </summary>
    public DbSet<BusLine> BusLines { get; set; }

    /// <summary>
    /// 公交站点
    /// </summary>
    public DbSet<BusStop> BusStops { get; set; }

    /// <summary>
    /// 线路站点关联
    /// </summary>
    public DbSet<LineStop> LineStops { get; set; }

    /// <summary>
    /// 车辆
    /// </summary>
    public DbSet<Vehicle> Vehicles { get; set; }

    /// <summary>
    /// API密钥
    /// </summary>
    public DbSet<ApiKey> ApiKeys { get; set; }

    /// <summary>
    /// API权限
    /// </summary>
    public DbSet<ApiPermission> ApiPermissions { get; set; }

    /// <summary>
    /// API密钥权限关联
    /// </summary>
    public DbSet<ApiKeyPermission> ApiKeyPermissions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置表名
        modelBuilder.Entity<BusLine>().ToTable("bus_lines");
        modelBuilder.Entity<BusStop>().ToTable("bus_stops");
        modelBuilder.Entity<LineStop>().ToTable("line_stops");
        modelBuilder.Entity<Vehicle>().ToTable("vehicles");
        modelBuilder.Entity<ApiKey>().ToTable("api_keys");
        modelBuilder.Entity<ApiPermission>().ToTable("api_permissions");
        modelBuilder.Entity<ApiKeyPermission>().ToTable("api_key_permissions");

        ConfigureBusLine(modelBuilder);
        ConfigureBusStop(modelBuilder);
        ConfigureLineStop(modelBuilder);
        ConfigureVehicle(modelBuilder);
        ConfigureApiKey(modelBuilder);
        ConfigureApiPermission(modelBuilder);
        ConfigureApiKeyPermission(modelBuilder);
    }

    /// <summary>
    /// 配置公交线路实体
    /// </summary>
    private static void ConfigureBusLine(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<BusLine>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.LineNumber).HasMaxLength(20).IsRequired();
        entity.Property(e => e.LineName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.StartStopName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.EndStopName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.TicketPrice).HasColumnType("decimal(4,2)");
        entity.Property(e => e.TotalDistance).HasColumnType("decimal(8,2)");
        entity.Property(e => e.CreatedAt).HasColumnName("created_at");
        entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

        // PostGIS几何类型配置
        entity.Property(e => e.RouteGeometry)
            .HasColumnName("route_geometry")
            .HasColumnType("geometry(LINESTRING,4326)");

        // 索引
        entity.HasIndex(e => e.LineNumber).HasDatabaseName("idx_lines_number");
        entity.HasIndex(e => e.Status).HasDatabaseName("idx_lines_status");
        entity.HasIndex(e => e.CompanyId).HasDatabaseName("idx_lines_company");
    }

    /// <summary>
    /// 配置公交站点实体
    /// </summary>
    private static void ConfigureBusStop(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<BusStop>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.StopName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.StopCode).HasMaxLength(20);
        entity.Property(e => e.Address).HasMaxLength(200);
        entity.Property(e => e.District).HasMaxLength(50);
        entity.Property(e => e.CreatedAt).HasColumnName("created_at");
        entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

        // PostGIS点类型配置
        entity.Property(e => e.Location)
            .HasColumnName("location")
            .HasColumnType("geometry(POINT,4326)")
            .IsRequired();

        // JSONB配置
        entity.Property(e => e.Facilities)
            .HasColumnType("jsonb");

        // 索引
        entity.HasIndex(e => e.StopName).HasDatabaseName("idx_stops_name");
        entity.HasIndex(e => e.StopCode).HasDatabaseName("idx_stops_code");
        entity.HasIndex(e => e.District).HasDatabaseName("idx_stops_district");
        entity.HasIndex(e => e.Location).HasDatabaseName("idx_stops_location");
    }

    /// <summary>
    /// 配置线路站点关联实体
    /// </summary>
    private static void ConfigureLineStop(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<LineStop>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.DistanceFromStart).HasColumnType("decimal(8,2)");
        entity.Property(e => e.CreatedAt).HasColumnName("created_at");
        entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

        // 外键关系
        entity.HasOne(e => e.Line)
            .WithMany(l => l.LineStops)
            .HasForeignKey(e => e.LineId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(e => e.Stop)
            .WithMany(s => s.LineStops)
            .HasForeignKey(e => e.StopId)
            .OnDelete(DeleteBehavior.Cascade);

        // 索引
        entity.HasIndex(e => new { e.LineId, e.SequenceNumber })
            .HasDatabaseName("idx_line_stops_line_sequence");
        entity.HasIndex(e => e.StopId).HasDatabaseName("idx_line_stops_stop");
    }

    /// <summary>
    /// 配置车辆实体
    /// </summary>
    private static void ConfigureVehicle(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<Vehicle>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.VehicleNumber).HasMaxLength(50).IsRequired();
        entity.Property(e => e.PlateNumber).HasMaxLength(20);
        entity.Property(e => e.CreatedAt).HasColumnName("created_at");
        entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

        // 外键关系
        entity.HasOne(e => e.Line)
            .WithMany(l => l.Vehicles)
            .HasForeignKey(e => e.LineId)
            .OnDelete(DeleteBehavior.Restrict);

        // 索引
        entity.HasIndex(e => e.VehicleNumber).HasDatabaseName("idx_vehicles_number");
        entity.HasIndex(e => e.LineId).HasDatabaseName("idx_vehicles_line");
        entity.HasIndex(e => e.Status).HasDatabaseName("idx_vehicles_status");
    }

    /// <summary>
    /// 配置API密钥实体
    /// </summary>
    private static void ConfigureApiKey(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ApiKey>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.KeyName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.ApiKeyHash).HasMaxLength(64).IsRequired();
        entity.Property(e => e.KeyType).HasMaxLength(20).IsRequired();
        entity.Property(e => e.Description).HasMaxLength(500);
        entity.Property(e => e.OwnerName).HasMaxLength(100);
        entity.Property(e => e.OwnerContact).HasMaxLength(200);
        entity.Property(e => e.CreatedAt).HasColumnName("created_at");
        entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

        // PostgreSQL数组类型
        entity.Property(e => e.AllowedIps)
            .HasColumnType("text[]");

        // 索引
        entity.HasIndex(e => e.ApiKeyHash).IsUnique().HasDatabaseName("idx_api_keys_hash");
        entity.HasIndex(e => e.KeyType).HasDatabaseName("idx_api_keys_type");
        entity.HasIndex(e => e.IsActive).HasDatabaseName("idx_api_keys_active");
    }

    /// <summary>
    /// 配置API权限实体
    /// </summary>
    private static void ConfigureApiPermission(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ApiPermission>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.PermissionName).HasMaxLength(100).IsRequired();
        entity.Property(e => e.EndpointPattern).HasMaxLength(200).IsRequired();
        entity.Property(e => e.HttpMethods).HasMaxLength(100).IsRequired();
        entity.Property(e => e.Description).HasMaxLength(500);
        entity.Property(e => e.CreatedAt).HasColumnName("created_at");
        entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");

        // 索引
        entity.HasIndex(e => e.PermissionName).IsUnique().HasDatabaseName("idx_api_permissions_name");
        entity.HasIndex(e => e.IsActive).HasDatabaseName("idx_api_permissions_active");
    }

    /// <summary>
    /// 配置API密钥权限关联实体
    /// </summary>
    private static void ConfigureApiKeyPermission(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ApiKeyPermission>();

        entity.HasKey(e => new { e.ApiKeyId, e.PermissionId });
        entity.Property(e => e.GrantedBy).HasMaxLength(100);

        // 外键关系
        entity.HasOne(e => e.ApiKey)
            .WithMany(ak => ak.ApiKeyPermissions)
            .HasForeignKey(e => e.ApiKeyId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(e => e.Permission)
            .WithMany(ap => ap.ApiKeyPermissions)
            .HasForeignKey(e => e.PermissionId)
            .OnDelete(DeleteBehavior.Cascade);

        // 索引
        entity.HasIndex(e => e.ApiKeyId).HasDatabaseName("idx_api_key_permissions_key");
        entity.HasIndex(e => e.PermissionId).HasDatabaseName("idx_api_key_permissions_permission");
    }
}
