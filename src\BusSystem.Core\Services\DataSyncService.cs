using System.Diagnostics;
using Microsoft.Extensions.Logging;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Core.Entities;
using BusSystem.Shared.Models.DataSync;

namespace BusSystem.Core.Services;

/// <summary>
/// 数据同步服务实现
/// </summary>
public class DataSyncService : IDataSyncService
{
    private readonly IVehiclePositionRepository _positionRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IBusStopRepository _stopRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILineStopRepository _lineStopRepository;
    private readonly IRealtimeService _realtimeService;
    private readonly ILogger<DataSyncService> _logger;

    public DataSyncService(
        IVehiclePositionRepository positionRepository,
        IBusLineRepository lineRepository,
        IBusStopRepository stopRepository,
        IVehicleRepository vehicleRepository,
        ILineStopRepository lineStopRepository,
        IRealtimeService realtimeService,
        ILogger<DataSyncService> logger)
    {
        _positionRepository = positionRepository;
        _lineRepository = lineRepository;
        _stopRepository = stopRepository;
        _vehicleRepository = vehicleRepository;
        _lineStopRepository = lineStopRepository;
        _realtimeService = realtimeService;
        _logger = logger;
    }

    public async Task<SyncDataResponse> SyncGpsDataAsync(GpsDataSyncRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new SyncDataResponse();
        
        try
        {
            _logger.LogInformation("开始同步GPS数据，数据源: {DataSource}, 数据条数: {Count}", 
                request.DataSource, request.GpsData.Count);

            var processedCount = 0;
            var failedCount = 0;
            var errors = new List<string>();

            foreach (var gpsData in request.GpsData)
            {
                try
                {
                    // 验证GPS数据
                    if (!ValidateGpsData(gpsData, out var validationError))
                    {
                        errors.Add($"车辆 {gpsData.VehicleId}: {validationError}");
                        failedCount++;
                        continue;
                    }

                    // 创建车辆位置记录
                    var position = new VehiclePosition
                    {
                        VehicleId = int.Parse(gpsData.VehicleId), // 转换字符串到int
                        Timestamp = gpsData.GpsTime, // 使用GPS时间作为时间戳
                        Longitude = gpsData.Longitude,
                        Latitude = gpsData.Latitude,
                        Speed = gpsData.Speed,
                        Direction = gpsData.Direction,
                        Status = gpsData.Status,
                        NextStopId = gpsData.NextStopId
                        // 注意：VehiclePosition实体中没有LineId、DistanceToNextStop、DataSource、CreatedAt字段
                        // 这些信息可以通过Vehicle实体的LineId获取，或者需要扩展VehiclePosition实体
                    };

                    // 保存到TimescaleDB
                    await _positionRepository.AddAsync(position);

                    // 更新实时位置缓存
                    await _realtimeService.UpdateVehiclePositionAsync(position);

                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理GPS数据失败，车辆ID: {VehicleId}", gpsData.VehicleId);
                    errors.Add($"车辆 {gpsData.VehicleId}: {ex.Message}");
                    failedCount++;
                }
            }

            stopwatch.Stop();

            response.Success = failedCount == 0;
            response.Message = failedCount == 0 ? "GPS数据同步成功" : $"GPS数据同步完成，部分失败";
            response.ProcessedCount = processedCount;
            response.FailedCount = failedCount;
            response.Errors = errors;
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("GPS数据同步完成，成功: {Success}, 处理: {Processed}, 失败: {Failed}, 耗时: {Time}ms",
                processedCount, failedCount, processedCount, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "GPS数据同步异常，数据源: {DataSource}", request.DataSource);
            
            response.Success = false;
            response.Message = "GPS数据同步异常";
            response.Errors.Add(ex.Message);
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            
            return response;
        }
    }

    public async Task<SyncDataResponse> SyncLineDataAsync(LineSyncRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new SyncDataResponse();
        var processedCount = 0;
        var failedCount = 0;
        var errors = new List<string>();

        try
        {
            _logger.LogInformation("开始同步线路数据，数据源: {DataSource}, 数据条数: {Count}",
                request.DataSource, request.Lines.Count);

            foreach (var lineItem in request.Lines)
            {
                try
                {
                    // 根据外部ID查找现有线路
                    var existingLine = await _lineRepository.GetByExternalIdAsync(lineItem.ExternalLineId);

                    if (existingLine != null)
                    {
                        // 更新现有线路
                        existingLine.LineNumber = lineItem.LineNumber;
                        existingLine.LineName = lineItem.LineName;
                        existingLine.StartStopName = lineItem.StartStopName;
                        existingLine.EndStopName = lineItem.EndStopName;
                        existingLine.OperationTime = lineItem.OperationTime;
                        existingLine.Direction = lineItem.Direction;
                        existingLine.Status = lineItem.Status;
                        existingLine.UpdatedAt = DateTime.UtcNow;

                        await _lineRepository.UpdateAsync(existingLine);
                    }
                    else
                    {
                        // 创建新线路
                        var newLine = new BusLine
                        {
                            ExternalId = lineItem.ExternalLineId,
                            LineNumber = lineItem.LineNumber,
                            LineName = lineItem.LineName,
                            StartStopName = lineItem.StartStopName,
                            EndStopName = lineItem.EndStopName,
                            OperationTime = lineItem.OperationTime,
                            Direction = lineItem.Direction,
                            Status = lineItem.Status,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        await _lineRepository.AddAsync(newLine);
                        existingLine = newLine; // 用于后续处理站点关系
                    }

                    // 处理线路站点关系
                    if (lineItem.Stops.Any())
                    {
                        await SyncLineStopsAsync(existingLine.Id, lineItem.Stops);
                    }

                    processedCount++;
                }
                catch (Exception ex)
                {
                    failedCount++;
                    errors.Add($"线路 {lineItem.ExternalLineId}: {ex.Message}");
                    _logger.LogWarning(ex, "同步线路数据失败，外部ID: {ExternalId}", lineItem.ExternalLineId);
                }
            }

            stopwatch.Stop();

            response.Success = failedCount == 0;
            response.Message = failedCount == 0 ? "线路数据同步成功" : $"线路数据同步完成，但有 {failedCount} 条失败";
            response.ProcessedCount = processedCount;
            response.FailedCount = failedCount;
            response.Errors = errors;
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "线路数据同步异常，数据源: {DataSource}", request.DataSource);

            response.Success = false;
            response.Message = "线路数据同步异常";
            response.Errors.Add(ex.Message);
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
    }

    public async Task<SyncDataResponse> SyncStopDataAsync(StopSyncRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new SyncDataResponse();
        var processedCount = 0;
        var failedCount = 0;
        var errors = new List<string>();

        try
        {
            _logger.LogInformation("开始同步站点数据，数据源: {DataSource}, 数据条数: {Count}",
                request.DataSource, request.Stops.Count);

            foreach (var stopItem in request.Stops)
            {
                try
                {
                    // 根据外部ID查找现有站点
                    var existingStop = await _stopRepository.GetByExternalIdAsync(stopItem.ExternalStopId);

                    if (existingStop != null)
                    {
                        // 更新现有站点
                        existingStop.StopName = stopItem.StopName;
                        existingStop.StopCode = stopItem.StopCode;
                        existingStop.Longitude = stopItem.Longitude;
                        existingStop.Latitude = stopItem.Latitude;
                        existingStop.Address = stopItem.Address;
                        existingStop.District = stopItem.District;
                        existingStop.Status = stopItem.Status;
                        existingStop.UpdatedAt = DateTime.UtcNow;

                        await _stopRepository.UpdateAsync(existingStop);
                    }
                    else
                    {
                        // 创建新站点
                        var newStop = new BusStop
                        {
                            ExternalId = stopItem.ExternalStopId,
                            StopName = stopItem.StopName,
                            StopCode = stopItem.StopCode,
                            Longitude = stopItem.Longitude,
                            Latitude = stopItem.Latitude,
                            Address = stopItem.Address,
                            District = stopItem.District,
                            Status = stopItem.Status,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        await _stopRepository.AddAsync(newStop);
                    }

                    processedCount++;
                }
                catch (Exception ex)
                {
                    failedCount++;
                    errors.Add($"站点 {stopItem.ExternalStopId}: {ex.Message}");
                    _logger.LogWarning(ex, "同步站点数据失败，外部ID: {ExternalId}", stopItem.ExternalStopId);
                }
            }

            stopwatch.Stop();

            response.Success = failedCount == 0;
            response.Message = failedCount == 0 ? "站点数据同步成功" : $"站点数据同步完成，但有 {failedCount} 条失败";
            response.ProcessedCount = processedCount;
            response.FailedCount = failedCount;
            response.Errors = errors;
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "站点数据同步异常，数据源: {DataSource}", request.DataSource);

            response.Success = false;
            response.Message = "站点数据同步异常";
            response.Errors.Add(ex.Message);
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
    }

    public async Task<SyncDataResponse> SyncVehicleDataAsync(VehicleSyncRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new SyncDataResponse();
        var processedCount = 0;
        var failedCount = 0;
        var errors = new List<string>();

        try
        {
            _logger.LogInformation("开始同步车辆数据，数据源: {DataSource}, 数据条数: {Count}",
                request.DataSource, request.Vehicles.Count);

            foreach (var vehicleItem in request.Vehicles)
            {
                try
                {
                    // 根据外部ID查找现有车辆
                    var existingVehicle = await _vehicleRepository.GetByExternalIdAsync(vehicleItem.ExternalVehicleId);

                    // 根据外部线路ID查找线路
                    var line = await _lineRepository.GetByExternalIdAsync(vehicleItem.ExternalLineId);
                    if (line == null)
                    {
                        errors.Add($"车辆 {vehicleItem.ExternalVehicleId}: 关联的线路不存在 ({vehicleItem.ExternalLineId})");
                        failedCount++;
                        continue;
                    }

                    if (existingVehicle != null)
                    {
                        // 更新现有车辆
                        existingVehicle.PlateNumber = vehicleItem.PlateNumber;
                        existingVehicle.LineId = line.Id;
                        existingVehicle.VehicleTypeDescription = vehicleItem.VehicleType;
                        existingVehicle.Capacity = vehicleItem.Capacity;
                        existingVehicle.Status = vehicleItem.Status;
                        existingVehicle.UpdatedAt = DateTime.UtcNow;

                        await _vehicleRepository.UpdateAsync(existingVehicle);
                    }
                    else
                    {
                        // 创建新车辆
                        var newVehicle = new Vehicle
                        {
                            ExternalId = vehicleItem.ExternalVehicleId,
                            PlateNumber = vehicleItem.PlateNumber,
                            LineId = line.Id,
                            VehicleTypeDescription = vehicleItem.VehicleType,
                            Capacity = vehicleItem.Capacity,
                            Status = vehicleItem.Status,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        await _vehicleRepository.AddAsync(newVehicle);
                    }

                    processedCount++;
                }
                catch (Exception ex)
                {
                    failedCount++;
                    errors.Add($"车辆 {vehicleItem.ExternalVehicleId}: {ex.Message}");
                    _logger.LogWarning(ex, "同步车辆数据失败，外部ID: {ExternalId}", vehicleItem.ExternalVehicleId);
                }
            }

            stopwatch.Stop();

            response.Success = failedCount == 0;
            response.Message = failedCount == 0 ? "车辆数据同步成功" : $"车辆数据同步完成，但有 {failedCount} 条失败";
            response.ProcessedCount = processedCount;
            response.FailedCount = failedCount;
            response.Errors = errors;
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "车辆数据同步异常，数据源: {DataSource}", request.DataSource);

            response.Success = false;
            response.Message = "车辆数据同步异常";
            response.Errors.Add(ex.Message);
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
    }

    public async Task<SyncDataResponse> SyncLineStopsAsync(LineStopsSyncRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var response = new SyncDataResponse();

        try
        {
            _logger.LogInformation("开始同步线路站点关系，数据源: {DataSource}, 外部线路ID: {ExternalLineId}, 站点数量: {Count}",
                request.DataSource, request.ExternalLineId, request.Stops.Count);

            // 根据外部线路ID查找线路
            var line = await _lineRepository.GetByExternalIdAsync(request.ExternalLineId);
            if (line == null)
            {
                response.Success = false;
                response.Message = $"线路不存在: {request.ExternalLineId}";
                response.Errors.Add($"外部线路ID {request.ExternalLineId} 对应的线路不存在");
                response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                return response;
            }

            // 同步线路站点关系
            await SyncLineStopsAsync(line.Id, request.Stops);

            stopwatch.Stop();

            response.Success = true;
            response.Message = "线路站点关系同步成功";
            response.ProcessedCount = request.Stops.Count;
            response.FailedCount = 0;
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("线路站点关系同步完成，线路: {ExternalLineId}, 处理: {ProcessedCount}, 耗时: {ElapsedMs}ms",
                request.ExternalLineId, request.Stops.Count, stopwatch.ElapsedMilliseconds);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "线路站点关系同步异常，数据源: {DataSource}, 线路: {ExternalLineId}",
                request.DataSource, request.ExternalLineId);

            response.Success = false;
            response.Message = "线路站点关系同步异常";
            response.Errors.Add(ex.Message);
            response.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;

            return response;
        }
    }

    #region 私有方法

    private static bool ValidateGpsData(GpsDataItem gpsData, out string error)
    {
        error = string.Empty;

        if (string.IsNullOrWhiteSpace(gpsData.VehicleId))
        {
            error = "车辆ID不能为空";
            return false;
        }

        if (gpsData.Longitude < -180 || gpsData.Longitude > 180)
        {
            error = "经度超出有效范围";
            return false;
        }

        if (gpsData.Latitude < -90 || gpsData.Latitude > 90)
        {
            error = "纬度超出有效范围";
            return false;
        }

        if (gpsData.Speed < 0 || gpsData.Speed > 200)
        {
            error = "速度超出有效范围";
            return false;
        }

        if (gpsData.GpsTime > DateTime.UtcNow.AddMinutes(5))
        {
            error = "GPS时间不能超过当前时间5分钟";
            return false;
        }

        if (gpsData.GpsTime < DateTime.UtcNow.AddHours(-24))
        {
            error = "GPS时间不能早于24小时前";
            return false;
        }

        if (gpsData.Direction < 0 || gpsData.Direction > 360)
        {
            error = "方向角超出有效范围";
            return false;
        }

        return true;
    }

    /// <summary>
    /// 同步线路站点关系
    /// </summary>
    private async Task SyncLineStopsAsync(int lineId, List<LineStopSyncItem> stopItems)
    {
        try
        {
            // 删除现有的线路站点关系
            await _lineStopRepository.DeleteByLineIdAsync(lineId);

            var lineStops = new List<LineStop>();

            foreach (var stopItem in stopItems)
            {
                // 根据外部站点ID查找站点
                var stop = await _stopRepository.GetByExternalIdAsync(stopItem.ExternalStopId);
                if (stop == null)
                {
                    _logger.LogWarning("同步线路站点关系时，站点不存在: {ExternalStopId}", stopItem.ExternalStopId);
                    continue;
                }

                var lineStop = new LineStop
                {
                    LineId = lineId,
                    StopId = stop.Id,
                    SequenceNumber = stopItem.SequenceNumber,
                    DistanceFromStart = stopItem.DistanceFromStart,
                    EstimatedTime = stopItem.EstimatedTime,
                    IsKeyStop = stopItem.IsKeyStop,
                    Status = stopItem.Status,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                lineStops.Add(lineStop);
            }

            if (lineStops.Any())
            {
                await _lineStopRepository.AddRangeAsync(lineStops);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步线路站点关系失败，线路ID: {LineId}", lineId);
            throw;
        }
    }

    #endregion
}
