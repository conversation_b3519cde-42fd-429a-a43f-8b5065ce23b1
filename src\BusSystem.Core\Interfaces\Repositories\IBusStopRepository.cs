using BusSystem.Core.Entities;
using NetTopologySuite.Geometries;

namespace BusSystem.Core.Interfaces.Repositories;

/// <summary>
/// 公交站点Repository接口
/// </summary>
public interface IBusStopRepository : IRepository<BusStop>
{
    /// <summary>
    /// 根据站点编码获取站点
    /// </summary>
    Task<BusStop?> GetByStopCodeAsync(string stopCode);
    
    /// <summary>
    /// 根据站点名称搜索站点
    /// </summary>
    Task<IEnumerable<BusStop>> SearchByNameAsync(string name);
    
    /// <summary>
    /// 获取附近的站点（使用PostGIS）
    /// </summary>
    Task<IEnumerable<BusStop>> GetNearbyStopsAsync(double longitude, double latitude, double radiusMeters);
    
    /// <summary>
    /// 获取站点及其线路信息
    /// </summary>
    Task<BusStop?> GetWithLinesAsync(int stopId);
    
    /// <summary>
    /// 根据区域获取站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetByDistrictAsync(string district);

    /// <summary>
    /// 根据站点名称获取所有同名站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetStopsByNameAsync(string stopName);

    /// <summary>
    /// 获取指定站点附近的同名站点（用于站点切换）
    /// </summary>
    Task<IEnumerable<BusStop>> GetNearbyStopsWithSameNameAsync(int stopId, double radiusMeters = 1000);
    
    /// <summary>
    /// 获取指定类型的站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetByTypeAsync(int stopType);
    
    /// <summary>
    /// 获取活跃的站点
    /// </summary>
    Task<IEnumerable<BusStop>> GetActiveAsync();

    /// <summary>
    /// 根据外部系统ID获取站点（用于数据同步）
    /// </summary>
    /// <param name="externalId">外部系统站点ID</param>
    /// <returns>站点信息</returns>
    Task<BusStop?> GetByExternalIdAsync(string externalId);
}
