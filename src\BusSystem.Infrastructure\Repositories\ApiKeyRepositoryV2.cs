using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Entities;
using BusSystem.Infrastructure.Data;
using static BusSystem.Core.Interfaces.Services.IApiKeyService;
using ApiPermissionEntity = BusSystem.Core.Entities.ApiPermission;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// API密钥数据访问实现（EF Core版本）
/// 推荐使用此版本替代原生SQL版本
/// </summary>
public class ApiKeyRepositoryV2 : IApiKeyRepository
{
    private readonly BusSystemDbContext _context;

    public ApiKeyRepositoryV2(BusSystemDbContext context)
    {
        _context = context;
    }

    public async Task<ApiKeyInfo?> GetByHashAsync(string apiKeyHash)
    {
        var apiKey = await _context.ApiKeys
            .Include(ak => ak.ApiKeyPermissions)
                .ThenInclude(akp => akp.Permission)
            .FirstOrDefaultAsync(ak => ak.ApiKeyHash == apiKeyHash && ak.IsActive);

        return apiKey == null ? null : MapToApiKeyInfo(apiKey);
    }

    public async Task<ApiKeyInfo?> GetByIdAsync(int apiKeyId)
    {
        var apiKey = await _context.ApiKeys
            .Include(ak => ak.ApiKeyPermissions)
                .ThenInclude(akp => akp.Permission)
            .FirstOrDefaultAsync(ak => ak.Id == apiKeyId);

        return apiKey == null ? null : MapToApiKeyInfo(apiKey);
    }

    public async Task<IEnumerable<Core.Entities.ApiPermission>> GetPermissionsAsync(int apiKeyId)
    {
        var permissions = await _context.ApiKeyPermissions
            .Where(akp => akp.ApiKeyId == apiKeyId)
            .Include(akp => akp.Permission)
            .Where(akp => akp.Permission.IsActive)
            .Select(akp => new Core.Entities.ApiPermission
            {
                Id = akp.Permission.Id,
                PermissionName = akp.Permission.PermissionName,
                EndpointPattern = akp.Permission.EndpointPattern,
                HttpMethods = akp.Permission.HttpMethods,
                Description = akp.Permission.Description ?? string.Empty,
                IsActive = akp.Permission.IsActive
            })
            .ToListAsync();

        return permissions;
    }

    public async Task<int> GetHourlyRequestCountAsync(int apiKeyId, DateTime timeWindow)
    {
        // 对于复杂的统计查询，仍然使用原生SQL以获得更好的性能
        var sql = @"
            SELECT COALESCE(request_count, 0) as count
            FROM api_rate_limits 
            WHERE api_key_id = {0} AND time_window = {1}";

        var result = await _context.Database
            .SqlQueryRaw<int>(sql, apiKeyId, timeWindow)
            .FirstOrDefaultAsync();

        return result;
    }

    public async Task IncrementRequestCountAsync(int apiKeyId)
    {
        var currentHour = DateTime.UtcNow.Date.AddHours(DateTime.UtcNow.Hour);
        
        // 使用原生SQL进行UPSERT操作，因为EF Core的UPSERT支持有限
        var sql = @"
            INSERT INTO api_rate_limits (api_key_id, time_window, request_count, created_at, updated_at)
            VALUES ({0}, {1}, 1, {2}, {2})
            ON CONFLICT (api_key_id, time_window) 
            DO UPDATE SET 
                request_count = api_rate_limits.request_count + 1,
                updated_at = {2}";

        await _context.Database.ExecuteSqlRawAsync(sql, apiKeyId, currentHour, DateTime.UtcNow);
    }

    public async Task LogAccessAsync(ApiAccessLog accessLog)
    {
        // 使用原生SQL插入日志，避免EF Core跟踪开销
        var sql = @"
            INSERT INTO api_access_audit 
            (api_key_id, api_key_hash, endpoint, http_method, client_ip, user_agent, 
             request_size, response_size, response_time_ms, status_code, error_message, created_at)
            VALUES 
            ({0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}, {10}, {11})";

        await _context.Database.ExecuteSqlRawAsync(sql,
            accessLog.ApiKeyId,
            accessLog.ApiKeyHash,
            accessLog.Endpoint,
            accessLog.HttpMethod,
            accessLog.ClientIp,
            accessLog.UserAgent,
            accessLog.RequestSize,
            accessLog.ResponseSize,
            accessLog.ResponseTimeMs,
            accessLog.StatusCode,
            accessLog.ErrorMessage,
            accessLog.CreatedAt);
    }

    public async Task UpdateLastUsedAsync(int apiKeyId, DateTime lastUsedAt)
    {
        var apiKey = await _context.ApiKeys.FindAsync(apiKeyId);
        if (apiKey != null)
        {
            apiKey.LastUsedAt = lastUsedAt;
            apiKey.UsageCount++;
            apiKey.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<ApiKeyInfo> CreateAsync(ApiKeyInfo apiKeyInfo, string apiKeyHash)
    {
        var apiKey = new ApiKey
        {
            KeyName = apiKeyInfo.KeyName,
            ApiKeyHash = apiKeyHash,
            KeyType = apiKeyInfo.KeyType,
            Description = apiKeyInfo.Description,
            OwnerName = apiKeyInfo.OwnerName,
            IsActive = apiKeyInfo.IsActive,
            RateLimitPerHour = apiKeyInfo.RateLimitPerHour,
            RateLimitPerDay = apiKeyInfo.RateLimitPerDay,
            AllowedIps = apiKeyInfo.AllowedIps,
            ExpiresAt = apiKeyInfo.ExpiresAt,
            CreatedAt = apiKeyInfo.CreatedAt,
            UpdatedAt = DateTime.UtcNow
        };

        _context.ApiKeys.Add(apiKey);
        await _context.SaveChangesAsync();

        apiKeyInfo.Id = apiKey.Id;
        return apiKeyInfo;
    }

    public async Task UpdateAsync(int apiKeyId, UpdateApiKeyRequest request)
    {
        var apiKey = await _context.ApiKeys.FindAsync(apiKeyId);
        if (apiKey == null) return;

        if (!string.IsNullOrWhiteSpace(request.KeyName))
            apiKey.KeyName = request.KeyName;

        if (!string.IsNullOrWhiteSpace(request.Description))
            apiKey.Description = request.Description;

        if (request.RateLimitPerHour.HasValue)
            apiKey.RateLimitPerHour = request.RateLimitPerHour.Value;

        if (request.IsActive.HasValue)
            apiKey.IsActive = request.IsActive.Value;

        apiKey.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
    }

    public async Task SetPermissionsAsync(int apiKeyId, IEnumerable<string> permissionNames)
    {
        // 删除现有权限
        var existingPermissions = await _context.ApiKeyPermissions
            .Where(akp => akp.ApiKeyId == apiKeyId)
            .ToListAsync();
        
        _context.ApiKeyPermissions.RemoveRange(existingPermissions);

        // 添加新权限
        if (permissionNames.Any())
        {
            var permissions = await _context.ApiPermissions
                .Where(p => permissionNames.Contains(p.PermissionName) && p.IsActive)
                .ToListAsync();

            var newApiKeyPermissions = permissions.Select(p => new ApiKeyPermission
            {
                ApiKeyId = apiKeyId,
                PermissionId = p.Id,
                GrantedAt = DateTime.UtcNow
            });

            _context.ApiKeyPermissions.AddRange(newApiKeyPermissions);
        }

        await _context.SaveChangesAsync();
    }

    public async Task DisableAsync(int apiKeyId)
    {
        var apiKey = await _context.ApiKeys.FindAsync(apiKeyId);
        if (apiKey != null)
        {
            apiKey.IsActive = false;
            apiKey.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<IEnumerable<ApiKeyInfo>> GetAllAsync(string? keyType = null, bool? isActive = null)
    {
        var query = _context.ApiKeys
            .Include(ak => ak.ApiKeyPermissions)
                .ThenInclude(akp => akp.Permission)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(keyType))
            query = query.Where(ak => ak.KeyType == keyType);

        if (isActive.HasValue)
            query = query.Where(ak => ak.IsActive == isActive.Value);

        var apiKeys = await query
            .OrderByDescending(ak => ak.CreatedAt)
            .ToListAsync();

        return apiKeys.Select(MapToApiKeyInfo);
    }

    public async Task<IEnumerable<Core.Entities.ApiPermission>> GetAllPermissionsAsync()
    {
        var permissions = await _context.ApiPermissions
            .Where(p => p.IsActive)
            .OrderBy(p => p.PermissionName)
            .Select(p => new Core.Entities.ApiPermission
            {
                Id = p.Id,
                PermissionName = p.PermissionName,
                EndpointPattern = p.EndpointPattern,
                HttpMethods = p.HttpMethods,
                Description = p.Description ?? string.Empty,
                IsActive = p.IsActive
            })
            .ToListAsync();

        return permissions;
    }

    public async Task<IEnumerable<int>> GetPermissionIdsByNamesAsync(IEnumerable<string> permissionNames)
    {
        if (!permissionNames.Any()) return Enumerable.Empty<int>();

        var ids = await _context.ApiPermissions
            .Where(p => permissionNames.Contains(p.PermissionName) && p.IsActive)
            .Select(p => p.Id)
            .ToListAsync();

        return ids;
    }

    public async Task<int> CleanupExpiredRateLimitsAsync(DateTime cutoffTime)
    {
        // 对于批量删除操作，使用原生SQL更高效
        var sql = "DELETE FROM api_rate_limits WHERE time_window < {0}";
        return await _context.Database.ExecuteSqlRawAsync(sql, cutoffTime);
    }

    public async Task<int> CleanupExpiredAccessLogsAsync(DateTime cutoffTime)
    {
        // 对于批量删除操作，使用原生SQL更高效
        var sql = "DELETE FROM api_access_audit WHERE created_at < {0}";
        return await _context.Database.ExecuteSqlRawAsync(sql, cutoffTime);
    }

    /// <summary>
    /// 将实体映射为DTO
    /// </summary>
    private static ApiKeyInfo MapToApiKeyInfo(ApiKey apiKey)
    {
        return new ApiKeyInfo
        {
            Id = apiKey.Id,
            KeyName = apiKey.KeyName,
            KeyType = apiKey.KeyType,
            Description = apiKey.Description ?? string.Empty,
            OwnerName = apiKey.OwnerName ?? string.Empty,
            IsActive = apiKey.IsActive,
            RateLimitPerHour = apiKey.RateLimitPerHour,
            RateLimitPerDay = apiKey.RateLimitPerDay,
            AllowedIps = apiKey.AllowedIps,
            ExpiresAt = apiKey.ExpiresAt,
            LastUsedAt = apiKey.LastUsedAt,
            UsageCount = apiKey.UsageCount,
            CreatedAt = apiKey.CreatedAt,
            Permissions = apiKey.ApiKeyPermissions.Select(akp => new Core.Interfaces.Services.ApiPermission
            {
                Id = akp.Permission.Id,
                PermissionName = akp.Permission.PermissionName,
                EndpointPattern = akp.Permission.EndpointPattern,
                HttpMethods = akp.Permission.HttpMethods,
                Description = akp.Permission.Description ?? string.Empty,
                IsActive = akp.Permission.IsActive
            }).ToList()
        };
    }
}
