namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// API密钥服务接口
/// </summary>
public interface IApiKeyService
{
    /// <summary>
    /// 验证API密钥
    /// </summary>
    /// <param name="apiKey">API密钥</param>
    /// <returns>API密钥信息，如果无效则返回null</returns>
    Task<ApiKeyInfo?> ValidateApiKeyAsync(string apiKey);

    /// <summary>
    /// 检查API密钥是否有指定权限
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <param name="endpoint">端点路径</param>
    /// <param name="httpMethod">HTTP方法</param>
    /// <returns>是否有权限</returns>
    Task<bool> HasPermissionAsync(int apiKeyId, string endpoint, string httpMethod);

    /// <summary>
    /// 检查速率限制
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <returns>速率限制检查结果</returns>
    Task<RateLimitResult> CheckRateLimitAsync(int apiKeyId);

    /// <summary>
    /// 创建新的API密钥
    /// </summary>
    /// <param name="request">创建请求</param>
    /// <returns>创建的API密钥信息和原始密钥（仅此一次）</returns>
    Task<CreateApiKeyResult> CreateApiKeyAsync(CreateApiKeyRequest request);

    /// <summary>
    /// 更新API密钥
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <param name="request">更新请求</param>
    Task UpdateApiKeyAsync(int apiKeyId, UpdateApiKeyRequest request);

    /// <summary>
    /// 禁用API密钥
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    Task DisableApiKeyAsync(int apiKeyId);

    /// <summary>
    /// 获取API密钥列表
    /// </summary>
    /// <param name="keyType">密钥类型过滤</param>
    /// <param name="isActive">激活状态过滤</param>
    /// <returns>API密钥列表</returns>
    Task<IEnumerable<ApiKeyInfo>> GetApiKeysAsync(string? keyType = null, bool? isActive = null);

    /// <summary>
    /// 获取API密钥的权限列表
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<ApiPermission>> GetApiKeyPermissionsAsync(int apiKeyId);

    /// <summary>
    /// 获取所有可用权限
    /// </summary>
    /// <returns>权限列表</returns>
    Task<IEnumerable<ApiPermission>> GetAllPermissionsAsync();

    /// <summary>
    /// 记录API访问日志
    /// </summary>
    /// <param name="accessLog">访问日志</param>
    Task LogApiAccessAsync(ApiAccessLog accessLog);

    /// <summary>
    /// 更新API密钥使用统计
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    Task UpdateApiKeyUsageAsync(int apiKeyId);
}

/// <summary>
/// API密钥信息
/// </summary>
public class ApiKeyInfo
{
    public int Id { get; set; }
    public string KeyName { get; set; } = string.Empty;
    public string KeyType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int RateLimitPerHour { get; set; }
    public int RateLimitPerDay { get; set; }
    public string[]? AllowedIps { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public long UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<ApiPermission> Permissions { get; set; } = new();
}

/// <summary>
/// API权限信息
/// </summary>
public class ApiPermission
{
    public int Id { get; set; }
    public string PermissionName { get; set; } = string.Empty;
    public string EndpointPattern { get; set; } = string.Empty;
    public string HttpMethods { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

/// <summary>
/// 速率限制检查结果
/// </summary>
public class RateLimitResult
{
    public bool IsAllowed { get; set; }
    public int CurrentCount { get; set; }
    public int Limit { get; set; }
    public TimeSpan ResetTime { get; set; }
    public string? Message { get; set; }
}

/// <summary>
/// 创建API密钥请求
/// </summary>
public class CreateApiKeyRequest
{
    public string KeyName { get; set; } = string.Empty;
    public string KeyType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public int RateLimitPerHour { get; set; } = 1000;
    public int RateLimitPerDay { get; set; } = 10000;
    public string[]? AllowedIps { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public List<string> PermissionNames { get; set; } = new();
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// 更新API密钥请求
/// </summary>
public class UpdateApiKeyRequest
{
    public string? KeyName { get; set; }
    public string? Description { get; set; }
    public string? OwnerName { get; set; }
    public string? OwnerContact { get; set; }
    public int? RateLimitPerHour { get; set; }
    public int? RateLimitPerDay { get; set; }
    public string[]? AllowedIps { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool? IsActive { get; set; }
    public List<string>? PermissionNames { get; set; }
}

/// <summary>
/// 创建API密钥结果
/// </summary>
public class CreateApiKeyResult
{
    public ApiKeyInfo ApiKeyInfo { get; set; } = new();
    public string ApiKey { get; set; } = string.Empty;
    public string SecurityNotice { get; set; } = string.Empty;
}

/// <summary>
/// API访问日志记录
/// </summary>
public class ApiAccessLog
{
    public int? ApiKeyId { get; set; }
    public string? ApiKeyHash { get; set; }
    public string Endpoint { get; set; } = string.Empty;
    public string HttpMethod { get; set; } = string.Empty;
    public string ClientIp { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;
    public int RequestSize { get; set; }
    public int ResponseSize { get; set; }
    public int ResponseTimeMs { get; set; }
    public int StatusCode { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CreatedAt { get; set; }
}
