# 文档维护指南

## 📋 核心设计文档清单

### 🎯 必须保持同步的核心文档

#### 1. **业务需求.md** - 需求设计
- **作用**: 项目的业务需求和功能规范
- **更新时机**: 业务需求变更时
- **维护要点**: 保持需求的完整性和准确性

#### 2. **架构设计.md** - 架构设计
- **作用**: 系统整体架构和技术栈选型
- **更新时机**: 架构调整、技术栈变更时
- **维护要点**: 
  - 技术栈信息必须与实际项目一致
  - 架构图要反映真实的系统结构
  - ✅ 已更新：.NET 8技术栈

#### 3. **核心服务设计.md** - 核心服务设计
- **作用**: 各个服务的详细设计和实现方案
- **更新时机**: 新增服务、服务接口变更时
- **维护要点**: 
  - 服务接口定义要与实际代码一致
  - 新增服务必须及时添加到文档
  - ✅ 已更新：添加AggregationService设计

#### 4. **接口设计.md** - 接口设计
- **作用**: API接口规范和示例
- **更新时机**: 控制器变更、接口路径调整时
- **维护要点**: 
  - API路径必须与实际控制器一致
  - 接口参数和响应格式要准确
  - ✅ 已更新：实际控制器路径结构

#### 5. **数据库设计.md** - 数据库设计
- **作用**: 数据库表结构和关系设计
- **更新时机**: 数据库结构变更时
- **维护要点**: 表结构、字段定义、关系设计的准确性

#### 6. **H5首页API接口设计.md** - 前端专用接口
- **作用**: H5前端的具体接口调用方案
- **更新时机**: 前端接口变更时
- **维护要点**: 
  - 接口路径要与后端一致
  - 调用示例要可用
  - ✅ 已更新：/api/aggregation路径

---

## 🔄 文档同步更新流程

### 代码变更时的文档检查清单

#### ✅ 控制器变更检查
- [ ] 新增控制器 → 更新`接口设计.md`和`核心服务设计.md`
- [ ] 控制器重命名 → 更新`接口设计.md`、`H5首页API接口设计.md`
- [ ] 路由路径变更 → 更新所有相关接口文档

#### ✅ 服务变更检查
- [ ] 新增服务 → 更新`核心服务设计.md`
- [ ] 服务接口变更 → 更新服务设计和接口文档
- [ ] 服务重命名 → 更新所有相关文档

#### ✅ 架构变更检查
- [ ] 技术栈变更 → 更新`架构设计.md`
- [ ] 数据库变更 → 更新`数据库设计.md`
- [ ] 部署方式变更 → 更新架构文档

#### ✅ 业务变更检查
- [ ] 新增功能 → 更新`业务需求.md`
- [ ] 功能调整 → 更新需求和接口文档
- [ ] 业务流程变更 → 更新相关设计文档

---

## 📝 文档更新最佳实践

### 1. 及时更新原则
- **代码先行，文档跟进**: 代码变更后立即更新相关文档
- **批量更新**: 相关变更一次性更新所有受影响的文档
- **版本同步**: 确保文档版本与代码版本保持一致

### 2. 准确性原则
- **实际验证**: 文档中的示例要经过实际测试
- **路径一致**: API路径必须与实际控制器路径完全一致
- **参数准确**: 接口参数和响应格式要与实际代码匹配

### 3. 完整性原则
- **覆盖全面**: 所有核心功能都要有对应的文档
- **细节充分**: 重要的设计决策和实现细节要记录
- **示例完整**: 提供完整可用的调用示例

### 4. 可维护性原则
- **结构清晰**: 文档结构要便于查找和更新
- **标记更新**: 重要更新要有明确的标记和说明
- **历史记录**: 在NOTES.md中记录重要的文档更新

---

## 🚫 避免的文档类型

### 过程性报告文档（建议删除）
- ❌ `*完成报告.md` - 开发过程记录，价值有限
- ❌ `*修复报告.md` - 问题修复记录，临时性文档
- ❌ `*测试报告.md` - 测试结果记录，时效性强
- ❌ `*优化报告.md` - 优化过程记录，参考价值低

### 保留的过程文档（有长期价值）
- ✅ `开发进度记录.md` - 项目进度跟踪
- ✅ `NOTES.md` - 项目备忘录和重要信息
- ✅ `开发启动指南.md` - 新会话启动指南

---

## 📊 文档质量检查

### 定期检查项目
1. **月度检查**: 核心设计文档与实际代码的一致性
2. **版本检查**: 重大版本发布前的文档完整性检查
3. **新人测试**: 新开发者能否通过文档快速理解项目

### 检查工具和方法
- **代码对比**: 定期对比文档中的接口定义与实际代码
- **示例验证**: 定期验证文档中的API调用示例
- **路径检查**: 确保所有API路径与实际控制器一致

---

## 🎯 当前文档状态

### ✅ 已同步更新（2025-08-25）
- ✅ **接口设计.md**: 更新为实际控制器路径结构
- ✅ **核心服务设计.md**: 添加AggregationService设计
- ✅ **架构设计.md**: 更新技术栈为.NET 8
- ✅ **H5首页API接口设计.md**: 更新API路径为/api/aggregation
- ✅ **NOTES.md**: 记录文档同步更新

### 📋 下次检查重点
- [ ] 前端开发开始后，验证H5接口文档的准确性
- [ ] 数据同步服务开发时，更新相关架构文档
- [ ] 部署上线前，检查所有文档的完整性

---

**维护原则**: 核心设计文档是项目的重要资产，必须与实际代码保持一致，过程性报告文档可以适当清理，保持文档目录的整洁和实用性。
