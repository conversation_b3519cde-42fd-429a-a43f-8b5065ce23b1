using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// 公交站点Repository实现
/// </summary>
public class BusStopRepository : Repository<BusStop>, IBusStopRepository
{
    public BusStopRepository(BusSystemDbContext context) : base(context)
    {
    }

    public async Task<BusStop?> GetByStopCodeAsync(string stopCode)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.StopCode == stopCode);
    }

    public async Task<IEnumerable<BusStop>> SearchByNameAsync(string name)
    {
        return await _dbSet
            .Where(x => x.StopName.Contains(name))
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetNearbyStopsAsync(double longitude, double latitude, double radiusMeters)
    {
        // 创建查询点
        var geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
        var queryPoint = geometryFactory.CreatePoint(new Coordinate(longitude, latitude));

        return await _dbSet
            .Where(x => x.Location.Distance(queryPoint) <= radiusMeters)
            .OrderBy(x => x.Location.Distance(queryPoint))
            .ToListAsync();
    }

    public async Task<BusStop?> GetWithLinesAsync(int stopId)
    {
        return await _dbSet
            .Include(x => x.LineStops)
                .ThenInclude(ls => ls.Line)
            .FirstOrDefaultAsync(x => x.Id == stopId);
    }

    public async Task<IEnumerable<BusStop>> GetByDistrictAsync(string district)
    {
        return await _dbSet
            .Where(x => x.District == district)
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetByTypeAsync(int stopType)
    {
        return await _dbSet
            .Where(x => x.StopType == stopType)
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetActiveAsync()
    {
        return await _dbSet
            .Where(x => x.Status == 1)
            .OrderBy(x => x.StopName)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetStopsByNameAsync(string stopName)
    {
        return await _dbSet
            .Where(x => x.StopName == stopName && x.Status == 1)
            .OrderBy(x => x.Id)
            .ToListAsync();
    }

    public async Task<IEnumerable<BusStop>> GetNearbyStopsWithSameNameAsync(int stopId, double radiusMeters = 1000)
    {
        var targetStop = await _dbSet.FirstOrDefaultAsync(x => x.Id == stopId);
        if (targetStop == null)
        {
            return new List<BusStop>();
        }

        // 创建查询点
        var geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
        var queryPoint = geometryFactory.CreatePoint(new Coordinate(targetStop.Longitude, targetStop.Latitude));

        return await _dbSet
            .Where(x => x.StopName == targetStop.StopName &&
                       x.Id != stopId &&
                       x.Status == 1 &&
                       x.Location.Distance(queryPoint) <= radiusMeters)
            .OrderBy(x => x.Location.Distance(queryPoint))
            .ToListAsync();
    }

    public async Task<BusStop?> GetByExternalIdAsync(string externalId)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.ExternalId == externalId);
    }
}
