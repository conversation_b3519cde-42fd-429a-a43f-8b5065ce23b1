using BusSystem.Core.Entities;

namespace BusSystem.Core.Interfaces.Repositories;

/// <summary>
/// 线路站点关系Repository接口
/// </summary>
public interface ILineStopRepository : IRepository<LineStop>
{
    /// <summary>
    /// 根据线路ID获取所有站点关系
    /// </summary>
    /// <param name="lineId">线路ID</param>
    /// <returns>线路站点关系列表</returns>
    Task<IEnumerable<LineStop>> GetByLineIdAsync(int lineId);
    
    /// <summary>
    /// 根据站点ID获取所有线路关系
    /// </summary>
    /// <param name="stopId">站点ID</param>
    /// <returns>线路站点关系列表</returns>
    Task<IEnumerable<LineStop>> GetByStopIdAsync(int stopId);
    
    /// <summary>
    /// 根据线路ID和站点ID获取关系
    /// </summary>
    /// <param name="lineId">线路ID</param>
    /// <param name="stopId">站点ID</param>
    /// <returns>线路站点关系</returns>
    Task<LineStop?> GetByLineAndStopAsync(int lineId, int stopId);
    
    /// <summary>
    /// 删除线路的所有站点关系
    /// </summary>
    /// <param name="lineId">线路ID</param>
    /// <returns>删除的记录数</returns>
    Task<int> DeleteByLineIdAsync(int lineId);
    
    /// <summary>
    /// 批量添加线路站点关系
    /// </summary>
    /// <param name="lineStops">线路站点关系列表</param>
    /// <returns>添加的记录数</returns>
    Task<int> AddRangeAsync(IEnumerable<LineStop> lineStops);
}
