-- =============================================
-- 业务数据表创建脚本
-- 创建时间: 2025-08-22
-- 说明: 创建到站预测历史、用户数据、搜索记录等业务表
-- =============================================

-- 1. 到站预测历史表
CREATE TABLE IF NOT EXISTS arrival_predictions_history (
    id BIGSERIAL PRIMARY KEY,
    vehicle_id VARCHAR(50) NOT NULL,
    line_id INTEGER NOT NULL,
    stop_id INTEGER NOT NULL,
    predicted_arrival_time TIMESTAMPTZ NOT NULL,
    actual_arrival_time TIMESTAMPTZ,
    prediction_accuracy_seconds INTEGER, -- 预测准确度（秒）
    prediction_method VARCHAR(50) DEFAULT 'gps_based', -- 预测方法
    prediction_confidence DECIMAL(5,2), -- 预测置信度 (0-100)
    distance_to_stop DECIMAL(10,2), -- 预测时距离站点的距离（米）
    vehicle_speed DECIMAL(5,2), -- 预测时车辆速度（km/h）
    traffic_condition VARCHAR(20) DEFAULT 'normal', -- 交通状况
    weather_condition VARCHAR(20), -- 天气状况
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_arrival_predictions_history_vehicle_time 
ON arrival_predictions_history (vehicle_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_arrival_predictions_history_line_stop 
ON arrival_predictions_history (line_id, stop_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_arrival_predictions_history_accuracy 
ON arrival_predictions_history (prediction_accuracy_seconds) 
WHERE prediction_accuracy_seconds IS NOT NULL;

-- 添加外键约束
ALTER TABLE arrival_predictions_history 
ADD CONSTRAINT fk_arrival_predictions_history_line 
FOREIGN KEY (line_id) REFERENCES bus_lines(id) ON DELETE CASCADE;

ALTER TABLE arrival_predictions_history 
ADD CONSTRAINT fk_arrival_predictions_history_stop 
FOREIGN KEY (stop_id) REFERENCES bus_stops(id) ON DELETE CASCADE;

-- 2. 用户位置记录表
CREATE TABLE IF NOT EXISTS user_locations (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(100), -- 用户标识（可以是设备ID、会话ID等）
    longitude DECIMAL(10,7) NOT NULL,
    latitude DECIMAL(10,7) NOT NULL,
    location GEOMETRY(POINT, 4326), -- PostGIS点类型
    accuracy DECIMAL(8,2), -- GPS精度（米）
    address TEXT, -- 地址信息
    district VARCHAR(100), -- 所属区域
    city VARCHAR(100), -- 所属城市
    source VARCHAR(50) DEFAULT 'gps', -- 位置来源：gps, network, manual
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建空间索引
CREATE INDEX IF NOT EXISTS idx_user_locations_location 
ON user_locations USING GIST (location);

-- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_user_locations_time 
ON user_locations (created_at DESC);

-- 创建用户索引
CREATE INDEX IF NOT EXISTS idx_user_locations_user 
ON user_locations (user_id, created_at DESC);

-- 3. 用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL,
    favorite_type VARCHAR(20) NOT NULL, -- 收藏类型：line, stop
    target_id INTEGER NOT NULL, -- 目标ID（线路ID或站点ID）
    target_name VARCHAR(200) NOT NULL, -- 目标名称
    custom_name VARCHAR(200), -- 用户自定义名称
    sort_order INTEGER DEFAULT 0, -- 排序顺序
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, favorite_type, target_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_favorites_user 
ON user_favorites (user_id, favorite_type, sort_order);

-- 4. 用户搜索历史表
CREATE TABLE IF NOT EXISTS user_search_history (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    search_keyword VARCHAR(200) NOT NULL,
    search_type VARCHAR(20) DEFAULT 'all', -- 搜索类型：line, stop, all
    result_count INTEGER DEFAULT 0,
    user_longitude DECIMAL(10,7),
    user_latitude DECIMAL(10,7),
    selected_result_type VARCHAR(20), -- 用户选择的结果类型
    selected_result_id INTEGER, -- 用户选择的结果ID
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_search_history_user 
ON user_search_history (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_search_history_keyword 
ON user_search_history (search_keyword, created_at DESC);

-- 5. 搜索行为统计表
CREATE TABLE IF NOT EXISTS search_behavior_stats (
    id BIGSERIAL PRIMARY KEY,
    search_keyword VARCHAR(200) NOT NULL,
    search_count INTEGER DEFAULT 1,
    result_count_avg DECIMAL(8,2), -- 平均结果数量
    click_through_rate DECIMAL(5,4), -- 点击率
    last_searched_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(search_keyword)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_search_behavior_stats_count 
ON search_behavior_stats (search_count DESC);

CREATE INDEX IF NOT EXISTS idx_search_behavior_stats_ctr 
ON search_behavior_stats (click_through_rate DESC);

-- 6. 用户反馈表
CREATE TABLE IF NOT EXISTS user_feedback (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    feedback_type VARCHAR(50) NOT NULL, -- 反馈类型：bug, suggestion, complaint, praise
    title VARCHAR(200),
    content TEXT NOT NULL,
    related_feature VARCHAR(100), -- 相关功能模块
    related_data JSONB, -- 相关数据（JSON格式）
    contact_info VARCHAR(200), -- 联系方式
    status VARCHAR(20) DEFAULT 'pending', -- 状态：pending, processing, resolved, closed
    priority INTEGER DEFAULT 3, -- 优先级：1-高，2-中，3-低
    assigned_to VARCHAR(100), -- 分配给谁处理
    resolution TEXT, -- 处理结果
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_feedback_status 
ON user_feedback (status, priority, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_feedback_type 
ON user_feedback (feedback_type, created_at DESC);

-- 7. 系统通知表
CREATE TABLE IF NOT EXISTS system_notifications (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'info', -- 通知类型：info, warning, error, maintenance
    target_audience VARCHAR(50) DEFAULT 'all', -- 目标受众：all, users, admins
    priority INTEGER DEFAULT 3, -- 优先级：1-高，2-中，3-低
    is_active BOOLEAN DEFAULT true,
    start_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMPTZ,
    created_by VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_notifications_active 
ON system_notifications (is_active, priority, start_time DESC) 
WHERE is_active = true;

-- 添加表注释
COMMENT ON TABLE arrival_predictions_history IS '到站预测历史记录表，用于分析预测准确性';
COMMENT ON TABLE user_locations IS '用户位置记录表，用于位置服务和分析';
COMMENT ON TABLE user_favorites IS '用户收藏表，存储用户收藏的线路和站点';
COMMENT ON TABLE user_search_history IS '用户搜索历史表，记录用户搜索行为';
COMMENT ON TABLE search_behavior_stats IS '搜索行为统计表，用于搜索优化和热门关键词';
COMMENT ON TABLE user_feedback IS '用户反馈表，收集用户意见和建议';
COMMENT ON TABLE system_notifications IS '系统通知表，发布系统公告和通知';

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_arrival_predictions_history_updated_at 
BEFORE UPDATE ON arrival_predictions_history 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_favorites_updated_at 
BEFORE UPDATE ON user_favorites 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_search_behavior_stats_updated_at 
BEFORE UPDATE ON search_behavior_stats 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_feedback_updated_at 
BEFORE UPDATE ON user_feedback 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_notifications_updated_at 
BEFORE UPDATE ON system_notifications 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
