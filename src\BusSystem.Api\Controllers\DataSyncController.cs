using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Shared.Models.Common;
using BusSystem.Shared.Models.DataSync;
using BusSystem.Api.Authorization;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 数据同步API控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[ApiAuthorize] // 使用统一API认证
public class DataSyncController : ControllerBase
{
    private readonly IDataSyncService _dataSyncService;
    private readonly ILogger<DataSyncController> _logger;

    public DataSyncController(IDataSyncService dataSyncService, ILogger<DataSyncController> logger)
    {
        _dataSyncService = dataSyncService;
        _logger = logger;
    }

    /// <summary>
    /// 同步GPS数据
    /// </summary>
    /// <param name="request">GPS数据同步请求</param>
    /// <param name="apiKey">API密钥</param>
    [HttpPost("gps")]
    [ApiAuthorize("datasync.gps")] // 需要GPS数据同步权限
    public async Task<ActionResult<ApiResponse<SyncDataResponse>>> SyncGpsData(
        [FromBody] GpsDataSyncRequest request)
    {
        try
        {
            // 统一认证已在中间件中处理，这里直接处理业务逻辑

            // 验证请求数据
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("请求数据无效"));
            }

            if (request.GpsData == null || !request.GpsData.Any())
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("GPS数据不能为空"));
            }

            // 限制单次同步的数据量
            if (request.GpsData.Count > 1000)
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("单次同步GPS数据不能超过1000条"));
            }

            var result = await _dataSyncService.SyncGpsDataAsync(request);
            
            if (result.Success)
            {
                return Ok(ApiResponse<SyncDataResponse>.Ok(result, "GPS数据同步成功"));
            }
            else
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("GPS数据同步失败"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GPS数据同步异常，数据源: {DataSource}", request?.DataSource);
            return StatusCode(500, ApiResponse<SyncDataResponse>.Fail("服务器内部错误"));
        }
    }



    /// <summary>
    /// 同步线路数据
    /// </summary>
    /// <param name="request">线路数据同步请求</param>
    [HttpPost("lines")]
    [ApiAuthorize("datasync.lines")] // 需要线路数据同步权限
    public async Task<ActionResult<ApiResponse<SyncDataResponse>>> SyncLineData(
        [FromBody] LineSyncRequest request)
    {
        try
        {
            if (request == null || request.Lines.Count == 0)
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("线路数据不能为空"));
            }

            var result = await _dataSyncService.SyncLineDataAsync(request);

            if (result.Success)
            {
                return Ok(ApiResponse<SyncDataResponse>.Ok(result, "线路数据同步成功"));
            }
            else
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("线路数据同步失败"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "线路数据同步异常");
            return StatusCode(500, ApiResponse<SyncDataResponse>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 同步站点数据
    /// </summary>
    /// <param name="request">站点数据同步请求</param>
    [HttpPost("stops")]
    [ApiAuthorize("datasync.stops")] // 需要站点数据同步权限
    public async Task<ActionResult<ApiResponse<SyncDataResponse>>> SyncStopData(
        [FromBody] StopSyncRequest request)
    {
        try
        {
            if (request == null || request.Stops.Count == 0)
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("站点数据不能为空"));
            }

            var result = await _dataSyncService.SyncStopDataAsync(request);

            if (result.Success)
            {
                return Ok(ApiResponse<SyncDataResponse>.Ok(result, "站点数据同步成功"));
            }
            else
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("站点数据同步失败"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "站点数据同步异常");
            return StatusCode(500, ApiResponse<SyncDataResponse>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 同步车辆数据
    /// </summary>
    /// <param name="request">车辆数据同步请求</param>
    [HttpPost("vehicles")]
    [ApiAuthorize("datasync.vehicles")] // 需要车辆数据同步权限
    public async Task<ActionResult<ApiResponse<SyncDataResponse>>> SyncVehicleData(
        [FromBody] VehicleSyncRequest request)
    {
        try
        {
            if (request == null || request.Vehicles.Count == 0)
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("车辆数据不能为空"));
            }

            var result = await _dataSyncService.SyncVehicleDataAsync(request);

            if (result.Success)
            {
                return Ok(ApiResponse<SyncDataResponse>.Ok(result, "车辆数据同步成功"));
            }
            else
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("车辆数据同步失败"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "车辆数据同步异常");
            return StatusCode(500, ApiResponse<SyncDataResponse>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 同步线路站点关系（专用接口，用于增量更新）
    /// </summary>
    /// <param name="request">线路站点关系同步请求</param>
    [HttpPost("line-stops")]
    [ApiAuthorize("datasync.line_stops")] // 需要线路站点关系同步权限
    public async Task<ActionResult<ApiResponse<SyncDataResponse>>> SyncLineStops(
        [FromBody] LineStopsSyncRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrWhiteSpace(request.ExternalLineId) || request.Stops.Count == 0)
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("线路站点关系数据不能为空"));
            }

            var result = await _dataSyncService.SyncLineStopsAsync(request);

            if (result.Success)
            {
                return Ok(ApiResponse<SyncDataResponse>.Ok(result, "线路站点关系同步成功"));
            }
            else
            {
                return BadRequest(ApiResponse<SyncDataResponse>.Fail("线路站点关系同步失败"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "线路站点关系同步异常");
            return StatusCode(500, ApiResponse<SyncDataResponse>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 健康检查
    /// </summary>
    [HttpGet("health")]
    public ActionResult<ApiResponse<object>> HealthCheck()
    {
        var health = new
        {
            Status = "healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Services = new
            {
                Database = "connected",
                Redis = "connected",
                TimescaleDB = "connected"
            }
        };

        return Ok(ApiResponse<object>.Ok(health));
    }
}
