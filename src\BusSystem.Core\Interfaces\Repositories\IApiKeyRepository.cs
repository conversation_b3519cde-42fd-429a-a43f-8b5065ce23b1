using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Entities;
using static BusSystem.Core.Interfaces.Services.IApiKeyService;

namespace BusSystem.Core.Interfaces.Repositories;

/// <summary>
/// API密钥数据访问接口
/// </summary>
public interface IApiKeyRepository
{
    /// <summary>
    /// 根据API密钥哈希获取密钥信息
    /// </summary>
    /// <param name="apiKeyHash">API密钥哈希值</param>
    /// <returns>API密钥信息，如果不存在则返回null</returns>
    Task<ApiKeyInfo?> GetByHashAsync(string apiKeyHash);

    /// <summary>
    /// 根据ID获取API密钥信息
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <returns>API密钥信息，如果不存在则返回null</returns>
    Task<ApiKeyInfo?> GetByIdAsync(int apiKeyId);

    /// <summary>
    /// 获取API密钥的权限列表
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<Entities.ApiPermission>> GetPermissionsAsync(int apiKeyId);

    /// <summary>
    /// 获取指定时间窗口内的请求次数
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <param name="timeWindow">时间窗口</param>
    /// <returns>请求次数</returns>
    Task<int> GetHourlyRequestCountAsync(int apiKeyId, DateTime timeWindow);

    /// <summary>
    /// 增加请求计数
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    Task IncrementRequestCountAsync(int apiKeyId);

    /// <summary>
    /// 记录API访问日志
    /// </summary>
    /// <param name="accessLog">访问日志</param>
    Task LogAccessAsync(ApiAccessLog accessLog);

    /// <summary>
    /// 更新最后使用时间
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <param name="lastUsedAt">最后使用时间</param>
    Task UpdateLastUsedAsync(int apiKeyId, DateTime lastUsedAt);

    /// <summary>
    /// 创建新的API密钥
    /// </summary>
    /// <param name="apiKeyInfo">API密钥信息</param>
    /// <param name="apiKeyHash">API密钥哈希值</param>
    /// <returns>创建的API密钥信息</returns>
    Task<ApiKeyInfo> CreateAsync(ApiKeyInfo apiKeyInfo, string apiKeyHash);

    /// <summary>
    /// 更新API密钥
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <param name="request">更新请求</param>
    Task UpdateAsync(int apiKeyId, UpdateApiKeyRequest request);

    /// <summary>
    /// 设置API密钥权限
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    /// <param name="permissionNames">权限名称列表</param>
    Task SetPermissionsAsync(int apiKeyId, IEnumerable<string> permissionNames);

    /// <summary>
    /// 禁用API密钥
    /// </summary>
    /// <param name="apiKeyId">API密钥ID</param>
    Task DisableAsync(int apiKeyId);

    /// <summary>
    /// 获取所有API密钥
    /// </summary>
    /// <param name="keyType">密钥类型过滤</param>
    /// <param name="isActive">激活状态过滤</param>
    /// <returns>API密钥列表</returns>
    Task<IEnumerable<ApiKeyInfo>> GetAllAsync(string? keyType = null, bool? isActive = null);

    /// <summary>
    /// 获取所有权限
    /// </summary>
    /// <returns>权限列表</returns>
    Task<IEnumerable<Entities.ApiPermission>> GetAllPermissionsAsync();

    /// <summary>
    /// 根据权限名称获取权限ID
    /// </summary>
    /// <param name="permissionNames">权限名称列表</param>
    /// <returns>权限ID列表</returns>
    Task<IEnumerable<int>> GetPermissionIdsByNamesAsync(IEnumerable<string> permissionNames);

    /// <summary>
    /// 清理过期的速率限制记录
    /// </summary>
    /// <param name="cutoffTime">截止时间</param>
    /// <returns>清理的记录数</returns>
    Task<int> CleanupExpiredRateLimitsAsync(DateTime cutoffTime);

    /// <summary>
    /// 清理过期的访问日志
    /// </summary>
    /// <param name="cutoffTime">截止时间</param>
    /// <returns>清理的记录数</returns>
    Task<int> CleanupExpiredAccessLogsAsync(DateTime cutoffTime);
}
