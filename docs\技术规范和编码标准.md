# 技术规范和编码标准

## 1. 项目结构规范

### 1.1 解决方案结构
```
BusSystem/
├── src/
│   ├── BusSystem.Core/              # 核心业务层
│   │   ├── Entities/                # 实体类
│   │   ├── Interfaces/              # 接口定义
│   │   │   ├── Services/            # 业务服务接口
│   │   │   └── Repositories/        # 数据访问接口
│   │   ├── Services/                # 业务服务实现
│   │   │   ├── Prediction/          # 预测相关服务
│   │   │   ├── Timetable/           # 时间表相关服务
│   │   │   └── Cache/               # 缓存相关服务
│   │   ├── Models/                  # 数据传输对象
│   │   └── Exceptions/              # 自定义异常
│   ├── BusSystem.Infrastructure/    # 基础设施层
│   │   ├── Data/                    # 数据访问实现
│   │   ├── Cache/                   # 缓存实现
│   │   ├── External/                # 外部服务集成
│   │   └── Configuration/           # 配置管理
│   ├── BusSystem.API/               # API层
│   │   ├── Controllers/             # 控制器
│   │   ├── Middleware/              # 中间件
│   │   ├── Filters/                 # 过滤器
│   │   └── Extensions/              # 扩展方法
│   └── BusSystem.Tests/             # 测试项目
│       ├── Unit/                    # 单元测试
│       ├── Integration/             # 集成测试
│       └── Performance/             # 性能测试
├── docs/                            # 文档
└── scripts/                         # 脚本文件
```

### 1.2 命名空间规范
```csharp
// 核心业务层
namespace BusSystem.Core.Services.Prediction;
namespace BusSystem.Core.Interfaces.Services;
namespace BusSystem.Core.Entities;

// 基础设施层
namespace BusSystem.Infrastructure.Data.Repositories;
namespace BusSystem.Infrastructure.Cache;

// API层
namespace BusSystem.API.Controllers;
namespace BusSystem.API.Models;
```

## 2. 编码规范

### 2.1 命名规范

#### 2.1.1 类和接口命名
```csharp
// 接口：I + 描述性名词
public interface IPredictionService { }
public interface ITimetableManager { }
public interface IHotspotAnalyzer { }

// 实现类：描述性名词
public class SmartPredictionService : IPredictionService { }
public class IntelligentTimetableManager : ITimetableManager { }

// 实体类：业务名词
public class StationTravelTime { }
public class VehiclePrediction { }
public class TimetableGenerationResult { }
```

#### 2.1.2 方法命名
```csharp
// 异步方法：动词 + Async后缀
public async Task<List<StopPrediction>> PredictMultipleStopsAsync(int vehicleId, List<int> stopIds);
public async Task<TimetableGenerationResult> GenerateTimetableAsync(int lineId);

// 查询方法：Get/Find/Query + 名词
public async Task<VehicleLocationState> GetVehicleLocationStateAsync(int vehicleId);
public async Task<List<int>> FindHotStopsAsync(int lineId);

// 计算方法：Calculate/Compute + 名词
public double CalculateHotnessScore(int stopId);
public TimeSpan ComputeTravelTime(int fromStopId, int toStopId);
```

#### 2.1.3 变量和参数命名
```csharp
// 使用有意义的名称
var vehicleLocationState = await GetVehicleLocationStateAsync(vehicleId);
var travelTimeSeconds = await CalculateTravelTimeAsync(fromStopId, toStopId);

// 避免缩写和单字母变量（除了循环计数器）
// ❌ 错误示例
var vls = await GetVehicleLocationStateAsync(vid);
var tts = await CalculateTravelTimeAsync(fid, tid);

// ✅ 正确示例
var vehicleLocationState = await GetVehicleLocationStateAsync(vehicleId);
var travelTimeSeconds = await CalculateTravelTimeAsync(fromStopId, toStopId);
```

### 2.2 代码结构规范

#### 2.2.1 类结构顺序
```csharp
public class SmartPredictionService : IPredictionService
{
    // 1. 常量
    private const int DEFAULT_CACHE_EXPIRY_MINUTES = 2;
    
    // 2. 字段
    private readonly ITimetableManager _timetableManager;
    private readonly ILogger<SmartPredictionService> _logger;
    
    // 3. 构造函数
    public SmartPredictionService(
        ITimetableManager timetableManager,
        ILogger<SmartPredictionService> logger)
    {
        _timetableManager = timetableManager;
        _logger = logger;
    }
    
    // 4. 公共方法
    public async Task<List<StopPrediction>> PredictMultipleStopsAsync(int vehicleId, List<int> stopIds)
    {
        // 实现...
    }
    
    // 5. 私有方法
    private double CalculateConfidence(int stopId, int nextStopId)
    {
        // 实现...
    }
}
```

#### 2.2.2 方法结构规范
```csharp
public async Task<List<StopPrediction>> PredictMultipleStopsAsync(int vehicleId, List<int> stopIds)
{
    // 1. 参数验证
    if (vehicleId <= 0)
        throw new ArgumentException("车辆ID必须大于0", nameof(vehicleId));
    
    if (stopIds == null || !stopIds.Any())
        throw new ArgumentException("站点列表不能为空", nameof(stopIds));
    
    // 2. 日志记录
    _logger.LogDebug("开始预测车辆 {VehicleId} 到站点 {StopIds} 的时间", vehicleId, string.Join(",", stopIds));
    
    try
    {
        // 3. 核心业务逻辑
        var vehicleState = await GetVehicleLocationStateAsync(vehicleId);
        var predictions = await CalculatePredictionsAsync(vehicleState, stopIds);
        
        // 4. 结果处理
        await CachePredictionsAsync(predictions);
        
        _logger.LogDebug("预测完成，车辆 {VehicleId}，预测数量: {Count}", vehicleId, predictions.Count);
        return predictions;
    }
    catch (Exception ex)
    {
        // 5. 异常处理
        _logger.LogError(ex, "预测失败，车辆ID: {VehicleId}", vehicleId);
        throw;
    }
}
```

## 3. 错误处理规范

### 3.1 自定义异常
```csharp
// 业务异常基类
public abstract class BusSystemException : Exception
{
    protected BusSystemException(string message) : base(message) { }
    protected BusSystemException(string message, Exception innerException) : base(message, innerException) { }
}

// 具体业务异常
public class PredictionException : BusSystemException
{
    public PredictionException(string message) : base(message) { }
    public PredictionException(string message, Exception innerException) : base(message, innerException) { }
}

public class TimetableException : BusSystemException
{
    public TimetableException(string message) : base(message) { }
}

public class VehicleNotFoundException : BusSystemException
{
    public VehicleNotFoundException(int vehicleId) 
        : base($"未找到车辆，ID: {vehicleId}") { }
}
```

### 3.2 异常处理策略
```csharp
public async Task<List<StopPrediction>> PredictMultipleStopsAsync(int vehicleId, List<int> stopIds)
{
    try
    {
        // 业务逻辑
        return await CalculatePredictionsAsync(vehicleId, stopIds);
    }
    catch (VehicleNotFoundException ex)
    {
        // 记录警告日志，返回空结果
        _logger.LogWarning(ex, "车辆不存在，返回空预测结果");
        return new List<StopPrediction>();
    }
    catch (TimetableException ex)
    {
        // 降级到简单预测算法
        _logger.LogWarning(ex, "时间表查询失败，使用降级算法");
        return await FallbackPredictionAsync(vehicleId, stopIds);
    }
    catch (Exception ex)
    {
        // 记录错误日志，重新抛出
        _logger.LogError(ex, "预测服务发生未知错误，车辆ID: {VehicleId}", vehicleId);
        throw new PredictionException("预测服务暂时不可用，请稍后重试", ex);
    }
}
```

## 4. 日志规范

### 4.1 日志级别使用
```csharp
// Trace: 详细的调试信息
_logger.LogTrace("进入方法 PredictMultipleStopsAsync，参数: vehicleId={VehicleId}", vehicleId);

// Debug: 调试信息
_logger.LogDebug("缓存命中，键: {CacheKey}", cacheKey);

// Information: 一般信息
_logger.LogInformation("预测计算完成，车辆 {VehicleId}，耗时 {ElapsedMs}ms", vehicleId, elapsed);

// Warning: 警告信息
_logger.LogWarning("GPS数据质量较差，车辆 {VehicleId}，置信度: {Confidence}", vehicleId, confidence);

// Error: 错误信息
_logger.LogError(ex, "预测计算失败，车辆 {VehicleId}", vehicleId);

// Critical: 严重错误
_logger.LogCritical(ex, "数据库连接失败，系统无法正常工作");
```

### 4.2 结构化日志
```csharp
// 使用结构化参数
_logger.LogInformation("预测结果: {@PredictionResult}", new
{
    VehicleId = vehicleId,
    StopCount = predictions.Count,
    AverageConfidence = predictions.Average(p => p.Confidence),
    CalculationTime = stopwatch.ElapsedMilliseconds
});

// 性能监控日志
using var activity = _logger.BeginScope("PredictionCalculation");
_logger.LogInformation("开始预测计算，车辆 {VehicleId}", vehicleId);
// ... 业务逻辑
_logger.LogInformation("预测计算完成，耗时 {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
```

## 5. 性能规范

### 5.1 异步编程规范
```csharp
// ✅ 正确：使用ConfigureAwait(false)
var result = await SomeAsyncMethod().ConfigureAwait(false);

// ✅ 正确：并行执行独立任务
var tasks = stopIds.Select(async stopId => 
    await CalculatePredictionAsync(vehicleId, stopId).ConfigureAwait(false));
var results = await Task.WhenAll(tasks).ConfigureAwait(false);

// ❌ 错误：同步等待异步方法
var result = SomeAsyncMethod().Result; // 可能导致死锁

// ❌ 错误：不必要的异步
public async Task<int> GetConstantValueAsync()
{
    return await Task.FromResult(42); // 应该直接返回42
}
```

### 5.2 缓存使用规范
```csharp
public async Task<T> GetWithCacheAsync<T>(string cacheKey, Func<Task<T>> factory, TimeSpan expiry)
{
    // 1. 尝试从缓存获取
    var cached = await _cache.GetAsync<T>(cacheKey).ConfigureAwait(false);
    if (cached != null)
    {
        _logger.LogDebug("缓存命中: {CacheKey}", cacheKey);
        return cached;
    }
    
    // 2. 缓存未命中，执行工厂方法
    _logger.LogDebug("缓存未命中: {CacheKey}", cacheKey);
    var result = await factory().ConfigureAwait(false);
    
    // 3. 设置缓存
    if (result != null)
    {
        await _cache.SetAsync(cacheKey, result, expiry).ConfigureAwait(false);
    }
    
    return result;
}
```

## 6. 测试规范

### 6.1 单元测试规范
```csharp
[TestClass]
public class SmartPredictionServiceTests
{
    private Mock<ITimetableManager> _mockTimetableManager;
    private Mock<ILogger<SmartPredictionService>> _mockLogger;
    private SmartPredictionService _service;
    
    [TestInitialize]
    public void Setup()
    {
        _mockTimetableManager = new Mock<ITimetableManager>();
        _mockLogger = new Mock<ILogger<SmartPredictionService>>();
        _service = new SmartPredictionService(_mockTimetableManager.Object, _mockLogger.Object);
    }
    
    [TestMethod]
    public async Task PredictMultipleStopsAsync_ValidInput_ReturnsExpectedResults()
    {
        // Arrange
        var vehicleId = 1;
        var stopIds = new List<int> { 101, 102, 103 };
        var expectedPredictions = CreateTestPredictions();
        
        _mockTimetableManager
            .Setup(x => x.GetTravelTimeAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<DateTime>()))
            .ReturnsAsync(120); // 2分钟
        
        // Act
        var result = await _service.PredictMultipleStopsAsync(vehicleId, stopIds);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(stopIds.Count, result.Count);
        Assert.IsTrue(result.All(p => p.VehicleId == vehicleId));
    }
    
    [TestMethod]
    [ExpectedException(typeof(ArgumentException))]
    public async Task PredictMultipleStopsAsync_InvalidVehicleId_ThrowsException()
    {
        // Act & Assert
        await _service.PredictMultipleStopsAsync(-1, new List<int> { 101 });
    }
}
```

### 6.2 集成测试规范
```csharp
[TestClass]
public class PredictionIntegrationTests : IntegrationTestBase
{
    [TestMethod]
    public async Task EndToEnd_PredictionFlow_WorksCorrectly()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Act
        var response = await _httpClient.GetAsync("/api/prediction/stop/101");
        
        // Assert
        response.EnsureSuccessStatusCode();
        var predictions = await response.Content.ReadFromJsonAsync<List<VehiclePrediction>>();
        Assert.IsNotNull(predictions);
        Assert.IsTrue(predictions.Any());
    }
}
```

## 7. 配置管理规范

### 7.1 配置结构
```json
{
  "Prediction": {
    "Cache": {
      "DefaultExpiryMinutes": 2,
      "HotDataExpirySeconds": 30,
      "MaxCacheSize": 10000
    },
    "Timetable": {
      "MinSampleCount": 10,
      "ConfidenceThreshold": 0.7,
      "UpdateIntervalHours": 24
    },
    "Performance": {
      "MaxConcurrentPredictions": 100,
      "TimeoutSeconds": 30,
      "RetryCount": 3
    }
  }
}
```

### 7.2 配置类定义
```csharp
public class PredictionOptions
{
    public const string SectionName = "Prediction";
    
    public CacheOptions Cache { get; set; } = new();
    public TimetableOptions Timetable { get; set; } = new();
    public PerformanceOptions Performance { get; set; } = new();
}

public class CacheOptions
{
    public int DefaultExpiryMinutes { get; set; } = 2;
    public int HotDataExpirySeconds { get; set; } = 30;
    public int MaxCacheSize { get; set; } = 10000;
}
```

这些技术规范将确保代码质量、一致性和可维护性，为项目的成功实施提供坚实的基础。
