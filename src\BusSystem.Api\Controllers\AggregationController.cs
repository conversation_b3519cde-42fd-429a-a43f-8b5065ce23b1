using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Shared.Models.Common;
using BusSystem.Shared.Enums;
using BusSystem.Api.Authorization;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 聚合查询API控制器 - 提供多数据源聚合查询服务
/// </summary>
[ApiController]
[Route("api/[controller]")]
[ApiAuthorize] // 添加API认证
public class AggregationController : ControllerBase
{
    private readonly IAggregationService _aggregationService;
    private readonly IStopService _stopService;
    private readonly ILogger<AggregationController> _logger;

    public AggregationController(IAggregationService aggregationService, IStopService stopService, ILogger<AggregationController> logger)
    {
        _aggregationService = aggregationService;
        _stopService = stopService;
        _logger = logger;
    }

    /// <summary>
    /// 获取附近站点及实时信息（聚合查询主接口）
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    /// <param name="radius">搜索半径（米，默认500米）</param>
    /// <param name="coordinateSystem">返回的坐标系（默认GCJ02，适用于高德、腾讯地图）</param>
    /// <param name="mergeSameNameStops">是否合并同名站点（默认true）</param>
    [HttpGet("nearby-stops")]
    public async Task<ActionResult<ApiResponse<object>>> GetNearbyStopsWithRealtimeInfo(
        [FromQuery] double longitude,
        [FromQuery] double latitude,
        [FromQuery] double radius = 500,
        [FromQuery] CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02,
        [FromQuery] bool mergeSameNameStops = true)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
            }

            // 限制搜索半径
            if (radius <= 0 || radius > 2000) radius = 500;

            var result = await _aggregationService.GetNearbyStopsWithRealtimeInfoAsync(longitude, latitude, radius, coordinateSystem, mergeSameNameStops);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取附近站点信息失败，位置: ({Longitude}, {Latitude})", longitude, latitude);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取站点详细信息及到站预测
    /// </summary>
    /// <param name="stopId">站点ID</param>
    /// <param name="coordinateSystem">返回的坐标系（默认GCJ02，适用于高德、腾讯地图）</param>
    [HttpGet("stop/{stopId}/detail")]
    public async Task<ActionResult<ApiResponse<object>>> GetStopDetailWithPredictions(
        int stopId,
        [FromQuery] CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            var result = await _aggregationService.GetStopDetailWithPredictionsAsync(stopId, coordinateSystem);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "站点不存在，ID: {StopId}", stopId);
            return NotFound(ApiResponse<object>.Fail("站点不存在"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点详细信息失败，ID: {StopId}", stopId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取可切换的站点列表（同名且附近的站点）
    /// </summary>
    /// <param name="stopId">当前站点ID</param>
    [HttpGet("stop/{stopId}/alternatives")]
    public async Task<ActionResult<ApiResponse<object>>> GetAlternativeStops(int stopId)
    {
        try
        {
            var result = await _stopService.GetAlternativeStopsAsync(stopId);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "站点不存在，ID: {StopId}", stopId);
            return NotFound(ApiResponse<object>.Fail("站点不存在"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可切换站点失败，ID: {StopId}", stopId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取单个站点的到站预测信息
    /// </summary>
    /// <param name="stopId">站点ID</param>
    /// <param name="coordinateSystem">返回的坐标系（默认GCJ02，适用于高德、腾讯地图）</param>
    [HttpGet("stop/{stopId}/predictions")]
    public async Task<ActionResult<ApiResponse<object>>> GetSingleStopPredictions(
        int stopId,
        [FromQuery] CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            var result = await _aggregationService.GetSingleStopPredictionsAsync(stopId, coordinateSystem);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "站点不存在，ID: {StopId}", stopId);
            return NotFound(ApiResponse<object>.Fail("站点不存在"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取站点到站预测失败，ID: {StopId}", stopId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 搜索站点和线路（综合搜索）
    /// </summary>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="longitude">用户经度（可选，用于距离排序）</param>
    /// <param name="latitude">用户纬度（可选，用于距离排序）</param>
    /// <param name="coordinateSystem">返回的坐标系（默认GCJ02，适用于高德、腾讯地图）</param>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<object>>> SearchStopsAndLines(
        [FromQuery] string keyword,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] CoordinateSystem coordinateSystem = CoordinateSystem.GCJ02)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return BadRequest(ApiResponse<object>.Fail("搜索关键词不能为空"));
            }

            // 验证坐标（如果提供）
            if (longitude.HasValue && latitude.HasValue)
            {
                if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
                {
                    return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
                }
            }

            var result = await _aggregationService.SearchStopsAndLinesAsync(keyword, longitude, latitude, coordinateSystem);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索失败，关键词: {Keyword}", keyword);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取基于位置的推荐信息
    /// </summary>
    /// <param name="longitude">经度</param>
    /// <param name="latitude">纬度</param>
    [HttpGet("recommendations")]
    public async Task<ActionResult<ApiResponse<object>>> GetRecommendations(
        [FromQuery] double longitude,
        [FromQuery] double latitude)
    {
        try
        {
            // 验证坐标范围
            if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
            {
                return BadRequest(ApiResponse<object>.Fail("坐标参数无效"));
            }

            // 获取附近站点（较小半径）
            var nearbyInfo = await _aggregationService.GetNearbyStopsWithRealtimeInfoAsync(longitude, latitude, 300);

            // 构建推荐信息
            var recommendations = new
            {
                NearbyStops = nearbyInfo,
                Tips = new[]
                {
                    "点击站点可查看详细信息和到站预测",
                    "使用搜索功能快速找到目标线路",
                    "长按站点可查看可切换的同名站点"
                },
                Timestamp = DateTime.UtcNow
            };

            return Ok(ApiResponse<object>.Ok(recommendations));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取推荐信息失败，位置: ({Longitude}, {Latitude})", longitude, latitude);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取可切换的线路方向列表（同线路编号的不同方向）
    /// </summary>
    /// <param name="lineId">当前线路ID</param>
    [HttpGet("line/{lineId}/alternatives")]
    public async Task<ActionResult<ApiResponse<object>>> GetAlternativeLines(int lineId)
    {
        try
        {
            var result = await _aggregationService.GetAlternativeLinesAsync(lineId);
            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "线路不存在，ID: {LineId}", lineId);
            return NotFound(ApiResponse<object>.Fail("线路不存在"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可切换线路失败，线路ID: {LineId}", lineId);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }
}
