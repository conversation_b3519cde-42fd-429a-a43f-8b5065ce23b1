# 预测算法核心实现

## 1. 位置推断服务实现

### 1.1 LocationInferenceService

```csharp
public class LocationInferenceService : ILocationInferenceService
{
    private readonly IVehiclePositionRepository _positionRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IBusStopRepository _stopRepository;
    private readonly IRouteMatchingService _routeMatchingService;
    private readonly IRedisService _redisService;
    private readonly ILogger<LocationInferenceService> _logger;

    public async Task<VehicleLocationState> InferLocationStateAsync(VehiclePosition position)
    {
        // 1. 检查是否已有站点信息
        if (position.NextStopId.HasValue)
        {
            return new VehicleLocationState
            {
                VehicleId = position.VehicleId,
                Timestamp = position.Timestamp,
                Latitude = position.Latitude,
                Longitude = position.Longitude,
                CurrentStopId = position.CurrentStopId,
                NextStopId = position.NextStopId,
                IsInferred = false,
                MatchingConfidence = 1.0
            };
        }

        // 2. 基于GPS位置推断
        return await InferFromGpsAsync(position);
    }

    private async Task<VehicleLocationState> InferFromGpsAsync(VehiclePosition position)
    {
        // 获取车辆所属线路
        var vehicle = await _vehicleRepository.GetWithLineAsync(position.VehicleId);
        if (vehicle?.Line == null)
        {
            throw new InvalidOperationException($"车辆 {position.VehicleId} 未分配线路");
        }

        // 路径匹配
        var matchResult = await _routeMatchingService.MatchToRouteAsync(
            position.Latitude, position.Longitude, vehicle.LineId);

        if (matchResult.Confidence < 0.5)
        {
            _logger.LogWarning("GPS匹配置信度过低: {Confidence}", matchResult.Confidence);
        }

        // 推断下一站
        var nextStopId = await InferNextStopFromRoutePosition(
            vehicle.LineId, matchResult.RoutePosition, position);

        // 计算到下一站的距离
        var distanceToNext = nextStopId.HasValue 
            ? await CalculateDistanceToStopAsync(position, nextStopId.Value)
            : null;

        return new VehicleLocationState
        {
            VehicleId = position.VehicleId,
            Timestamp = position.Timestamp,
            Latitude = position.Latitude,
            Longitude = position.Longitude,
            CurrentStopId = await InferCurrentStopAsync(position, matchResult),
            NextStopId = nextStopId,
            DistanceToNextStop = distanceToNext,
            IsInferred = true,
            MatchingConfidence = matchResult.Confidence
        };
    }

    private async Task<int?> InferNextStopFromRoutePosition(
        int lineId, double routePosition, VehiclePosition position)
    {
        // 获取线路站点序列
        var lineWithStops = await _lineRepository.GetWithStopsAsync(lineId);
        if (lineWithStops?.LineStops == null) return null;

        // 判断行驶方向
        var direction = await DetermineDirectionAsync(position);
        
        // 根据方向和位置找到下一站
        var orderedStops = direction == TravelDirection.Forward 
            ? lineWithStops.LineStops.OrderBy(ls => ls.SequenceNumber)
            : lineWithStops.LineStops.OrderByDescending(ls => ls.SequenceNumber);

        foreach (var lineStop in orderedStops)
        {
            var stopPosition = await GetStopRoutePositionAsync(lineId, lineStop.StopId);
            if ((direction == TravelDirection.Forward && stopPosition > routePosition) ||
                (direction == TravelDirection.Backward && stopPosition < routePosition))
            {
                return lineStop.StopId;
            }
        }

        return null;
    }
}
```

### 1.2 路径匹配算法

```csharp
public class RouteMatchingService : IRouteMatchingService
{
    public async Task<RouteMatchResult> MatchToRouteAsync(double latitude, double longitude, int lineId)
    {
        // 获取线路路径点或站点序列
        var routePoints = await GetRoutePointsAsync(lineId);
        
        var bestMatch = new RouteMatchResult { Confidence = 0 };
        
        for (int i = 0; i < routePoints.Count - 1; i++)
        {
            var segment = new RouteSegment(routePoints[i], routePoints[i + 1]);
            var match = MatchToSegment(latitude, longitude, segment);
            
            if (match.Confidence > bestMatch.Confidence)
            {
                bestMatch = match;
            }
        }
        
        return bestMatch;
    }

    private RouteMatchResult MatchToSegment(double lat, double lon, RouteSegment segment)
    {
        // 计算点到线段的最短距离和投影点
        var projection = CalculateProjection(lat, lon, segment);
        var distance = CalculateDistance(lat, lon, projection.Latitude, projection.Longitude);
        
        // 计算置信度（距离越近置信度越高）
        var confidence = Math.Max(0, 1 - distance / 100.0); // 100米内置信度线性递减
        
        return new RouteMatchResult
        {
            Latitude = projection.Latitude,
            Longitude = projection.Longitude,
            RoutePosition = projection.RoutePosition,
            Distance = distance,
            Confidence = confidence
        };
    }
}
```

## 2. 动态速度预测算法

### 2.1 多因素速度预测

```csharp
public class DynamicSpeedPredictor
{
    public async Task<double> PredictSpeedAsync(PredictionContext context)
    {
        // 基础速度：历史平均速度 + 当前速度的加权平均
        var baseSpeed = await CalculateBaseSpeedAsync(context);
        
        // 时间因子：不同时间段的调整
        var timeFactor = GetTimeOfDayFactor(context.PredictionTime);
        
        // 路段因子：不同路段的速度特征
        var routeFactor = await GetRouteFactorAsync(context.Line.Id, context.LocationState);
        
        // 交通因子：实时交通状况
        var trafficFactor = await GetTrafficFactorAsync(context);
        
        // 天气因子：天气对速度的影响
        var weatherFactor = await GetWeatherFactorAsync(context.PredictionTime);
        
        var predictedSpeed = baseSpeed * timeFactor * routeFactor * trafficFactor * weatherFactor;
        
        // 限制在合理范围内
        return Math.Max(5, Math.Min(80, predictedSpeed));
    }

    private async Task<double> CalculateBaseSpeedAsync(PredictionContext context)
    {
        var currentWeight = 0.3;
        var historyWeight = 0.7;
        
        var currentSpeed = context.CurrentSpeed > 0 ? context.CurrentSpeed : context.AverageSpeed;
        var historicalSpeed = context.AverageSpeed;
        
        return currentSpeed * currentWeight + historicalSpeed * historyWeight;
    }

    private double GetTimeOfDayFactor(DateTime time)
    {
        var hour = time.Hour;
        
        // 早高峰 (7-9点)
        if (hour >= 7 && hour <= 9) return 0.7;
        
        // 晚高峰 (17-19点)
        if (hour >= 17 && hour <= 19) return 0.75;
        
        // 夜间 (22-6点)
        if (hour >= 22 || hour <= 6) return 1.2;
        
        // 平峰期
        return 1.0;
    }

    private async Task<double> GetRouteFactorAsync(int lineId, VehicleLocationState locationState)
    {
        // 根据线路特征调整速度
        // 市区线路：0.8，郊区线路：1.1，快速公交：1.3
        
        var cacheKey = $"route_factor:{lineId}";
        var cached = await _redisService.GetAsync<double?>(cacheKey);
        
        if (cached.HasValue) return cached.Value;
        
        // 计算线路速度特征（基于历史数据）
        var factor = await CalculateRouteSpeedFactorAsync(lineId);
        
        await _redisService.SetAsync(cacheKey, factor, TimeSpan.FromHours(1));
        return factor;
    }
}
```

## 3. 路径距离计算算法

### 3.1 精确距离计算

```csharp
public class RouteDistanceCalculator
{
    public async Task<double> CalculateRouteDistanceAsync(
        int lineId, double fromLat, double fromLon, int toStopId)
    {
        // 1. 获取线路路径数据
        var routePoints = await GetRoutePointsAsync(lineId);
        
        if (routePoints.Any())
        {
            // 有详细路径数据，计算沿路径的实际距离
            return await CalculatePathDistanceAsync(fromLat, fromLon, toStopId, routePoints);
        }
        else
        {
            // 只有站点数据，使用站点间标准距离
            return await CalculateStationDistanceAsync(lineId, fromLat, fromLon, toStopId);
        }
    }

    private async Task<double> CalculatePathDistanceAsync(
        double fromLat, double fromLon, int toStopId, List<RoutePoint> routePoints)
    {
        // 1. 找到起点在路径上的投影位置
        var startProjection = FindNearestPointOnPath(fromLat, fromLon, routePoints);
        
        // 2. 找到目标站点在路径上的位置
        var targetStop = await _stopRepository.GetByIdAsync(toStopId);
        var endProjection = FindNearestPointOnPath(targetStop.Latitude, targetStop.Longitude, routePoints);
        
        // 3. 计算沿路径的累积距离
        var totalDistance = 0.0;
        var startIndex = startProjection.SegmentIndex;
        var endIndex = endProjection.SegmentIndex;
        
        // 从起点投影到第一个路径点的距离
        totalDistance += CalculateDistance(
            startProjection.Latitude, startProjection.Longitude,
            routePoints[startIndex + 1].Latitude, routePoints[startIndex + 1].Longitude);
        
        // 中间路径段的距离
        for (int i = startIndex + 1; i < endIndex; i++)
        {
            totalDistance += CalculateDistance(
                routePoints[i].Latitude, routePoints[i].Longitude,
                routePoints[i + 1].Latitude, routePoints[i + 1].Longitude);
        }
        
        // 最后一个路径点到终点投影的距离
        if (endIndex > startIndex)
        {
            totalDistance += CalculateDistance(
                routePoints[endIndex].Latitude, routePoints[endIndex].Longitude,
                endProjection.Latitude, endProjection.Longitude);
        }
        
        return totalDistance;
    }

    private async Task<double> CalculateStationDistanceAsync(
        int lineId, double fromLat, double fromLon, int toStopId)
    {
        // 获取线路站点序列
        var lineWithStops = await _lineRepository.GetWithStopsAsync(lineId);
        if (lineWithStops?.LineStops == null) return 0;
        
        var targetStop = await _stopRepository.GetByIdAsync(toStopId);
        if (targetStop == null) return 0;
        
        // 找到最近的起始站点
        var nearestStop = await FindNearestStopAsync(fromLat, fromLon, lineWithStops.LineStops);
        
        // 计算站点间的累积距离
        return await CalculateStationToStationDistanceAsync(nearestStop.StopId, toStopId, lineWithStops);
    }
}
```

## 4. 预测结果优化

### 4.1 多车辆数据融合

```csharp
public class MultiVehicleDataFusion
{
    public async Task<double> FusePredictionsAsync(int stopId, List<PredictionResult> predictions)
    {
        if (!predictions.Any()) return 0;
        
        // 按置信度加权平均
        var weightedSum = 0.0;
        var totalWeight = 0.0;
        
        foreach (var prediction in predictions)
        {
            var weight = CalculatePredictionWeight(prediction);
            weightedSum += prediction.EstimatedMinutes * weight;
            totalWeight += weight;
        }
        
        return totalWeight > 0 ? weightedSum / totalWeight : predictions.First().EstimatedMinutes;
    }

    private double CalculatePredictionWeight(PredictionResult prediction)
    {
        var baseWeight = prediction.Confidence;
        
        // 距离越近权重越高
        if (prediction.Distance < 500) baseWeight *= 1.2;
        else if (prediction.Distance > 2000) baseWeight *= 0.8;
        
        // 数据越新权重越高
        var dataAge = (DateTime.UtcNow - prediction.Timestamp).TotalMinutes;
        if (dataAge > 5) baseWeight *= 0.8;
        
        return Math.Max(0.1, baseWeight);
    }
}
```

## 5. 性能优化策略

### 5.1 缓存策略

```csharp
public class PredictionCacheManager
{
    private readonly IRedisService _redisService;
    
    public async Task<T?> GetCachedResultAsync<T>(string cacheKey) where T : class
    {
        return await _redisService.GetAsync<T>(cacheKey);
    }
    
    public async Task CacheResultAsync<T>(string cacheKey, T result, TimeSpan expiry)
    {
        await _redisService.SetAsync(cacheKey, result, expiry);
    }
    
    public string GeneratePredictionCacheKey(int vehicleId, int stopId)
    {
        return $"prediction:{vehicleId}:{stopId}:{DateTime.UtcNow:yyyyMMddHHmm}";
    }
}
```

### 5.2 批量处理优化

```csharp
public class BatchPredictionProcessor
{
    public async Task<List<PredictionResult>> ProcessBatchPredictionsAsync(
        List<PredictionRequest> requests)
    {
        // 按线路分组，减少数据库查询
        var groupedRequests = requests.GroupBy(r => r.LineId);
        var results = new List<PredictionResult>();
        
        foreach (var group in groupedRequests)
        {
            var lineData = await LoadLineDataAsync(group.Key);
            
            var tasks = group.Select(request => 
                ProcessSinglePredictionAsync(request, lineData));
            
            var groupResults = await Task.WhenAll(tasks);
            results.AddRange(groupResults);
        }
        
        return results;
    }
}
```

这个完整的设计方案解决了当前预测服务的主要问题，提供了处理不同调度系统数据类型的能力，并大幅提升了预测精度和系统性能。
