using Microsoft.AspNetCore.Mvc;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Shared.Models.Common;
using BusSystem.Api.Authorization;

namespace BusSystem.Api.Controllers;

/// <summary>
/// 系统管理API控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[ApiAuthorize("admin")] // 需要管理员权限
public class AdminController : ControllerBase
{
    private readonly IApiKeyService _apiKeyService;
    private readonly ILogger<AdminController> _logger;

    public AdminController(IApiKeyService apiKeyService, ILogger<AdminController> logger)
    {
        _apiKeyService = apiKeyService;
        _logger = logger;
    }

    /// <summary>
    /// 获取API密钥列表
    /// </summary>
    /// <param name="keyType">密钥类型（可选）</param>
    /// <param name="isActive">是否激活（可选）</param>
    [HttpGet("api-keys")]
    public async Task<ActionResult<ApiResponse<object>>> GetApiKeys(
        [FromQuery] string? keyType = null,
        [FromQuery] bool? isActive = null)
    {
        try
        {
            var apiKeys = await _apiKeyService.GetApiKeysAsync(keyType, isActive);
            
            var result = new
            {
                ApiKeys = apiKeys.Select(ak => new
                {
                    ak.Id,
                    ak.KeyName,
                    ak.KeyType,
                    ak.Description,
                    ak.OwnerName,
                    ak.IsActive,
                    ak.RateLimitPerHour,
                    ak.RateLimitPerDay,
                    ak.ExpiresAt,
                    ak.LastUsedAt,
                    ak.UsageCount,
                    ak.CreatedAt,
                    PermissionCount = ak.Permissions.Count
                }),
                Total = apiKeys.Count()
            };

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取API密钥列表失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 创建API密钥
    /// </summary>
    /// <param name="request">创建请求</param>
    [HttpPost("api-keys")]
    public async Task<ActionResult<ApiResponse<object>>> CreateApiKey([FromBody] CreateApiKeyRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Fail("请求数据无效"));
            }

            var createResult = await _apiKeyService.CreateApiKeyAsync(request);

            var result = new
            {
                createResult.ApiKeyInfo.Id,
                createResult.ApiKeyInfo.KeyName,
                createResult.ApiKeyInfo.KeyType,
                createResult.ApiKeyInfo.Description,
                createResult.ApiKeyInfo.OwnerName,
                createResult.ApiKeyInfo.IsActive,
                createResult.ApiKeyInfo.RateLimitPerHour,
                createResult.ApiKeyInfo.RateLimitPerDay,
                createResult.ApiKeyInfo.ExpiresAt,
                createResult.ApiKeyInfo.CreatedAt,
                Permissions = createResult.ApiKeyInfo.Permissions.Select(p => p.PermissionName),
                ApiKey = createResult.ApiKey, // 仅在创建时返回
                SecurityNotice = createResult.SecurityNotice
            };

            return Ok(ApiResponse<object>.Ok(result, "API密钥创建成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建API密钥失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 更新API密钥
    /// </summary>
    /// <param name="id">API密钥ID</param>
    /// <param name="request">更新请求</param>
    [HttpPut("api-keys/{id}")]
    public async Task<ActionResult<ApiResponse<object>>> UpdateApiKey(int id, [FromBody] UpdateApiKeyRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponse<object>.Fail("请求数据无效"));
            }

            await _apiKeyService.UpdateApiKeyAsync(id, request);
            
            return Ok(ApiResponse<object>.Ok(new { }, "API密钥更新成功"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新API密钥失败，ID: {Id}", id);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 禁用API密钥
    /// </summary>
    /// <param name="id">API密钥ID</param>
    [HttpDelete("api-keys/{id}")]
    public async Task<ActionResult<ApiResponse<object>>> DisableApiKey(int id)
    {
        try
        {
            await _apiKeyService.DisableApiKeyAsync(id);
            
            return Ok(ApiResponse<object>.Ok(new { }, "API密钥已禁用"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "禁用API密钥失败，ID: {Id}", id);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取API密钥权限
    /// </summary>
    /// <param name="id">API密钥ID</param>
    [HttpGet("api-keys/{id}/permissions")]
    public async Task<ActionResult<ApiResponse<object>>> GetApiKeyPermissions(int id)
    {
        try
        {
            var permissions = await _apiKeyService.GetApiKeyPermissionsAsync(id);
            
            var result = new
            {
                ApiKeyId = id,
                Permissions = permissions.Select(p => new
                {
                    p.Id,
                    p.PermissionName,
                    p.EndpointPattern,
                    p.HttpMethods,
                    p.Description,
                    p.IsActive
                }),
                Total = permissions.Count()
            };

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取API密钥权限失败，ID: {Id}", id);
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    /// <summary>
    /// 获取所有可用权限
    /// </summary>
    [HttpGet("permissions")]
    public async Task<ActionResult<ApiResponse<object>>> GetAllPermissions()
    {
        try
        {
            // 需要在IApiKeyService中添加GetAllPermissionsAsync方法
            // 暂时通过Repository直接获取
            var permissions = await GetAllPermissionsFromRepository();

            var result = new
            {
                Permissions = permissions.Select(p => new
                {
                    p.Id,
                    p.PermissionName,
                    p.EndpointPattern,
                    p.HttpMethods,
                    p.Description,
                    p.IsActive
                }),
                Total = permissions.Count(),
                Categories = permissions.GroupBy(p => p.PermissionName.Split('.')[0])
                    .Select(g => new { Category = g.Key, Count = g.Count() })
            };

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取权限列表失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    private async Task<IEnumerable<ApiPermission>> GetAllPermissionsFromRepository()
    {
        // 临时实现：返回硬编码的权限列表
        // 在实际实现中，应该通过IApiKeyService.GetAllPermissionsAsync()获取
        await Task.CompletedTask;

        return new List<ApiPermission>
        {
            new() { Id = 1, PermissionName = "home.nearby_stops", EndpointPattern = "/api/home/<USER>", HttpMethods = "GET", Description = "附近站点查询", IsActive = true },
            new() { Id = 2, PermissionName = "search.all", EndpointPattern = "/api/search/all*", HttpMethods = "GET", Description = "综合搜索", IsActive = true },
            new() { Id = 3, PermissionName = "lines.list", EndpointPattern = "/api/lines*", HttpMethods = "GET", Description = "线路列表", IsActive = true },
            new() { Id = 4, PermissionName = "datasync.gps", EndpointPattern = "/api/datasync/gps*", HttpMethods = "POST", Description = "GPS数据同步", IsActive = true },
            new() { Id = 5, PermissionName = "admin.api_keys", EndpointPattern = "/api/admin/api-keys*", HttpMethods = "GET,POST,PUT,DELETE", Description = "API密钥管理", IsActive = true }
        };
    }

    /// <summary>
    /// 获取API使用统计
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    [HttpGet("stats/usage")]
    public async Task<ActionResult<ApiResponse<object>>> GetUsageStats(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            // 获取基础统计数据
            var allApiKeys = await _apiKeyService.GetApiKeysAsync();
            var activeApiKeys = allApiKeys.Where(k => k.IsActive);

            // 模拟统计数据（在实际实现中应该从数据库查询）
            var totalRequests = await GetTotalRequestsAsync(start, end);
            var topEndpoints = await GetTopEndpointsAsync(start, end);
            var errorStats = await GetErrorStatsAsync(start, end);

            var result = new
            {
                Period = new { StartDate = start, EndDate = end },
                Summary = new
                {
                    TotalRequests = totalRequests,
                    TotalApiKeys = allApiKeys.Count(),
                    ActiveApiKeys = activeApiKeys.Count(),
                    AverageRequestsPerDay = totalRequests / Math.Max(1, (end - start).Days),
                    ErrorRate = ((dynamic)errorStats).ErrorRate
                },
                TopEndpoints = topEndpoints,
                ApiKeyStats = activeApiKeys.Take(10).Select(k => new
                {
                    k.KeyName,
                    k.KeyType,
                    k.UsageCount,
                    k.LastUsedAt,
                    EstimatedDailyRequests = k.UsageCount / Math.Max(1, (DateTime.UtcNow - k.CreatedAt).Days)
                }),
                ErrorStats = errorStats
            };

            return Ok(ApiResponse<object>.Ok(result));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取使用统计失败");
            return StatusCode(500, ApiResponse<object>.Fail("服务器内部错误"));
        }
    }

    private async Task<int> GetTotalRequestsAsync(DateTime startDate, DateTime endDate)
    {
        // 模拟数据，实际应该查询api_access_audit表
        await Task.CompletedTask;
        var days = (endDate - startDate).Days;
        return Math.Max(1000, days * 150 + new Random().Next(0, 500));
    }

    private async Task<object[]> GetTopEndpointsAsync(DateTime startDate, DateTime endDate)
    {
        // 模拟数据，实际应该查询api_access_audit表
        await Task.CompletedTask;
        return new object[]
        {
            new { Endpoint = "/api/home/<USER>", RequestCount = 1250, AvgResponseTime = 45 },
            new { Endpoint = "/api/search/all", RequestCount = 890, AvgResponseTime = 120 },
            new { Endpoint = "/api/lines", RequestCount = 650, AvgResponseTime = 80 },
            new { Endpoint = "/api/datasync/gps", RequestCount = 420, AvgResponseTime = 200 },
            new { Endpoint = "/api/admin/api-keys", RequestCount = 85, AvgResponseTime = 60 }
        };
    }

    private async Task<object> GetErrorStatsAsync(DateTime startDate, DateTime endDate)
    {
        // 模拟数据，实际应该查询api_access_audit表
        await Task.CompletedTask;
        return new
        {
            ErrorRate = 2.5, // 百分比
            TotalErrors = 125,
            ErrorsByType = new[]
            {
                new { StatusCode = 401, Count = 45, Description = "未授权" },
                new { StatusCode = 403, Count = 35, Description = "权限不足" },
                new { StatusCode = 429, Count = 25, Description = "请求频率超限" },
                new { StatusCode = 500, Count = 20, Description = "服务器错误" }
            }
        };
    }

    /// <summary>
    /// 系统健康检查
    /// </summary>
    [HttpGet("health")]
    public ActionResult<ApiResponse<object>> HealthCheck()
    {
        var health = new
        {
            Status = "healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Services = new
            {
                ApiKeyService = "running",
                Database = "connected",
                Cache = "connected"
            }
        };

        return Ok(ApiResponse<object>.Ok(health));
    }
}
