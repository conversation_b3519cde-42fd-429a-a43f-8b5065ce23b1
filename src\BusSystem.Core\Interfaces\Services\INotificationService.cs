using System.Net.WebSockets;

namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// WebSocket通知服务接口
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// 添加WebSocket连接
    /// </summary>
    Task<string> AddConnectionAsync(WebSocket webSocket, string? userId = null);

    /// <summary>
    /// 移除WebSocket连接
    /// </summary>
    Task RemoveConnectionAsync(string connectionId);

    /// <summary>
    /// 订阅线路更新
    /// </summary>
    Task SubscribeToLineAsync(string connectionId, int lineId);

    /// <summary>
    /// 取消订阅线路更新
    /// </summary>
    Task UnsubscribeFromLineAsync(string connectionId, int lineId);

    /// <summary>
    /// 订阅站点更新
    /// </summary>
    Task SubscribeToStopAsync(string connectionId, int stopId);

    /// <summary>
    /// 取消订阅站点更新
    /// </summary>
    Task UnsubscribeFromStopAsync(string connectionId, int stopId);

    /// <summary>
    /// 订阅区域更新
    /// </summary>
    Task SubscribeToAreaAsync(string connectionId, double longitude, double latitude, double radiusMeters);

    /// <summary>
    /// 通知车辆位置更新
    /// </summary>
    Task NotifyVehiclePositionUpdateAsync(int vehicleId, object positionData);

    /// <summary>
    /// 通知到站预测更新
    /// </summary>
    Task NotifyArrivalPredictionUpdateAsync(int stopId, object predictionData);

    /// <summary>
    /// 通知线路状态更新
    /// </summary>
    Task NotifyLineStatusUpdateAsync(int lineId, object statusData);

    /// <summary>
    /// 发送消息到指定连接
    /// </summary>
    Task SendMessageToConnectionAsync(string connectionId, object message);

    /// <summary>
    /// 广播消息到所有连接
    /// </summary>
    Task BroadcastMessageAsync(object message);

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    Task<object> GetConnectionStatsAsync();

    /// <summary>
    /// 清理无效连接
    /// </summary>
    Task CleanupInvalidConnectionsAsync();
}
