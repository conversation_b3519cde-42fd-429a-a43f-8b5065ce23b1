using Microsoft.EntityFrameworkCore;
using Serilog;
using StackExchange.Redis;
using BusSystem.Infrastructure.Data;
using BusSystem.Infrastructure.Services;
using BusSystem.Infrastructure.Repositories;
using BusSystem.Core.Interfaces.Services;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Core.Services;
using BusSystem.Api.Authentication;
using BusSystem.Api.Authorization;
using BusSystem.Api.Middleware;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

// Add services to the container.
builder.Services.AddControllers();

// 配置数据库连接
builder.Services.AddDbContext<BusSystemDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection"),
        npgsqlOptions =>
        {
            npgsqlOptions.UseNetTopologySuite();
            npgsqlOptions.MigrationsAssembly("BusSystem.Api");
        }));

builder.Services.AddDbContext<TimescaleDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("TimescaleConnection"),
        npgsqlOptions => npgsqlOptions.MigrationsAssembly("BusSystem.Api")));

// 配置Redis
builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
{
    var connectionString = builder.Configuration.GetConnectionString("Redis");
    return ConnectionMultiplexer.Connect(connectionString!);
});

builder.Services.AddScoped<IRedisService, RedisService>();

// 注册Repository服务
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped<IBusLineRepository, BusLineRepository>();
builder.Services.AddScoped<IBusStopRepository, BusStopRepository>();
builder.Services.AddScoped<IVehicleRepository, VehicleRepository>();
builder.Services.AddScoped<IVehiclePositionRepository, VehiclePositionRepository>();
builder.Services.AddScoped<IApiKeyRepository, ApiKeyRepository>();

// 注册业务服务
builder.Services.AddScoped<ILineService, LineService>();
builder.Services.AddScoped<IStopService, StopService>();
builder.Services.AddScoped<IRealtimeService, RealtimeService>();
builder.Services.AddScoped<IPredictionService, PredictionService>();
builder.Services.AddScoped<IAggregationService, AggregationService>();
builder.Services.AddSingleton<INotificationService, NotificationService>();
builder.Services.AddScoped<ICoordinateTransformService, CoordinateTransformService>();
builder.Services.AddScoped<IDataSyncService, DataSyncService>();
builder.Services.AddScoped<ISearchService, SearchService>();
builder.Services.AddScoped<IApiKeyService, ApiKeyService>();

// 配置认证和授权
builder.Services.AddAuthentication("ApiKey")
    .AddApiKeyAuthentication();

builder.Services.AddApiAuthorizationPolicies();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "实时公交系统API", Version = "v1" });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "实时公交系统API v1");
    });
}

// 启用WebSocket支持
app.UseWebSockets();

// 使用WebSocket中间件
app.UseMiddleware<BusSystem.Api.Middleware.WebSocketMiddleware>();

// 使用Serilog请求日志
app.UseSerilogRequestLogging();

app.UseCors("AllowAll");

app.UseHttpsRedirection();

// 启用认证和授权
app.UseAuthentication();
// 使用API访问日志中间件（在认证之后，授权之前）
app.UseMiddleware<ApiAccessLoggingMiddleware>();
app.UseAuthorization();

app.MapControllers();

// 健康检查端点
app.MapGet("/health", () => Results.Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow }));

app.Run();
