using Microsoft.EntityFrameworkCore;
using BusSystem.Core.Entities;
using BusSystem.Core.Interfaces.Repositories;
using BusSystem.Infrastructure.Data;

namespace BusSystem.Infrastructure.Repositories;

/// <summary>
/// 线路站点关系Repository实现
/// </summary>
public class LineStopRepository : Repository<LineStop>, ILineStopRepository
{
    public LineStopRepository(BusSystemDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<LineStop>> GetByLineIdAsync(int lineId)
    {
        return await _dbSet
            .Where(x => x.LineId == lineId)
            .Include(x => x.Stop)
            .OrderBy(x => x.SequenceNumber)
            .ToListAsync();
    }

    public async Task<IEnumerable<LineStop>> GetByStopIdAsync(int stopId)
    {
        return await _dbSet
            .Where(x => x.StopId == stopId)
            .Include(x => x.Line)
            .OrderBy(x => x.Line.LineNumber)
            .ToListAsync();
    }

    public async Task<LineStop?> GetByLineAndStopAsync(int lineId, int stopId)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.LineId == lineId && x.StopId == stopId);
    }

    public async Task<int> DeleteByLineIdAsync(int lineId)
    {
        var lineStops = await _dbSet
            .Where(x => x.LineId == lineId)
            .ToListAsync();
            
        if (lineStops.Any())
        {
            _dbSet.RemoveRange(lineStops);
            await _context.SaveChangesAsync();
        }
        
        return lineStops.Count;
    }

    public async Task<int> AddRangeAsync(IEnumerable<LineStop> lineStops)
    {
        var lineStopList = lineStops.ToList();
        if (lineStopList.Any())
        {
            await _dbSet.AddRangeAsync(lineStopList);
            await _context.SaveChangesAsync();
        }
        
        return lineStopList.Count;
    }
}
