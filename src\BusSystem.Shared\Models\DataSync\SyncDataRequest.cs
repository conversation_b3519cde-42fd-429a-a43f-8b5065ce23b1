using System.ComponentModel.DataAnnotations;

namespace BusSystem.Shared.Models.DataSync;

/// <summary>
/// 数据同步请求基类
/// </summary>
public abstract class SyncDataRequest
{
    /// <summary>
    /// 数据源标识
    /// </summary>
    [Required]
    public string DataSource { get; set; } = string.Empty;
    
    /// <summary>
    /// 同步时间戳
    /// </summary>
    [Required]
    public DateTime Timestamp { get; set; }
    
    /// <summary>
    /// 数据版本号
    /// </summary>
    public string? Version { get; set; }
    
    /// <summary>
    /// 批次ID（用于批量同步）
    /// </summary>
    public string? BatchId { get; set; }
}

/// <summary>
/// GPS数据同步请求
/// </summary>
public class GpsDataSyncRequest : SyncDataRequest
{
    /// <summary>
    /// GPS数据列表
    /// </summary>
    [Required]
    public List<GpsDataItem> GpsData { get; set; } = new();
}

/// <summary>
/// GPS数据项
/// </summary>
public class GpsDataItem
{
    /// <summary>
    /// 车辆ID
    /// </summary>
    [Required]
    public string VehicleId { get; set; } = string.Empty;
    
    /// <summary>
    /// 线路ID
    /// </summary>
    public int? LineId { get; set; }
    
    /// <summary>
    /// 经度（WGS84坐标系）
    /// </summary>
    [Required]
    [Range(-180, 180)]
    public double Longitude { get; set; }
    
    /// <summary>
    /// 纬度（WGS84坐标系）
    /// </summary>
    [Required]
    [Range(-90, 90)]
    public double Latitude { get; set; }
    
    /// <summary>
    /// 速度（km/h）
    /// </summary>
    [Range(0, 200)]
    public double Speed { get; set; }
    
    /// <summary>
    /// 方向角（0-360度）
    /// </summary>
    [Range(0, 360)]
    public double Direction { get; set; }
    
    /// <summary>
    /// GPS时间
    /// </summary>
    [Required]
    public DateTime GpsTime { get; set; }
    
    /// <summary>
    /// 车辆状态：1-运营中，2-回库，3-故障，4-离线
    /// </summary>
    [Range(1, 4)]
    public int Status { get; set; } = 1;
    
    /// <summary>
    /// 下一站点ID
    /// </summary>
    public int? NextStopId { get; set; }
    
    /// <summary>
    /// 距离下一站的距离（米）
    /// </summary>
    public double? DistanceToNextStop { get; set; }
}

/// <summary>
/// 线路数据同步请求
/// </summary>
public class LineSyncRequest : SyncDataRequest
{
    /// <summary>
    /// 线路数据列表
    /// </summary>
    [Required]
    public List<LineSyncItem> Lines { get; set; } = new();
}

/// <summary>
/// 站点数据同步请求
/// </summary>
public class StopSyncRequest : SyncDataRequest
{
    /// <summary>
    /// 站点数据列表
    /// </summary>
    [Required]
    public List<StopSyncItem> Stops { get; set; } = new();
}

/// <summary>
/// 车辆数据同步请求
/// </summary>
public class VehicleSyncRequest : SyncDataRequest
{
    /// <summary>
    /// 车辆数据列表
    /// </summary>
    [Required]
    public List<VehicleSyncItem> Vehicles { get; set; } = new();
}

/// <summary>
/// 线路站点关系同步请求
/// </summary>
public class LineStopsSyncRequest : SyncDataRequest
{
    /// <summary>
    /// 外部系统线路ID
    /// </summary>
    [Required]
    public string ExternalLineId { get; set; } = string.Empty;

    /// <summary>
    /// 线路站点关系列表（按顺序排列）
    /// </summary>
    [Required]
    public List<LineStopSyncItem> Stops { get; set; } = new();
}

/// <summary>
/// 线路同步数据项
/// </summary>
public class LineSyncItem
{
    /// <summary>
    /// 外部线路ID
    /// </summary>
    [Required]
    public string ExternalLineId { get; set; } = string.Empty;

    /// <summary>
    /// 线路编号
    /// </summary>
    [Required]
    public string LineNumber { get; set; } = string.Empty;

    /// <summary>
    /// 线路名称
    /// </summary>
    [Required]
    public string LineName { get; set; } = string.Empty;

    /// <summary>
    /// 起点站名称
    /// </summary>
    public string? StartStopName { get; set; }

    /// <summary>
    /// 终点站名称
    /// </summary>
    public string? EndStopName { get; set; }

    /// <summary>
    /// 运营时间
    /// </summary>
    public string? OperationTime { get; set; }

    /// <summary>
    /// 方向：0-上行，1-下行
    /// </summary>
    public int Direction { get; set; } = 0;

    /// <summary>
    /// 状态：1-正常，2-停运，3-维护
    /// </summary>
    public int Status { get; set; } = 1;

    /// <summary>
    /// 线路站点序列（按顺序排列）
    /// </summary>
    public List<LineStopSyncItem> Stops { get; set; } = new();
}

/// <summary>
/// 线路站点关系同步数据项
/// </summary>
public class LineStopSyncItem
{
    /// <summary>
    /// 外部系统站点ID
    /// </summary>
    [Required]
    public string ExternalStopId { get; set; } = string.Empty;

    /// <summary>
    /// 站点在线路中的序号（从1开始）
    /// </summary>
    [Required]
    [Range(1, 999)]
    public int SequenceNumber { get; set; }

    /// <summary>
    /// 距离起点的距离（公里）
    /// </summary>
    public decimal? DistanceFromStart { get; set; }

    /// <summary>
    /// 预计行驶时间（分钟）
    /// </summary>
    public int? EstimatedTime { get; set; }

    /// <summary>
    /// 是否为重点站
    /// </summary>
    public bool IsKeyStop { get; set; } = false;

    /// <summary>
    /// 状态：1-正常，0-停用
    /// </summary>
    public int Status { get; set; } = 1;
}

/// <summary>
/// 站点同步数据项
/// </summary>
public class StopSyncItem
{
    /// <summary>
    /// 外部站点ID
    /// </summary>
    [Required]
    public string ExternalStopId { get; set; } = string.Empty;

    /// <summary>
    /// 站点名称
    /// </summary>
    [Required]
    public string StopName { get; set; } = string.Empty;

    /// <summary>
    /// 站点编码
    /// </summary>
    public string? StopCode { get; set; }

    /// <summary>
    /// 经度（WGS84坐标系）
    /// </summary>
    [Required]
    [Range(-180, 180)]
    public double Longitude { get; set; }

    /// <summary>
    /// 纬度（WGS84坐标系）
    /// </summary>
    [Required]
    [Range(-90, 90)]
    public double Latitude { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 所属区域
    /// </summary>
    public string? District { get; set; }

    /// <summary>
    /// 状态：1-正常，2-停用，3-维护
    /// </summary>
    public int Status { get; set; } = 1;
}

/// <summary>
/// 车辆同步数据项
/// </summary>
public class VehicleSyncItem
{
    /// <summary>
    /// 外部车辆ID
    /// </summary>
    [Required]
    public string ExternalVehicleId { get; set; } = string.Empty;

    /// <summary>
    /// 车牌号
    /// </summary>
    [Required]
    public string PlateNumber { get; set; } = string.Empty;

    /// <summary>
    /// 外部线路ID
    /// </summary>
    [Required]
    public string ExternalLineId { get; set; } = string.Empty;

    /// <summary>
    /// 车辆类型
    /// </summary>
    public string? VehicleType { get; set; }

    /// <summary>
    /// 载客量
    /// </summary>
    public int? Capacity { get; set; }

    /// <summary>
    /// 状态：1-运营中，2-回库，3-维护，4-停用
    /// </summary>
    public int Status { get; set; } = 1;
}

/// <summary>
/// 数据同步响应
/// </summary>
public class SyncDataResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 处理的数据条数
    /// </summary>
    public int ProcessedCount { get; set; }
    
    /// <summary>
    /// 失败的数据条数
    /// </summary>
    public int FailedCount { get; set; }
    
    /// <summary>
    /// 错误详情
    /// </summary>
    public List<string> Errors { get; set; } = new();
    
    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 服务器时间戳
    /// </summary>
    public DateTime ServerTimestamp { get; set; } = DateTime.UtcNow;
}
