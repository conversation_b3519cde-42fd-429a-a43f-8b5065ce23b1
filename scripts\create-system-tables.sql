-- =============================================
-- 系统数据表创建脚本
-- 创建时间: 2025-08-22
-- 说明: 创建系统配置、日志、监控等系统管理表
-- =============================================

-- 1. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- 配置类型：string, number, boolean, json
    description TEXT,
    category VARCHAR(50) DEFAULT 'general', -- 配置分类
    is_sensitive BOOLEAN DEFAULT false, -- 是否敏感信息
    is_readonly BOOLEAN DEFAULT false, -- 是否只读
    validation_rule TEXT, -- 验证规则（正则表达式或JSON Schema）
    default_value TEXT, -- 默认值
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) -- 更新人
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_configs_category 
ON system_configs (category, config_key);

-- 插入默认配置
INSERT INTO system_configs (config_key, config_value, config_type, description, category) VALUES
('system.name', '实时公交系统', 'string', '系统名称', 'general'),
('system.version', '1.0.0', 'string', '系统版本', 'general'),
('api.rate_limit.default', '1000', 'number', '默认API速率限制（每小时）', 'api'),
('api.rate_limit.sync', '10000', 'number', '数据同步API速率限制（每小时）', 'api'),
('cache.ttl.realtime', '30', 'number', '实时数据缓存TTL（秒）', 'cache'),
('cache.ttl.static', '3600', 'number', '静态数据缓存TTL（秒）', 'cache'),
('prediction.max_distance', '5000', 'number', '到站预测最大距离（米）', 'prediction'),
('prediction.confidence_threshold', '0.7', 'number', '预测置信度阈值', 'prediction'),
('search.max_results', '50', 'number', '搜索结果最大数量', 'search'),
('notification.websocket.max_connections', '10000', 'number', 'WebSocket最大连接数', 'notification')
ON CONFLICT (config_key) DO NOTHING;

-- 2. 数据同步日志表
CREATE TABLE IF NOT EXISTS data_sync_logs (
    id BIGSERIAL PRIMARY KEY,
    data_source VARCHAR(100) NOT NULL,
    sync_type VARCHAR(50) NOT NULL, -- 同步类型：gps, base_data, batch
    batch_id VARCHAR(100), -- 批次ID
    operation VARCHAR(20) NOT NULL, -- 操作：create, update, delete, sync
    target_table VARCHAR(100), -- 目标表名
    record_count INTEGER DEFAULT 0, -- 记录数量
    success_count INTEGER DEFAULT 0, -- 成功数量
    failed_count INTEGER DEFAULT 0, -- 失败数量
    processing_time_ms BIGINT, -- 处理时间（毫秒）
    status VARCHAR(20) DEFAULT 'processing', -- 状态：processing, success, failed, partial
    error_message TEXT, -- 错误信息
    error_details JSONB, -- 错误详情（JSON格式）
    request_data JSONB, -- 请求数据（JSON格式）
    response_data JSONB, -- 响应数据（JSON格式）
    client_ip INET, -- 客户端IP
    user_agent TEXT, -- 用户代理
    api_key_hash VARCHAR(64), -- API密钥哈希（不存储原始密钥）
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_sync_logs_source_time 
ON data_sync_logs (data_source, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_data_sync_logs_status 
ON data_sync_logs (status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_data_sync_logs_batch 
ON data_sync_logs (batch_id) WHERE batch_id IS NOT NULL;

-- 3. API访问日志表
CREATE TABLE IF NOT EXISTS api_access_logs (
    id BIGSERIAL PRIMARY KEY,
    request_id VARCHAR(100), -- 请求ID（用于追踪）
    method VARCHAR(10) NOT NULL, -- HTTP方法
    path VARCHAR(500) NOT NULL, -- 请求路径
    query_string TEXT, -- 查询字符串
    status_code INTEGER NOT NULL, -- HTTP状态码
    response_time_ms INTEGER, -- 响应时间（毫秒）
    request_size BIGINT, -- 请求大小（字节）
    response_size BIGINT, -- 响应大小（字节）
    client_ip INET, -- 客户端IP
    user_agent TEXT, -- 用户代理
    referer TEXT, -- 来源页面
    api_key_hash VARCHAR(64), -- API密钥哈希
    user_id VARCHAR(100), -- 用户ID
    error_message TEXT, -- 错误信息
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_api_access_logs_time 
ON api_access_logs (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_api_access_logs_path_time 
ON api_access_logs (path, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_api_access_logs_status 
ON api_access_logs (status_code, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_api_access_logs_ip 
ON api_access_logs (client_ip, created_at DESC);

-- 4. 系统错误日志表
CREATE TABLE IF NOT EXISTS system_error_logs (
    id BIGSERIAL PRIMARY KEY,
    error_id VARCHAR(100), -- 错误ID（用于去重和追踪）
    error_type VARCHAR(100) NOT NULL, -- 错误类型
    error_level VARCHAR(20) DEFAULT 'error', -- 错误级别：debug, info, warning, error, critical
    error_message TEXT NOT NULL, -- 错误消息
    error_details JSONB, -- 错误详情（JSON格式）
    stack_trace TEXT, -- 堆栈跟踪
    source_file VARCHAR(500), -- 源文件
    source_line INTEGER, -- 源代码行号
    method_name VARCHAR(200), -- 方法名
    request_id VARCHAR(100), -- 关联的请求ID
    user_id VARCHAR(100), -- 关联的用户ID
    context_data JSONB, -- 上下文数据
    occurrence_count INTEGER DEFAULT 1, -- 发生次数
    first_occurred_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_occurred_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    is_resolved BOOLEAN DEFAULT false, -- 是否已解决
    resolved_at TIMESTAMPTZ, -- 解决时间
    resolved_by VARCHAR(100), -- 解决人
    resolution_notes TEXT, -- 解决说明
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_error_logs_type_level 
ON system_error_logs (error_type, error_level, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_system_error_logs_unresolved 
ON system_error_logs (is_resolved, error_level, created_at DESC) 
WHERE is_resolved = false;

CREATE INDEX IF NOT EXISTS idx_system_error_logs_error_id 
ON system_error_logs (error_id);

-- 5. 系统性能监控表
CREATE TABLE IF NOT EXISTS system_performance_metrics (
    id BIGSERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20), -- 单位：ms, %, MB, count等
    metric_type VARCHAR(50) DEFAULT 'gauge', -- 指标类型：gauge, counter, histogram
    tags JSONB, -- 标签（JSON格式）
    host_name VARCHAR(100), -- 主机名
    service_name VARCHAR(100), -- 服务名
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_performance_metrics_name_time 
ON system_performance_metrics (metric_name, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_system_performance_metrics_service 
ON system_performance_metrics (service_name, metric_name, created_at DESC);

-- 6. 数据源配置表
CREATE TABLE IF NOT EXISTS data_source_configs (
    id SERIAL PRIMARY KEY,
    source_name VARCHAR(100) NOT NULL UNIQUE,
    source_type VARCHAR(50) NOT NULL, -- 数据源类型：platform_a, hisense, test
    api_endpoint VARCHAR(500), -- API端点
    api_key_hash VARCHAR(64), -- API密钥哈希
    is_active BOOLEAN DEFAULT true, -- 是否激活
    rate_limit INTEGER DEFAULT 1000, -- 速率限制（每小时）
    timeout_seconds INTEGER DEFAULT 30, -- 超时时间（秒）
    retry_count INTEGER DEFAULT 3, -- 重试次数
    config_data JSONB, -- 配置数据（JSON格式）
    last_sync_at TIMESTAMPTZ, -- 最后同步时间
    sync_status VARCHAR(20) DEFAULT 'unknown', -- 同步状态
    error_count INTEGER DEFAULT 0, -- 错误计数
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_source_configs_active 
ON data_source_configs (is_active, source_type);

-- 7. 系统任务调度表
CREATE TABLE IF NOT EXISTS system_scheduled_tasks (
    id SERIAL PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL UNIQUE,
    task_type VARCHAR(50) NOT NULL, -- 任务类型：cleanup, sync, backup, report
    cron_expression VARCHAR(100), -- Cron表达式
    is_enabled BOOLEAN DEFAULT true, -- 是否启用
    max_runtime_minutes INTEGER DEFAULT 60, -- 最大运行时间（分钟）
    retry_count INTEGER DEFAULT 0, -- 重试次数
    last_run_at TIMESTAMPTZ, -- 最后运行时间
    next_run_at TIMESTAMPTZ, -- 下次运行时间
    last_status VARCHAR(20), -- 最后状态：success, failed, running, timeout
    last_error TEXT, -- 最后错误信息
    run_count INTEGER DEFAULT 0, -- 运行次数
    success_count INTEGER DEFAULT 0, -- 成功次数
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_scheduled_tasks_enabled 
ON system_scheduled_tasks (is_enabled, next_run_at);

-- 插入默认任务
INSERT INTO system_scheduled_tasks (task_name, task_type, cron_expression, is_enabled, max_runtime_minutes) VALUES
('cleanup_expired_gps_data', 'cleanup', '0 2 * * *', true, 30),
('cleanup_old_logs', 'cleanup', '0 3 * * *', true, 60),
('generate_daily_report', 'report', '0 6 * * *', true, 15),
('sync_health_check', 'sync', '*/5 * * * *', true, 5)
ON CONFLICT (task_name) DO NOTHING;

-- 添加表注释
COMMENT ON TABLE system_configs IS '系统配置表，存储系统运行参数';
COMMENT ON TABLE data_sync_logs IS '数据同步日志表，记录所有数据同步操作';
COMMENT ON TABLE api_access_logs IS 'API访问日志表，记录所有API请求';
COMMENT ON TABLE system_error_logs IS '系统错误日志表，记录系统运行错误';
COMMENT ON TABLE system_performance_metrics IS '系统性能监控表，记录性能指标';
COMMENT ON TABLE data_source_configs IS '数据源配置表，管理外部数据源';
COMMENT ON TABLE system_scheduled_tasks IS '系统任务调度表，管理定时任务';

-- 创建更新时间触发器
CREATE TRIGGER update_system_configs_updated_at 
BEFORE UPDATE ON system_configs 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_source_configs_updated_at 
BEFORE UPDATE ON data_source_configs 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_scheduled_tasks_updated_at 
BEFORE UPDATE ON system_scheduled_tasks 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
