# 实时公交系统开发备忘录

## 项目概述
实时公交系统是一个包含前端H5应用、后端API服务和运营后台的完整解决方案，为用户提供实时公交信息查询服务。

## 项目结构
```
实时公交/
├── docs/                    # 文档目录
│   ├── 业务需求.md           # 原始业务需求
│   ├── 详细需求设计.md       # 详细需求设计文档
│   ├── 架构设计.md           # 系统架构设计
│   ├── UI设计规范.md         # UI设计规范和交互规范
│   ├── 后端架构设计.md       # 后端分层架构设计（独立同步服务模式）
│   ├── 数据同步架构设计.md   # 独立同步服务架构详细设计
│   ├── 开发实施计划.md       # 详细的开发实施计划和时间安排
│   ├── 开发启动指南.md       # 新会话开发启动指南
│   ├── 数据库设计.md         # 数据库设计文档
│   ├── 接口设计.md           # API接口设计文档
│   ├── 核心服务设计.md       # 核心业务服务设计
│   └── 部署文档.md           # 部署相关文档
├── scripts/                 # 脚本目录
│   ├── docker-compose.dev.yml # 开发环境Docker配置
│   ├── init-db.sql          # PostgreSQL初始化脚本
│   └── init-timescaledb.sql # TimescaleDB初始化脚本
├── frontend/                # 前端H5应用
├── backend/                 # 后端API服务
├── admin/                   # 运营后台
└── scripts/                 # 部署和工具脚本
```

## 核心功能模块
1. **H5前端应用**
   - 实时公交主页面（附近站点信息）
   - 线路详情页面
   - 站点信息页面
   - 搜索页面

2. **后端API服务**
   - 实时公交数据接口
   - 地图服务集成（高德地图）
   - 数据缓存和处理

3. **运营后台**
   - 数据管理
   - 系统监控
   - 配置管理

## 技术栈确认
- **前端**: Vue.js 3 + Vant UI + TypeScript
- **后端**: .NET 8 + ASP.NET Core + C#
- **架构**: 独立同步服务 + 统一接口规范
- **数据库**:
  - PostgreSQL 15 + PostGIS (基础数据，地理空间查询)
  - TimescaleDB (GPS轨迹等时序数据)
  - Redis Geo (高频地理位置查询)
  - Redis (通用缓存、会话管理)
- **地图服务**: 高德地图API
- **部署**: Docker + Kubernetes + Nginx

## 开发阶段
- [x] 需求分析
- [x] 详细需求设计
- [x] 系统架构设计
- [x] 数据库设计
- [x] API接口设计
- [x] 核心服务设计
- [x] 数据同步架构设计（独立同步服务模式）
- [x] 后端架构设计（独立同步服务模式）
- [x] 开发实施计划制定
- [x] 环境搭建和基础设施
- [x] .NET 8项目结构创建
- [x] 数据库初始化
- [x] 主系统后端开发（第一阶段）
- [x] 数据同步服务开发
- [ ] 预测服务完整实现（分析完成，待实施）
- [ ] 前端H5应用开发
- [ ] 系统集成测试
- [ ] 部署上线

## 重要说明
- 项目采用前后端分离架构
- 所有接口遵循RESTful设计规范
- 前端采用响应式设计，适配移动端
- 实时数据通过WebSocket或轮询方式获取
- 地图功能集成高德地图API

## 📚 文档目录

### 设计文档
- [开发实施计划](docs/开发实施计划.md) - 详细的开发计划和时间安排
- [系统架构设计](docs/系统架构设计.md) - 整体架构和技术选型
- [数据库设计](docs/数据库设计.md) - 数据库表结构和关系设计
- [API接口设计](docs/API接口设计.md) - RESTful API接口规范

### 技术文档
- [坐标系和站点切换功能完善](docs/坐标系和站点切换功能完善.md) - 坐标转换和站点切换功能说明
- [数据同步接口规范](docs/数据同步接口规范.md) - 数据同步API详细规范和对接指南
- [API认证架构实施指南](docs/API认证架构实施指南.md) - 统一API认证架构设计和实施方案
- [API认证测试报告](docs/API认证测试报告.md) - API认证系统功能测试结果和验证报告
- [API认证开发文档](docs/API认证开发文档.md) - API认证系统技术实现详解和WEB管理后台开发指南
- [API认证系统完善报告](docs/API认证系统完善报告.md) - API认证系统安全加固和功能完善的详细报告
- [架构优化建议报告](docs/架构优化建议报告.md) - 针对Class1.cs命名、Repository设计、接口架构等问题的优化方案
- [架构调整完成报告](docs/架构调整完成报告.md) - 架构调整的执行情况和后续工作计划
- [架构分层设计指南](docs/架构分层设计指南.md) - Clean Architecture分层原则和服务分布指导
- [预测服务完整设计方案](docs/预测服务完整设计方案.md) - 预测服务的完整架构设计和实施方案
- [预测算法核心实现](docs/预测算法核心实现.md) - 预测算法的详细实现代码和优化策略
- [线路路径生成方案](docs/线路路径生成方案.md) - 基于GPS轨迹生成线路路径的完整解决方案
- [路径生成核心算法实现](docs/路径生成核心算法实现.md) - 路径生成算法的详细实现代码
- [路径生成与预测集成完整方案](docs/路径生成与预测集成完整方案.md) - 从数据缺失到预测精度提升的端到端解决方案
- [集成方案实施指导](docs/集成方案实施指导.md) - 详细的实施步骤、测试验证和问题排查指南

### 部署文档
- [数据库部署指南](docs/数据库部署指南.md) - PostgreSQL、TimescaleDB完整部署指南

### 进度记录
- [开发进度记录](docs/开发进度记录.md) - 详细的开发进度和成果记录

## 文档更新记录
- 2025-08-19: 创建项目备忘录，完成基础项目结构规划
- 2025-08-19: 完成详细需求设计文档，包含完整的功能需求分析
- 2025-08-19: 完成系统架构设计文档，确定技术栈和系统架构
- 2025-08-19: 根据参考UI图片完善详细需求设计，增加UI设计规范文档
- 2025-08-19: 完成系统详细设计，包括数据库设计、API接口设计、核心服务设计和数据同步设计
- 2025-08-19: 优化技术选型，从MySQL改为PostgreSQL+PostGIS+TimescaleDB+Redis Geo架构
- 2025-08-19: 更新所有相关文档以反映新的技术架构，创建技术架构优化总结文档
- 2025-08-19: 修正数据库设计文档结构，明确PostgreSQL、TimescaleDB、Redis Geo的职责分工
- 2025-08-19: 重新设计后端架构，采用适配器模式实现调度平台解耦，支持灵活切换数据源
- 2025-08-19: 完成后端技术选型分析，补充.NET方案对比，推荐.NET 8+微服务架构
- 2025-08-19: 重新设计数据同步架构，采用独立同步服务+统一接口规范，实现完全解耦
- 2025-08-19: 完成开发实施计划，制定详细的开发阶段和时间安排，删除过时的数据同步设计文档
- 2025-08-19: 规范化文档命名，将"数据同步架构重新设计.md"更名为"数据同步架构设计.md"
- 2025-08-19: 清理临时性文档，删除技术选型对比和架构优化总结等过程性文档，保留最终确定的设计文档
- 2025-08-19: 创建开发启动指南和环境配置文件，为新会话的.NET后端开发做好准备
- 2025-08-19: 完成后端技术选型分析，推荐Go语言+微服务架构，提供多种备选方案
- 2025-08-19: 创建.NET 8项目结构，包含API层、业务层、基础设施层等完整架构，配置PostgreSQL+Redis连接
- 2025-08-19: 完成数据库初始化、Repository模式实现、核心业务服务开发、API控制器实现，基础后端架构搭建完成
- 2025-08-20: 完成实时数据服务开发，包括RealtimeService、PredictionService、车辆位置管理、到站预测算法等功能
- 2025-08-20: 完成WebSocket实时推送服务开发，包括连接管理、订阅系统、实时数据推送、H5首页聚合接口等功能
- 2025-08-22: 完善坐标系转换功能，在所有返回坐标的接口中集成坐标系参数支持，实现WGS84、GCJ02、BD09三种坐标系的灵活转换
- 2025-08-22: 完善站点切换功能，添加同名站点查询、可切换站点列表接口，支持前端在站点信息页面实现站点切换操作
- 2025-08-22: 独立单站点到站预测功能，提供专门的单站点预测接口，优化站点信息页面的数据获取效率
- 2025-08-22: 完成数据同步API开发，包括GPS数据同步、基础数据同步、批量同步、权限验证等完整功能
- 2025-08-22: 完成搜索服务开发，包括智能线路搜索、站点搜索、综合搜索、搜索建议、热门关键词等功能
- 2025-08-22: 完善数据库设计，添加业务数据表（用户数据、搜索记录、反馈等）和系统数据表（配置、日志、监控等）
- 2025-08-22: 优化TimescaleDB配置，延长数据保留策略，完善压缩策略，提升时序数据处理性能
- 2025-08-22: 创建详细的数据同步接口规范文档，包含完整的API说明、对接流程、安全规范和技术支持信息
- 2025-08-22: 设计完整的API认证架构，包括统一的API Key认证、基于权限的访问控制、速率限制等安全机制
- 2025-08-22: 实施API认证架构，完成核心服务、数据访问层、认证处理器、授权策略等关键组件的开发
- 2025-08-25: 完成API认证系统功能测试，验证统一认证、权限控制、访问隔离等核心功能，系统可正常投入使用
- 2025-08-25: 清理临时文件，恢复正式ApiKeyRepository实现，整理完整的API认证开发文档和WEB管理后台开发指南
- 2025-08-25: 按优先级完善API认证系统，消除安全隐患，实现功能完整性，系统达到生产就绪状态
- 2025-08-25: 进行架构调整，解决Class1.cs命名问题，规范接口组织结构，创建EF Core实体和混合Repository实现
- 2025-08-25: 完成接口定义修复，从59个编译错误减少到0个，实现100%编译成功，架构质量达到企业级标准
- 2025-08-25: 优化编译警告，修复ISystemClock过时、Header添加、异步方法、null引用等问题，实现完全干净的编译
- 2025-08-25: 重命名HomeController为AggregationController，更准确反映聚合查询功能，API路径从/api/home/<USER>/api/aggregation/*
- 2025-08-25: 同步更新核心设计文档，确保与项目实际内容一致：更新接口设计.md的控制器路径、核心服务设计.md添加AggregationService、架构设计.md更新技术栈为.NET 8
- 2025-08-25: 优化数据同步接口设计，删除冗余的BatchSyncGpsDataAsync接口，SyncGpsDataAsync本身已支持批量处理（最多1000条），简化接口设计，提升可维护性
- 2025-08-25: 完整实施数据同步接口优化（方案1）：
  * 删除无用方法：GetSyncStatusAsync、CleanupExpiredSyncRecordsAsync
  * 重构基础数据同步：替换统一接口为类型专用接口（SyncLineDataAsync、SyncStopDataAsync、SyncVehicleDataAsync）
  * 实体扩展：为BusLine、BusStop、Vehicle添加ExternalId字段，支持外部系统数据同步
  * Repository增强：添加GetByExternalIdAsync方法，支持根据外部ID查找实体
  * 自动判断操作：同步接口自动处理新增/更新，无需手动指定操作类型
  * 接口优化：每种数据类型有专门的接口和请求模型，字段定义明确
  * 文档更新：更新数据同步接口规范文档，提供详细的字段说明和示例
  * 线路站点关系同步：在线路同步中包含站点序列，自动处理线路站点关系的创建和更新
  * 专用线路站点关系接口：新增/api/datasync/line-stops接口，支持线路站点关系的增量更新和灵活调整
  * 线路方向切换功能：新增/api/aggregation/line/{lineId}/alternatives接口，支持同线路编号不同方向的切换，与站点切换功能保持一致
- 2025-08-27: 完成预测服务完整设计方案分析，识别当前实现的不足，设计分层预测架构，支持两种调度系统数据类型（仅GPS vs GPS+站点信息），包含位置推断、路径匹配、动态速度预测等核心算法
- 2025-08-27: 设计基于GPS轨迹的线路路径生成方案，解决线路路径数据缺失问题，通过算法自动生成+人工优化的方式为所有线路生成高质量路径数据，大幅提升预测精度
- 2025-08-27: 制定路径生成与预测服务完整集成方案，包含四个实施阶段（数据基础建设、预测服务升级、智能化优化、运维体系建设），预期将预测误差从5-8分钟降低到2-3分钟，提升精度60%

