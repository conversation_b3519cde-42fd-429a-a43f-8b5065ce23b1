# 路径生成核心算法实现

## 1. 核心服务接口定义

```csharp
namespace BusSystem.Core.Interfaces.Services;

/// <summary>
/// 线路路径生成服务接口
/// </summary>
public interface IRoutePathGenerationService
{
    /// <summary>
    /// 为指定线路生成路径
    /// </summary>
    Task<RouteGenerationResult> GenerateRoutePathAsync(int lineId, RouteGenerationOptions options);
    
    /// <summary>
    /// 批量生成所有线路的路径
    /// </summary>
    Task<List<RouteGenerationResult>> GenerateAllRoutePathsAsync();
    
    /// <summary>
    /// 评估路径质量
    /// </summary>
    Task<RouteQualityScore> AssessRouteQualityAsync(int lineId);
    
    /// <summary>
    /// 更新线路路径
    /// </summary>
    Task UpdateRoutePathAsync(int lineId, LineString newPath, string updatedBy, string reason);
    
    /// <summary>
    /// 获取路径生成历史
    /// </summary>
    Task<List<RoutePathHistory>> GetPathGenerationHistoryAsync(int lineId);
}

/// <summary>
/// 路径生成选项
/// </summary>
public class RouteGenerationOptions
{
    /// <summary>
    /// 数据开始日期
    /// </summary>
    public DateTime StartDate { get; set; } = DateTime.UtcNow.AddDays(-30);
    
    /// <summary>
    /// 数据结束日期
    /// </summary>
    public DateTime EndDate { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 方向：0-上行，1-下行
    /// </summary>
    public int Direction { get; set; }
    
    /// <summary>
    /// 分段长度（米）
    /// </summary>
    public double SegmentLengthMeters { get; set; } = 100;
    
    /// <summary>
    /// 聚类半径（米）
    /// </summary>
    public double ClusteringRadiusMeters { get; set; } = 20;
    
    /// <summary>
    /// 最小轨迹长度（公里）
    /// </summary>
    public double MinTrajectoryLengthKm { get; set; } = 2;
    
    /// <summary>
    /// 最大合理速度（公里/小时）
    /// </summary>
    public double MaxSpeedKmh { get; set; } = 80;
}

/// <summary>
/// 路径生成结果
/// </summary>
public class RouteGenerationResult
{
    public int LineId { get; set; }
    public int Direction { get; set; }
    public LineString? GeneratedPath { get; set; }
    public RouteQualityScore QualityScore { get; set; } = new();
    public int GpsPointsUsed { get; set; }
    public int TrajectoriesUsed { get; set; }
    public DateTime GeneratedAt { get; set; }
    public List<string> Warnings { get; set; } = new();
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 路径质量评分
/// </summary>
public class RouteQualityScore
{
    /// <summary>
    /// 站点覆盖度 (0-100)
    /// </summary>
    public double StopCoverage { get; set; }
    
    /// <summary>
    /// 路径连续性 (0-100)
    /// </summary>
    public double PathContinuity { get; set; }
    
    /// <summary>
    /// 数据支撑度 (0-100)
    /// </summary>
    public double DataSupport { get; set; }
    
    /// <summary>
    /// 路径平滑度 (0-100)
    /// </summary>
    public double PathSmoothness { get; set; }
    
    /// <summary>
    /// 综合评分 (0-100)
    /// </summary>
    public double OverallScore { get; set; }
    
    /// <summary>
    /// 质量等级
    /// </summary>
    public string QualityLevel => OverallScore switch
    {
        >= 90 => "优秀",
        >= 80 => "良好",
        >= 70 => "一般",
        >= 60 => "较差",
        _ => "很差"
    };
}
```

## 2. 核心算法实现

```csharp
namespace BusSystem.Core.Services;

/// <summary>
/// 线路路径生成服务实现
/// </summary>
public class RoutePathGenerationService : IRoutePathGenerationService
{
    private readonly IVehiclePositionRepository _positionRepository;
    private readonly IBusLineRepository _lineRepository;
    private readonly IBusStopRepository _stopRepository;
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILogger<RoutePathGenerationService> _logger;

    public async Task<RouteGenerationResult> GenerateRoutePathAsync(int lineId, RouteGenerationOptions options)
    {
        var result = new RouteGenerationResult
        {
            LineId = lineId,
            Direction = options.Direction,
            GeneratedAt = DateTime.UtcNow
        };

        try
        {
            _logger.LogInformation("开始为线路 {LineId} 方向 {Direction} 生成路径", lineId, options.Direction);

            // 1. 获取线路的所有车辆轨迹
            var trajectories = await GetLineTrajectories(lineId, options);
            if (!trajectories.Any())
            {
                result.ErrorMessage = "没有找到足够的GPS轨迹数据";
                return result;
            }

            result.TrajectoriesUsed = trajectories.Count;
            result.GpsPointsUsed = trajectories.Sum(t => t.Count);

            // 2. 轨迹预处理和清洗
            var cleanedTrajectories = CleanTrajectories(trajectories, options);
            if (!cleanedTrajectories.Any())
            {
                result.ErrorMessage = "清洗后没有有效的轨迹数据";
                return result;
            }

            // 3. 轨迹聚合生成标准路径
            var standardPath = await AggregateTrajectories(cleanedTrajectories, options);
            if (standardPath == null || standardPath.Coordinates.Length < 2)
            {
                result.ErrorMessage = "无法生成有效的路径";
                return result;
            }

            // 4. 路径平滑和优化
            var smoothedPath = SmoothPath(standardPath);

            // 5. 与站点位置对齐
            var alignedPath = await AlignWithStops(smoothedPath, lineId, options.Direction);

            result.GeneratedPath = alignedPath;
            result.IsSuccessful = true;

            // 6. 评估路径质量
            result.QualityScore = await AssessRouteQuality(alignedPath, lineId, options.Direction);

            _logger.LogInformation("线路 {LineId} 路径生成完成，质量评分: {Score}", 
                lineId, result.QualityScore.OverallScore);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成线路 {LineId} 路径失败", lineId);
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    /// <summary>
    /// 获取线路轨迹数据
    /// </summary>
    private async Task<List<List<GpsPoint>>> GetLineTrajectories(int lineId, RouteGenerationOptions options)
    {
        // 获取线路上的所有车辆
        var vehicles = await _vehicleRepository.GetByLineIdAsync(lineId);
        var trajectories = new List<List<GpsPoint>>();

        foreach (var vehicle in vehicles)
        {
            // 获取车辆在指定时间范围内的轨迹
            var positions = await _positionRepository.GetVehicleTrajectoryAsync(
                vehicle.Id, options.StartDate, options.EndDate);

            if (positions.Any())
            {
                var trajectory = positions
                    .Where(p => IsValidGpsPoint(p, options))
                    .Select(p => new GpsPoint
                    {
                        Latitude = p.Latitude,
                        Longitude = p.Longitude,
                        Timestamp = p.Timestamp,
                        Speed = p.Speed,
                        Direction = p.Direction
                    })
                    .OrderBy(p => p.Timestamp)
                    .ToList();

                if (trajectory.Count >= 10) // 至少10个GPS点
                {
                    trajectories.Add(trajectory);
                }
            }
        }

        return trajectories;
    }

    /// <summary>
    /// 验证GPS点的有效性
    /// </summary>
    private bool IsValidGpsPoint(VehiclePosition position, RouteGenerationOptions options)
    {
        // 检查速度是否合理
        if (position.Speed.HasValue && position.Speed > options.MaxSpeedKmh)
            return false;

        // 检查坐标是否在合理范围内
        if (position.Latitude < -90 || position.Latitude > 90)
            return false;
        if (position.Longitude < -180 || position.Longitude > 180)
            return false;

        return true;
    }

    /// <summary>
    /// 轨迹清洗
    /// </summary>
    private List<List<GpsPoint>> CleanTrajectories(List<List<GpsPoint>> trajectories, RouteGenerationOptions options)
    {
        var cleanedTrajectories = new List<List<GpsPoint>>();

        foreach (var trajectory in trajectories)
        {
            var cleaned = new List<GpsPoint>();
            GpsPoint? lastPoint = null;

            foreach (var point in trajectory)
            {
                if (lastPoint != null)
                {
                    // 计算与上一个点的距离和时间间隔
                    var distance = CalculateDistance(lastPoint.Latitude, lastPoint.Longitude,
                        point.Latitude, point.Longitude);
                    var timeSpan = (point.Timestamp - lastPoint.Timestamp).TotalSeconds;

                    // 过滤异常跳跃的点
                    if (timeSpan > 0)
                    {
                        var speed = (distance / 1000) / (timeSpan / 3600); // km/h
                        if (speed <= options.MaxSpeedKmh)
                        {
                            cleaned.Add(point);
                        }
                    }
                }
                else
                {
                    cleaned.Add(point);
                }

                lastPoint = point;
            }

            // 只保留足够长的轨迹
            var totalDistance = CalculateTrajectoryDistance(cleaned);
            if (totalDistance >= options.MinTrajectoryLengthKm * 1000)
            {
                cleanedTrajectories.Add(cleaned);
            }
        }

        return cleanedTrajectories;
    }

    /// <summary>
    /// 轨迹聚合算法
    /// </summary>
    private async Task<LineString> AggregateTrajectories(List<List<GpsPoint>> trajectories, RouteGenerationOptions options)
    {
        var pathPoints = new List<Coordinate>();

        // 将所有轨迹按距离分段
        var segments = DivideIntoSegments(trajectories, options.SegmentLengthMeters);

        foreach (var segment in segments)
        {
            if (segment.Count >= 3) // 至少3个点才能形成有效段
            {
                // 对每段内的GPS点进行聚类，找到中心路径
                var centerPoint = CalculateSegmentCenter(segment, options.ClusteringRadiusMeters);
                pathPoints.Add(new Coordinate(centerPoint.Longitude, centerPoint.Latitude));
            }
        }

        if (pathPoints.Count < 2)
        {
            throw new InvalidOperationException("无法生成有效的路径点");
        }

        return new LineString(pathPoints.ToArray());
    }

    /// <summary>
    /// 将轨迹分段
    /// </summary>
    private List<List<GpsPoint>> DivideIntoSegments(List<List<GpsPoint>> trajectories, double segmentLength)
    {
        var segments = new List<List<GpsPoint>>();
        var allPoints = trajectories.SelectMany(t => t).OrderBy(p => p.Timestamp).ToList();

        if (!allPoints.Any()) return segments;

        var currentSegment = new List<GpsPoint> { allPoints[0] };
        var segmentStartPoint = allPoints[0];

        for (int i = 1; i < allPoints.Count; i++)
        {
            var point = allPoints[i];
            var distanceFromStart = CalculateDistance(
                segmentStartPoint.Latitude, segmentStartPoint.Longitude,
                point.Latitude, point.Longitude);

            if (distanceFromStart >= segmentLength)
            {
                // 开始新的段
                if (currentSegment.Count > 0)
                {
                    segments.Add(currentSegment);
                }
                currentSegment = new List<GpsPoint> { point };
                segmentStartPoint = point;
            }
            else
            {
                currentSegment.Add(point);
            }
        }

        // 添加最后一段
        if (currentSegment.Count > 0)
        {
            segments.Add(currentSegment);
        }

        return segments;
    }

    /// <summary>
    /// 计算段中心点
    /// </summary>
    private GpsPoint CalculateSegmentCenter(List<GpsPoint> segment, double clusteringRadius)
    {
        // 使用加权平均计算中心点
        var totalWeight = 0.0;
        var weightedLat = 0.0;
        var weightedLon = 0.0;

        foreach (var point in segment)
        {
            // 权重可以基于GPS精度、速度等因素
            var weight = 1.0; // 简化实现，使用相等权重
            
            totalWeight += weight;
            weightedLat += point.Latitude * weight;
            weightedLon += point.Longitude * weight;
        }

        return new GpsPoint
        {
            Latitude = weightedLat / totalWeight,
            Longitude = weightedLon / totalWeight,
            Timestamp = segment.OrderBy(p => p.Timestamp).Skip(segment.Count / 2).First().Timestamp
        };
    }

    /// <summary>
    /// 路径平滑处理
    /// </summary>
    private LineString SmoothPath(LineString path)
    {
        if (path.Coordinates.Length <= 2) return path;

        var smoothedCoords = new List<Coordinate>();
        var coords = path.Coordinates;

        // 保留起点
        smoothedCoords.Add(coords[0]);

        // 对中间点进行平滑处理
        for (int i = 1; i < coords.Length - 1; i++)
        {
            var prev = coords[i - 1];
            var current = coords[i];
            var next = coords[i + 1];

            // 使用简单的移动平均进行平滑
            var smoothedX = (prev.X + current.X + next.X) / 3;
            var smoothedY = (prev.Y + current.Y + next.Y) / 3;

            smoothedCoords.Add(new Coordinate(smoothedX, smoothedY));
        }

        // 保留终点
        smoothedCoords.Add(coords[coords.Length - 1]);

        return new LineString(smoothedCoords.ToArray());
    }

    /// <summary>
    /// 与站点位置对齐
    /// </summary>
    private async Task<LineString> AlignWithStops(LineString path, int lineId, int direction)
    {
        var lineWithStops = await _lineRepository.GetWithStopsAsync(lineId);
        if (lineWithStops?.LineStops == null) return path;

        var stops = lineWithStops.LineStops
            .OrderBy(ls => ls.SequenceNumber)
            .Select(ls => ls.Stop)
            .ToList();

        var alignedCoords = new List<Coordinate>();
        var pathCoords = path.Coordinates.ToList();

        foreach (var stop in stops)
        {
            // 找到路径上离站点最近的点
            var nearestPointIndex = FindNearestPointOnPath(pathCoords, stop.Latitude, stop.Longitude);
            
            // 在该位置插入站点坐标（可选）
            // 这里可以根据需要决定是否强制路径经过站点
        }

        return new LineString(pathCoords.ToArray());
    }

    /// <summary>
    /// 辅助方法：计算两点间距离
    /// </summary>
    private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371000; // 地球半径（米）
        var dLat = (lat2 - lat1) * Math.PI / 180;
        var dLon = (lon2 - lon1) * Math.PI / 180;
        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }
}

/// <summary>
/// GPS点数据模型
/// </summary>
public class GpsPoint
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public DateTime Timestamp { get; set; }
    public double? Speed { get; set; }
    public double? Direction { get; set; }
}
```

这个实现提供了完整的路径生成算法，包括数据获取、清洗、聚合、平滑和对齐等步骤。通过这个方案，可以有效利用现有的GPS轨迹数据生成高质量的线路路径，为预测服务提供更准确的距离计算基础。
